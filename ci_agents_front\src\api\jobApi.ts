import { http } from "@/utils/http";
import axios from "axios";

// API基础响应接口
export interface BaseResponse<T> {
  code: {
    code: string;
    msg: string;
    msgId: string;
  };
  data: T;
  other: null;
}

// Jenkins任务接口
export interface JenkinsJob {
  env_id: string;
  task_id: string;
  log_timestamp: string;
  version_branch: string;
  full_path: string;
  version_test_result: string;
  test_domain: string;
  test_uc_tag: string;
  test_work_dir: string;
  test_testcase_dir: string;
  jenkins_log_name: string;
  jenkins_job_name: string;
  jenkins_build_number: string;
  timestamp: string;
  service_type: string;
  current_state: string;
  fail_reason: string;
}

// 扩展的Jenkins任务接口，包含UI状态
export interface JenkinsJobWithStatus extends JenkinsJob {
  loadingAction: boolean;
  latestAction: string | null;
  loadingStatus: boolean;
  latestStatus: string | null;
  loadingPrincipal: boolean;
  principalName: string | null;
  statusRadio?: string; // 添加可选的 statusRadio 属性
}

// Agents日志接口
export interface AgentsLog {
  service_type: string;
  operation: string;
  status: string;
  timestamp: string;
  details: {
    result?: string;
    [key: string]: any; // 用于支持其他可能的字段
  };
  task_id: string;
  env_id: string;
  service_key: string;
}

const API_BASE_URL = "http://***********:3303";

// API方法
export default {
  /**
   * 获取所有Jenkins任务信息
   * @param deduplicateByTaskId 是否按任务ID去重，默认为false
   * @returns Promise<BaseResponse<JenkinsJob[]>>
   */
  getAllJobs(
    deduplicateByTaskId: boolean = false
  ): Promise<BaseResponse<JenkinsJob[]>> {
    return http.request<BaseResponse<JenkinsJob[]>>(
      "get",
      `${API_BASE_URL}/jenkins/jobs?deduplicate_by_task_id=${deduplicateByTaskId}`
    );
  },

  /**
   * 根据taskId获取agents日志信息
   * @param task_id 任务ID
   * @returns Promise<BaseResponse<AgentsLog[]>>
   */
  getAgentsLogbyId(task_id: string): Promise<BaseResponse<AgentsLog[]>> {
    return http.request<BaseResponse<AgentsLog[]>>(
      "get",
      `${API_BASE_URL}/agents_log/exact?task_id=${task_id}`,
      {},
      {
        timeout: 20000 // 为日志获取请求单独设置20秒超时
      }
    );
  },

  /**
   * 获取所有任务ID及其日志信息
   * @returns Promise<BaseResponse<any[]>> 任务ID及其日志数据
   */
  getAllTaskLogs(): Promise<BaseResponse<any[]>> {
    return http.request<BaseResponse<any[]>>(
      "get",
      `${API_BASE_URL}/jenkins/task_ids`,
      {},
      {
        timeout: 30000 // 为请求设置30秒超时
      }
    );
  },

  /**
   * 获取任务详细日志
   * @param taskId 任务ID
   * @returns Promise<BaseResponse<any>> 任务日志数据
   */
  getTaskLogs(taskId: string): Promise<BaseResponse<any>> {
    return http.request<BaseResponse<any>>(
      "get",
      `http://***********:3303/jenkins/task_logs/${taskId}`,
      {},
      {
        timeout: 30000 // 为日志获取请求设置30秒超时
      }
    );
  },

  /**
   * 获取单个任务详情
   * @param taskId 任务ID
   * @returns Promise<{success: boolean, data: any, message: string}>
   */
  async getJobById(
    taskId: string
  ): Promise<{ success: boolean; data: any; message: string }> {
    const response = await axios.get(
      `${API_BASE_URL}/jenkins/jobs/task/${taskId}`
    );
    if (response.data.code.code === "0000") {
      return {
        success: true,
        data: response.data.data,
        message: response.data.code.msg
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.code.msg
      };
    }
  },

  /**
   * 查询测试用例日志分析数据
   * @param taskId 任务ID
   * @param pageSize 每页数量，默认20
   * @returns Promise<{success: boolean, data: any, message: string}>
   */
  async queryTestcaseLogAnalysis(
    taskId: string,
    pageSize = 20
  ): Promise<{ success: boolean; data: any; message: string }> {
    const response = await axios.post(
      `http://***********:3303/biz/ci/v1/testcase/log/analysis/query`,
      {
        task_id: taskId,
        pageSize: pageSize
      }
    );
    if (response.data.result) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.failReason || "查询成功"
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.failReason || "查询失败"
      };
    }
  },

  // 更新日志分析的手动分类和分析结果
  async updateTestcaseLogAnalysis(params) {
    const response = await axios.post(
      `http://***********:3303/biz/ci/v1/testcase/log/analysis/manual`,
      {
        task_id: params.taskId,
        testcase_name: params.testcaseName,
        manualClassification: params.classification,
        manualAnalysisResult: params.analysisResult
      }
    );
    if (response.data.result) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.failReason || "更新成功"
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.failReason || "更新失败"
      };
    }
  },

  // 更新任务状态
  async updateTaskState(taskId, currentState) {
    const response = await axios.put(
      `${API_BASE_URL}/jenkins/jobs/task/${taskId}/state`,
      { current_state: currentState }
    );
    if (response.data.code.code === "0000") {
      return {
        success: true,
        data: response.data.data,
        message: response.data.code.msg
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.code.msg
      };
    }
  },

  // 根据env_id查询principal信息
  async getEnvPrincipal(envId) {
    const response = await axios.get(
      `${API_BASE_URL}/env_check/env_info/principal`,
      {
        params: {
          env_id: envId
        }
      }
    );
    if (response.data.result) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.failReason || "查询成功"
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.failReason || "查询失败"
      };
    }
  },

  /**
   * 批量获取环境负责人信息
   * @param envIds 环境ID数组
   * @returns Promise<{success: boolean, data: any, message: string}>
   */
  async getBatchEnvPrincipal(
    envIds: string[]
  ): Promise<{ success: boolean; data: any; message: string }> {
    const response = await axios.post(
      `${API_BASE_URL}/env_check/env_info/batch_principal`,
      envIds
    );

    if (response.data.result) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.failReason || "查询成功"
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.failReason || "查询失败"
      };
    }
  },

  /**
   * 更新任务失败原因
   * @param taskId 任务ID
   * @param failReason 失败原因
   * @param manualProcessingHours 人工处理时长（小时），默认为0
   * @returns Promise<{success: boolean, data: any, message: string}>
   */
  async updateTaskFailReason(
    taskId: string,
    failReason: string,
    manualProcessingHours: number = 0
  ): Promise<{ success: boolean; data: any; message: string }> {
    const response = await axios.put(
      `${API_BASE_URL}/jenkins/jobs/task/${taskId}/fail_reason`,
      {
        fail_reason: failReason,
        manual_processing_hours: manualProcessingHours
      }
    );
    if (response.data.code.code === "0000") {
      return {
        success: true,
        data: response.data.data,
        message: response.data.code.msg
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.code.msg
      };
    }
  },

  /**
   * 获取按天统计的失败数据
   * @param startTime 可选的开始时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
   * @param endTime 可选的结束时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
   * @returns Promise<{success: boolean, data: any, message: string}> 失败统计数据
   */
  async getFailureStatsByDay(
    startTime?: string,
    endTime?: string
  ): Promise<{ success: boolean; data: any; message: string }> {
    const url = `http://***********:3303/jenkins/failure_stats_by_day`;

    // 添加可选的查询参数
    const params: Record<string, string> = {};
    if (startTime) params.start_time = startTime;
    if (endTime) params.end_time = endTime;

    const response = await axios.get(url, { params });

    if (response.data.code.code === "0000") {
      return {
        success: true,
        data: response.data.data,
        message: response.data.code.msg
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.code.msg
      };
    }
  },
  /**
   * 获取业务流程详细信息（按业务流程类型聚合）
   * @param date 日期 YYYY-MM-DD
   * @param processType 业务流程类型，可选，不指定则返回所有流程类型
   * @param startDate 开始日期 YYYY-MM-DD，可选
   * @param endDate 结束日期 YYYY-MM-DD，可选
   * @param envId 环境ID过滤，可选
   * @returns Promise<{success: boolean, data: any, message: string}> 业务流程详细信息
   */
  async getBusinessProcessDetail(
    date: string,
    processType?: string,
    startDate?: string,
    endDate?: string,
    envId?: string
  ): Promise<{ success: boolean; data: any; message: string }> {
    const url = `${API_BASE_URL}/business_process_stats/detail`;

    // 添加查询参数
    const params: Record<string, string> = {
      date: date
    };
    if (processType) params.process_type = processType;
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    if (envId) params.env_id = envId;

    const response = await axios.get(url, { params });

    if (response.data.code.code === "0000") {
      return {
        success: true,
        data: response.data.data,
        message: response.data.code.msg
      };
    } else {
      return {
        success: false,
        data: null,
        message: response.data.code.msg
      };
    }
  }
};
