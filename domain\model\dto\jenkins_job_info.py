import uuid
from pydantic import BaseModel, Field, model_validator
from datetime import datetime


class JenkinsJobInfo(BaseModel):
    env_id: str = Field(..., example="RAN3-T17-10466")
    task_id: str = None
    log_timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    version_branch: str = ""
    full_path: str
    version_test_result: str
    test_domain: str = Field("")
    test_uc_tag: str
    test_work_dir: str
    test_testcase_dir: str
    jenkins_log_name: str
    jenkins_job_name: str
    jenkins_build_number: str
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    service_type: str = Field("")
    current_state: str = ""
    manual_processing_hours: str = "0.0"

    @model_validator(mode='before')
    def set_task_id_if_empty(cls, values):
        if not values.get('task_id'):
            values['task_id'] = str(uuid.uuid4()).replace("-", "")[:32]
        return values
