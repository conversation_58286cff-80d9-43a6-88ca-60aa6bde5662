# -*- encoding: utf-8 -*-
"""
@Time    :
<AUTHOR>   wen zhang 10234064
"""
import json
import os.path
import re

import requests

from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_exception

BASE_URL = "https://wxbee.zte.com.cn"
UPLOAD_TO_FOLDER = BASE_URL + "/log/uploadData2Folder"
ADD_FOLDER = BASE_URL + "/folder/addFolder"
DOWNLOAD_DATA = BASE_URL + "/log/downloadData"
FILES_URL = "https://wxbee.zte.com.cn/index.html?taskId={}&"
# wxbee对上传的文件名有特殊符号限制，所以默认替换成下划线，这里定义需要替换的特殊字符
SPECIAL_CHARS = "*\\\"“”<>|?\\\\/:&-@#￥().^!"
# 创建一个翻译表，将特殊字符映射到下划线
TRANS_TABLE = str.maketrans(SPECIAL_CHARS, "_" * len(SPECIAL_CHARS))


@retries_on_exception(3, everyTryDelaySecs=30, exceptions=requests.exceptions.ConnectionError)
def post_request(url: str, data: dict = None, body: dict = None, headers: dict = None, files=None,
                 savePath: str = None):
    try:
        response = requests.post(url, headers=headers, data=data, json=body, files=files, timeout=60)
        if response.status_code != 200:
            raise Exception(f"向WxBee发送请求异常: {response.text}")
        if savePath is not None:
            with open(savePath, 'wb') as f:
                f.write(response.content)
                logger.info(f"WxBee文件:{savePath}下载成功")
            return {"filePath": savePath}
        return json.loads(response.text)
    except Exception as e:
        logger.error(e)
        raise Exception(e)


def _check_wxbee_save_path(absPath: str):
    if '/' not in absPath or absPath.startswith('/') is not True:
        raise Exception(f"WxBee文件保存路径 {absPath} 不符合规范，应该以/开头，并且至少包含一个/符号")


def _format_file_name(fileName: str):
    st = os.path.splitext(fileName)
    return st[0].translate(TRANS_TABLE) + st[1]


class WxBee:

    def __init__(self, paras: dict):
        self._userID = paras.get("userID", "")
        self._taskId = paras.get("taskId", "")
        self._system = paras.get("system", "")
        self._systemCode = paras.get("systemCode", "")

    def upload_to_folder(self, savePath: str, filePath: str, uploadName: str = None):
        if uploadName is None:
            uploadName = os.path.basename(filePath)
        uploadName = _format_file_name(uploadName)
        savePath = self._add_folder(os.path.dirname(savePath), os.path.basename(savePath))
        files = {"files": (uploadName, open(filePath, 'rb'))}
        body = {"taskName": self._taskId, "taskId": self._taskId, "userID": self._userID, "dir": savePath,
                "system": self._system, "systemCode": self._systemCode}
        logger.info(f"Wxbee request body:{body}")
        response = post_request(UPLOAD_TO_FOLDER, data=body, files=files)
        if response.get("status") == "success":
            logger.info(
                f'文件已上传至WxBee,任务ID:{self._taskId},保存路径:{savePath}/{uploadName})，可通过{FILES_URL.format(self._taskId)}查看！')
            return f"{savePath}/{uploadName}"
        raise Exception(response.get("message"))

    def download_file(self, fileName: str, savePath: str):
        body = {"taskId": self._taskId, "userID": self._userID, "fileName": fileName,
                "system": self._system, "systemCode": self._systemCode}
        response = post_request(DOWNLOAD_DATA, body=body, savePath=os.path.join(savePath, fileName))
        return response.get("filePath")

    def _add_folder(self, absPath: str, folderName: str):
        _check_wxbee_save_path(absPath)
        if absPath == '//':
            absPath = '/'
        if absPath != '/':
            absPath = self._add_folder(os.path.dirname(absPath), os.path.basename(absPath))
        folderName = folderName.translate(TRANS_TABLE)
        url = f"{ADD_FOLDER}?taskId={self._taskId}&name={folderName}&dirIds={absPath}"
        response = post_request(url)
        if (response.get("code") == 200 and "添加成功" in response.get("msg")) or (
                response.get("code") == 201 and "相同的文件名已存在" in response.get("msg")):
            return re.sub(r"\\", '/', os.path.join(absPath, folderName))
        if response.get("code") == 99999:
            raise Exception(f"添加文件夹失败，err:{response.get('message')}")
        raise Exception(f"添加文件夹失败，err:{response.get('msg')}{response.get('reason')}")


if __name__ == '__main__':
    print('debug')
    # _check_wxbee_save_path('/')
    # _check_wxbee_save_path('test1111/test2222')
    # _check_wxbee_save_path('/test1111/test2222')
    # # 定义需要替换的特殊字符
    # special_chars = "*\\\"“”<>|?\\\\/:&-@#￥().^"
    # # 创建一个翻译表，将特殊字符映射到下划线
    # translation_table = str.maketrans(special_chars, "_" * len(special_chars))
    # # 定义原始字符串
    # original_string = "*.^World!.py"
    # # 使用 translate 方法替换特殊字符
    # processed_string = _format_file_name(original_string)
    # # 输出结果
    # print(processed_string)
