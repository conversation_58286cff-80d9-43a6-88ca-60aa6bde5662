from service.tools.ume_state_service import *
from service.tools.ue_state_service import *
from service.tools.pc_state_service import *
from domain.model.dto.logdto import LogJobInfo
from domain.repository.es_env import EsDeviceEnv
from domain.repository.es_env import EsBizCoreEnv
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from infrastructure.email.email import Email
from service.logging_service import LoggingService

ToolMap = {
    "升级用例失败": ["query_me_connect"],
    "接入用例失败": ["query_ume_alarm", "query_ue_com", "query_pc_net", "query_ue_ipconfig"],
    "ping包用例失败": ["query_ume_alarm", "query_ue_com", "query_pc_net", "query_ue_ipconfig"],
    "灌包流量为0": ["query_ume_alarm", "query_ue_com", "query_pc_net", "query_ue_ipconfig"],
    "灌包流量不足": ["query_ume_alarm"],
    "default": ["query_ue_com", "query_ue_ipconfig", "query_pc_net", "query_ume_alarm", "query_me_connect"],
    "debug": ["query_ue_com", "query_ue_ipconfig", "query_pc_net"]
}


class EnvMonitorService:
    def __init__(self, service_msg_dict):
        self.document = service_msg_dict
        self._es_biz_core_env = EsBizCoreEnv()
        self._es_device_env = EsDeviceEnv()

    def execute_tool(self, device_info, tool):
        if tool == "query_me_connect":
            return ume_state_service.query_me_connect(device_info)

        elif tool == "query_ume_alarm":
            return ume_state_service.query_ume_alarm(device_info)

        elif tool == "query_ue_ipconfig":
            return ue_state_service.query_ue_ipconfig(device_info)

        elif tool == "query_ue_com":
            return ue_state_service.query_ue_com(device_info)

        elif tool == "query_pc_net":
            return pc_state_service.query_pc_net(device_info)

        elif tool == "query_mts_num":
            return ume_state_service.query_mts_num(device_info)

        elif tool == "update_pc_time":
            return pc_state_service.update_pc_time(device_info)

    def get_env_device_info(self):
        result = self._es_device_env.query_by_filter_without_sort({"env_id": self.document.get("env_id")})
        device_info_list = result[1]
        # if device_info_list:
        #     device_info = device_info_list[0]
        # else:
        #     raise Exception("没有环境Device信息")
        # return device_info
        if not device_info_list:
            raise Exception("没有环境Device信息")
        return device_info_list

    async def run(self):
        pass_dict = {}
        fail_dict = {}
        fail_detail = self.document["log_analysis_results"]

        device_info_list = self.get_env_device_info()
        for device_info in device_info_list:
            for fail in fail_detail:
                for tool in ToolMap.get(fail.get("result", "default"), ToolMap.get("default")):
                    result = self.execute_tool(device_info, tool)
                    pass_dict.update({tool: result.get("result")})
                    print(f"当前检测项：{tool},结果为：{result}")
                    if not result.get("result"):
                        # fail_dict.update({tool: result.get("result")})
                        fail_dict.update({tool + "_details": result.get("details", {})})

                # 如果fail_dict不为空，则邮件通知，抛出异常，终止流程
                if len(fail_dict) != 0:
                    # 发送邮件通知
                    error_message = f"\n环境ID: {self.document.get('env_id')}\n" \
                                   f"异常项: {fail_dict}" 
                    email = Email(
                        subject=f"环境检测异常通知",
                        content=error_message,
                        recipients=["<EMAIL>"],
                        cc=["<EMAIL>"]
                    )
                    email.send_email()
                    LoggingService.log_execution(
                        service_type="log_save",
                        operation="log_save",
                        status="failure",
                        details={"message": self.document, "results": fail_dict},
                        task_id=self.document.get("task_id"),
                        env_id=self.document.get("env_id"),
                        service_key="env_check"
                    )
                    # 抛出异常
                    raise Exception(error_message)

        LoggingService.log_execution(
            service_type="log_save",
            operation="log_save",
            status="success",
            details={"message": self.document, "results": pass_dict},
            task_id=self.document.get("task_id"),
            env_id=self.document.get("env_id"),
            service_key="env_check"
        )

        # 塞消息队列，走复测流程
        msg = LogJobInfo(
            log_name=self.document.get("log_name"),
            job_name=self.document.get("job_name"),
            build_number=self.document.get("build_number"),
            env_id=self.document.get("env_id"),
            task_id=self.document.get("task_id"),
            service_type="online_test",
            subtype=self.document.get("subtype","normal")
        )
        from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
        await CiAgentsQueue().push_info(msg)

if __name__ == "__main__":
    from infrastructure.utils.Env import Env
    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)
    document = {"env_id": "RAN3-上海高频CI团队-VAT1014"}
    EnvMonitorService(document).get_env_device_info()
