import warnings
import paramiko
import re
warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)


class PcInfoService(object):
    @staticmethod
    def create_ssh_client(ip, username, password):
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=ip,
            username=username,
            password=password,
            timeout=10
        )
        return ssh

    @staticmethod
    def check_net_state(data):
        try:
            pc_ip = data.get("pc_ip")
            pc_username = data.get("pc_username")
            pc_password = data.get("pc_password")
            pc_net = data.get("pc_net")
            ssh = PcInfoService.create_ssh_client(pc_ip, pc_username, pc_password)  # 使用提取的函数
            # 执行命令
            netsh_command = 'netsh interface show interface'
            stdin, stdout, stderr = ssh.exec_command(netsh_command)
            output = stdout.read()

            encodings = ['gbk', 'utf-8', 'cp936', 'ascii']
            netsh_output = ""
            
            for encoding in encodings:
                try:
                    netsh_output = output.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue

            prefix = "已启用            已连接            专用               "
            formatted_net = [f"{prefix}{pcnet}" for pcnet in pc_net]
            missing_nets = [net for net in formatted_net if net not in netsh_output]

            all_present = all(pcnet in netsh_output for pcnet in formatted_net)
            if all_present:
                flag = True
            else:
                flag = False

            # 获取命令输出
            error = stderr.read().decode('gbk')
            if error:
                print(f"命令执行错误: {error}")
            # 关闭连接
            ssh.close()
            print(netsh_output)
            return {"result": flag, "details": {"missing_networks": missing_nets}}
        except paramiko.AuthenticationException:
            print("认证失败：请检查用户名和密码")
            return {"result": False, "details": {"error": "认证失败：请检查用户名和密码"}}
        except paramiko.SSHException as ssh_exception:
            print(f"SSH连接错误: {str(ssh_exception)}")
            return {"result": False, "details": {"error": f"SSH连接错误: {str(ssh_exception)}"}}
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return {"result": False, "details": {"error": f"发生错误: {str(e)}"}}

    @staticmethod
    def update_pc_time(data):
        try:
            name = data.get("name")
            ip = data.get("ip")
            username = data.get("username")
            password = data.get("password")
            print(f"正在同步{name}的系统时间")
            ssh = PcInfoService.create_ssh_client(ip, username, password)  # 使用提取的函数
            # 执行命令
            admin_command = 'w32tm /resync'
            stdin, stdout, stderr = ssh.exec_command(admin_command)
            # 获取命令输出
            error = stderr.read().decode('gbk')
            # 关闭连接
            ssh.close()
            if error:
                print(f"命令执行错误: {error}")
                return False
            return True
        except paramiko.AuthenticationException:
            print("认证失败：请检查用户名和密码")
            return None
        except paramiko.SSHException as ssh_exception:
            print(f"SSH连接错误: {str(ssh_exception)}")
            return None
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return None