from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class TimeRange(BaseModel):
    """时间范围"""
    start_date: str = Field(..., description="开始日期 YYYY-MM-DD")
    end_date: str = Field(..., description="结束日期 YYYY-MM-DD")


class EnvGroupStats(BaseModel):
    """环境分组统计"""
    env_id: str = Field(..., description="环境ID")
    total_jobs: int = Field(default=0, description="总任务数")
    total_manual_hours: float = Field(default=0.0, description="总人工处理时间(小时)")
    unique_task_count: int = Field(default=0, description="唯一任务数")


class PrincipalGroupStats(BaseModel):
    """负责人分组统计"""
    principal: str = Field(..., description="环境维护负责人")
    total_jobs: int = Field(default=0, description="总任务数")
    total_manual_hours: float = Field(default=0.0, description="总人工处理时间(小时)")
    unique_task_count: int = Field(default=0, description="唯一任务数")
    env_ids: List[str] = Field(default_factory=list, description="相关环境ID列表")


class TaskIdGroupStats(BaseModel):
    """任务ID分组统计"""
    task_id: str = Field(..., description="任务ID")
    total_jobs: int = Field(default=0, description="总任务数")
    total_manual_hours: float = Field(default=0.0, description="总人工处理时间(小时)")
    env_id: str = Field(default="", description="环境ID")
    principal: str = Field(default="", description="环境维护负责人")


class JenkinsJobAggregationResponse(BaseModel):
    """Jenkins任务聚合响应"""
    total_jobs: int = Field(default=0, description="总任务数")
    total_manual_hours: float = Field(default=0.0, description="总人工处理时间(小时)")
    unique_task_count: int = Field(default=0, description="唯一任务数")
    time_range: TimeRange = Field(..., description="查询时间范围")
    env_groups: Optional[List[EnvGroupStats]] = Field(default=None, description="环境分组统计(可选)")
    principal_groups: Optional[List[PrincipalGroupStats]] = Field(default=None, description="负责人分组统计(可选)")
    task_id_groups: Optional[List[TaskIdGroupStats]] = Field(default=None, description="任务ID分组统计(可选)")


class JenkinsJobAggregationRequest(BaseModel):
    """Jenkins任务聚合请求"""
    start_date: str = Field(..., description="开始日期 YYYY-MM-DD", example="2024-01-01")
    end_date: str = Field(..., description="结束日期 YYYY-MM-DD", example="2024-01-31")
    env_id: Optional[str] = Field(default=None, description="环境ID过滤(可选)")
    principal: Optional[str] = Field(default=None, description="环境维护负责人过滤(可选)")
    task_id: Optional[str] = Field(default=None, description="任务ID过滤(可选)")
    group_by_env: bool = Field(default=False, description="是否按环境分组")
    group_by_principal: bool = Field(default=False, description="是否按负责人分组")
    group_by_task_id: bool = Field(default=False, description="是否按任务ID分组")
