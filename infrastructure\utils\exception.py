from six.moves import http_client
from locales import MsgId
from infrastructure.utils.HttpCodeEnum import HttpCodeEnum, HttpMessageIdEnum


class FrameworkException(Exception):
    """Base FrameworkException

    To correctly use this class, inherit from it and define
    a 'message' property. That message will get printf'd
    with the keyword arguments provided to the constructor.
    """

    @property
    def error_message(self):
        return MsgId.ERR_UNKNOWN

    @property
    def code(self):
        return http_client.INTERNAL_SERVER_ERROR

    @property
    def headers(self):
        return {}

    @property
    def data(self):
        return {}

    def __init__(self, message=None, data=None, *args, **kwargs):
        self.message = message if message else self.error_message
        self.bo = data if data is not None else self.data


class Invalid(FrameworkException):

    @property
    def code(self):
        return http_client.BAD_REQUEST

    @property
    def error_message(self):
        return MsgId.ERR_PARA_UNACCEPT


class TdlException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.system_failed.value

    @property
    def error_message(self):
        return MsgId.TDL_ERR_BASE

    def __init__(self, message=None, data=None, *args, **kwargs):
        err_msg = self.error_message if not message else message
        super(TdlException, self).__init__(err_msg, data, *args, **kwargs)


class SystemFailedException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.system_failed.value

    @property
    def error_message(self):
        return HttpMessageIdEnum.system_failed.value


class AuthFailedException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.auth_failed.value

    @property
    def error_message(self):
        return HttpMessageIdEnum.auth_failed.value


class BusinessFailedException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.business_failed.value

    @property
    def error_message(self):
        return HttpMessageIdEnum.business_failed.value

    def __init__(self, message=None, data=None):
        err_msg = self.error_message if not message else message
        super(BusinessFailedException, self).__init__(err_msg, data=data)


class ValidatedFailedException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.validated_failed.value

    @property
    def error_message(self):
        return HttpMessageIdEnum.validated_failed.value

    def __init__(self, message=None, data=None):
        err_msg = self.error_message if not message else message
        super(ValidatedFailedException, self).__init__(err_msg, data=data)


class ServerFailedException(FrameworkException):

    @property
    def code(self):
        return HttpCodeEnum.server_failed.value

    @property
    def error_message(self):
        return HttpMessageIdEnum.server_failed.value


class MongoDBException(FrameworkException):

    @property
    def error_message(self):
        return MsgId.MONGO_ERR_BASE

    @property
    def code(self):
        return HttpCodeEnum.server_failed.value

    def __init__(self, message=None, data=None):
        err_msg = self.error_message if not message else message
        super(MongoDBException, self).__init__(err_msg, data=data)


class RedisException(FrameworkException):

    @property
    def error_message(self):
        return MsgId.REDIS_BASE_ERR

    @property
    def code(self):
        return HttpCodeEnum.business_failed.value

    def __init__(self, message=None, data=None, *args, **kwargs):
        err_msg = self.error_message if not message else message
        super(RedisException, self).__init__(err_msg, data, *args, **kwargs)


if __name__ == '__main__':
    try:
        raise TdlException()
    except FrameworkException as e:
        print(e.code, e.message, e.bo)
