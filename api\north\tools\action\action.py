from fastapi import APIRouter, Depends
from api.north.tools.action.schemas import ActionExecuteInfo, ActionExecuteLogs
from service.tools.ActionService import ActionService

router = APIRouter(prefix="/action")

@router.get("/get_execute_info")
async def get_execute_info(data: ActionExecuteInfo = Depends(ActionExecuteInfo)):
    return ActionService.get_execution_info(data.model_dump())

@router.get("/get_execute_logs")
async def get_execute_logs(data: ActionExecuteLogs = Depends(ActionExecuteLogs)):
    return ActionService.get_execution_logs(data.model_dump())