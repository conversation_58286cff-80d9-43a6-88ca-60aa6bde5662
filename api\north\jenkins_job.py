from fastapi import APIRouter, HTTPException, Query, Path, Body, BackgroundTasks
from api.routers.tdl_api import TdlApi
from domain.model.response.response import Response, ExceptionEnum
from service.jenkins_job_service import JenkinsJobService
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from domain.repository.es_jenkins_job import EsJenkinsJob
import logging
from typing import Optional
from datetime import datetime
from domain.model.dto.independence_rollback_info import IndependenceRollbackInfo, TaskIndependenceRollbackInfo
from service.run_service.independence_rollback_service import IndependenceRollbackService, TaskIndependenceRollbackService
from service.measurement_report_service import MeasurementReportService
from service.agent_log_report_service import AgentLogReportService
from service.agent_log_aggregation_service import AgentLogAggregationService

router = APIRouter(prefix="/jenkins")


@router.get("/env_config")
async def get_historical_config(env_id: str = Query(..., description="环境ID")):
    """
    根据传入的 envId 查询历史配置数据
    """
    if not env_id or env_id.strip() == "":
        raise HTTPException(status_code=400, detail="envId 参数不能为空")

    # 这里模拟数据查询，可以替换成实际的业务逻辑或数据库查询
    return TdlApi().query_tdl_env_config(env_id)


@router.post("/save_jenkins_job")
async def save_jenkins_job(document: JenkinsJobInfo):
    """
    保存jenkins任务信息
    """
    try:
        result = await JenkinsJobService(document).run()
        return Response.build(result)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="jenkins任务信息保存失败")


@router.get("/jobs", response_model=Response)
async def get_jenkins_jobs(
        env_id: Optional[str] = Query(None, description="环境ID，可选过滤条件"),
        job_name: Optional[str] = Query(None, description="Jenkins任务名称，可选过滤条件"),
        test_result: Optional[str] = Query(None, description="测试结果，可选过滤条件，True或False"),
        deduplicate_by_task_id: bool = Query(False, description="是否对相同task_id只保留时间最早的记录")
):
    """
    获取Jenkins任务列表，支持按环境ID、任务名称和测试结果过滤

    当deduplicate_by_task_id=True时，对于相同的task_id只保留时间最早的一条记录
    """
    try:
        filter_dict = {}
        if env_id:
            filter_dict["env_id"] = env_id
        if job_name:
            filter_dict["jenkins_job_name"] = job_name
        if test_result:
            filter_dict["version_test_result"] = test_result

        # 修改这里，增加size参数为10000，确保返回更多结果
        result = EsJenkinsJob().query_by_filter_sort(filter_dict, "timestamp", size=10000)

        # 获取查询结果
        jobs = result[1]

        # 如果需要去重，对相同task_id只保留时间最早的记录
        if deduplicate_by_task_id and jobs:
            # 创建一个字典，用于存储每个task_id对应的最早记录
            task_id_earliest_jobs = {}

            for job in jobs:
                task_id = job.get("task_id")
                if not task_id:
                    continue

                timestamp = job.get("timestamp", "")

                # 如果该task_id尚未记录或当前记录时间更早，则更新
                if (task_id not in task_id_earliest_jobs or
                        timestamp < task_id_earliest_jobs[task_id].get("timestamp", "")):
                    task_id_earliest_jobs[task_id] = job

            # 将字典值转换为列表作为结果
            deduplicated_jobs = list(task_id_earliest_jobs.values())

            # 添加日志，帮助调试
            logging.info(f"查询条件: {filter_dict}, 原始结果数量: {len(jobs)}, 去重后结果数量: {len(deduplicated_jobs)}")
            return Response.build(deduplicated_jobs)

        # 添加日志，帮助调试
        logging.info(f"查询条件: {filter_dict}, 返回结果数量: {len(jobs)}")
        return Response.build(jobs)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取Jenkins任务列表失败")

@router.get("/jobs/task/{task_id}", response_model=Response)
async def get_jenkins_jobs_by_task_id(task_id: str = Path(..., description="任务ID")):
    """
    根据TASK_ID获取Jenkins任务列表
    """
    try:
        filter_dict = {"task_id": task_id}
        result = EsJenkinsJob().query_by_filter_sort(filter_dict, "timestamp")

        if not result[1]:  # 检查结果是否为空
            return Response.from_exception(ExceptionEnum.NOT_FOUND, msg="未找到指定任务ID的Jenkins任务")

        return Response.build(result[1])
    except Exception as e:
        logging.error(f"Error retrieving jenkins jobs for task ID {task_id}: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取Jenkins任务列表失败")

@router.put("/jobs/task/{task_id}/state")
async def update_jenkins_job_state(
    task_id: str = Path(..., description="任务ID"),
    current_state: str = Body(..., embed=True, description="新的状态值")
):
    """
    根据任务ID更新Jenkins任务的状态
    """
    try:
        # 使用 query_by_filter 查询任务
        total, jobs = EsJenkinsJob().query_by_filter({"task_id": task_id})

        if total == 0 or not jobs:
            return Response.from_exception(
                ExceptionEnum.NOT_FOUND,
                msg=f"未找到任务ID为 {task_id} 的Jenkins任务"
            )

        # 直接使用 update_by_task_id 进行更新
        update_result = EsJenkinsJob().update_by_task_id(
            task_id=task_id,
            timestamp=jobs[-1].get("timestamp"),
            filter={
                "current_state": current_state,
            }
        )

        if not update_result:
            return Response.from_exception(
                ExceptionEnum.SERVER_FAILED,
                msg="更新状态失败"
            )

        return Response.build({
            "message": "状态更新成功",
            "task_id": task_id,
            "current_state": current_state
        })

    except Exception as e:
        logging.error(f"更新任务 {task_id} 状态时发生错误: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"更新Jenkins任务状态失败: {str(e)}"
        )

@router.put("/jobs/task/{task_id}/fail_reason")
async def update_jenkins_job_fail_reason(
    task_id: str = Path(..., description="任务ID"),
    fail_reason: str = Body(..., embed=True, description="失败原因"),
    manual_processing_hours: float = Body(0.0, embed=True, description="人工处理时长，单位：小时H，默认为0")
):
    """
    根据任务ID更新Jenkins任务的失败原因和人工处理时长

    更新规则：
    - 对于同一个task_id存在多条记录的情况，更新时间最早的一条记录
    - 支持设置人工处理时长，默认为0小时
    """
    try:
        # 使用 query_by_filter 查询任务
        total, jobs = EsJenkinsJob().query_by_filter({"task_id": task_id})

        if total == 0 or not jobs:
            return Response.from_exception(
                ExceptionEnum.NOT_FOUND,
                msg=f"未找到任务ID为 {task_id} 的Jenkins任务"
            )

        # 直接使用 update_by_task_id 进行更新
        update_result = EsJenkinsJob().update_by_task_id(
            task_id=task_id,
            timestamp=jobs[-1].get("timestamp"),
            filter={
                "fail_reason": fail_reason,
                "manual_processing_hours": manual_processing_hours
            }
        )

        if not update_result:
            return Response.from_exception(
                ExceptionEnum.SERVER_FAILED,
                msg="更新失败原因失败"
            )

        return Response.build({
            "message": "失败原因更新成功",
            "task_id": task_id,
            "fail_reason": fail_reason,
            "manual_processing_hours": manual_processing_hours
        })

    except Exception as e:
        logging.error(f"更新任务 {task_id} 失败原因时发生错误: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"更新Jenkins任务失败原因失败: {str(e)}"
        )

@router.get("/jobs/{job_id}", response_model=Response)
async def get_jenkins_job(job_id: str = Path(..., description="Jenkins任务ID")):
    """
    根据ID获取Jenkins任务详情
    """
    try:
        result = EsJenkinsJob().get(id=job_id)
        if not result:
            return Response.from_exception(ExceptionEnum.NOT_FOUND, msg="未找到指定的Jenkins任务")
        return Response.build(result)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取Jenkins任务详情失败")


@router.put("/jobs/{job_id}", response_model=Response)
async def update_jenkins_job(job_id: str = Path(..., description="Jenkins任务ID"), update_data: dict = Body(...)):
    """
    更新Jenkins任务信息
    """
    try:
        # 先获取现有数据
        existing_job = EsJenkinsJob().get(id=job_id)
        if not existing_job:
            return Response.from_exception(ExceptionEnum.NOT_FOUND, msg="未找到指定的Jenkins任务")

        # 更新数据
        update_data["timestamp"] = datetime.now().isoformat()
        result = EsJenkinsJob().update(id=job_id, body={"doc": update_data})
        return Response.build(result)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="更新Jenkins任务信息失败")


@router.delete("/jobs/{job_id}", response_model=Response)
async def delete_jenkins_job(job_id: str = Path(..., description="Jenkins任务ID")):
    """
    删除Jenkins任务
    """
    try:
        result = EsJenkinsJob().delete(id=job_id)
        if not result:
            return Response.from_exception(ExceptionEnum.NOT_FOUND, msg="未找到指定的Jenkins任务")
        return Response.build({"message": "删除成功"})
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="删除Jenkins任务失败")


@router.post("/save_independece_rollback_info")
async def save_independece_rollback_info(document: IndependenceRollbackInfo):
    """
    通过job执行单点回溯流程
    """
    env_id = document.env_id
    job_name = document.job_name
    build_number = document.build_number
    try:
        result = await IndependenceRollbackService(env_id, job_name, build_number).execute()
        return Response.build(result)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="单点回溯执行失败")

@router.post("/save_taskid_independece_rollback_info")
async def save_independece_rollback_info(document: TaskIndependenceRollbackInfo):
    """
    通过task_id执行单点回溯流程
    """
    task_id = document.task_id
    try:
        result = await TaskIndependenceRollbackService(task_id).execute()
        return Response.build(result)
    except Exception as e:
        logging.error(e)
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="单点回溯执行失败")


@router.get("/failure_stats_by_day", response_model=Response)
async def get_failure_stats_by_day(
    start_time: Optional[str] = Query(None, description="开始时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)"),
    end_time: Optional[str] = Query(None, description="结束时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)")
):
    """
    获取指定时间范围内每天每个环境的测试失败统计
    
    该接口返回按日期和环境分组的测试失败统计数据，包括：
    - 每天每个环境的测试总数
    - 每天每个环境的首次失败数
    - 每天每个环境的总失败数
    - 整个时间范围的汇总统计
    
    如果不指定时间范围，默认返回最近14天的数据。
    """
    try:
        service = MeasurementReportService()
        result = service.get_failure_counts_by_day(start_time, end_time)
        return Response.build(result)
    except Exception as e:
        logging.error(f"获取失败统计数据失败: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg=f"获取失败统计数据失败: {str(e)}")


@router.get("/agent_logs_report", response_model=Response)
async def get_agent_logs_report(
    limit: int = Query(1000, description="返回记录的最大数量"),
    skip: int = Query(0, description="跳过的记录数量"),
    task_id: Optional[str] = Query(None, description="按任务ID筛选"),
    env_id: Optional[str] = Query(None, description="按环境ID筛选"),
    background_tasks: BackgroundTasks = None
):
    """
    获取Agent日志报告，支持按任务ID或环境ID筛选
    
    返回格式化后的日志报告，包含以下字段：
    - timestamp: 时间戳
    - env_id: 环境ID
    - task_id: 任务ID
    - principal: 负责人
    - version_test_result: 测试结果 (success/failure)
    - version_branch: 版本分支
    - subtype: 子类型 (first/retest/rollback等)
    - version: 版本号
    - duration: 持续时间 (HH:MM:SS格式)
    """
    try:
        # 创建服务实例
        service = AgentLogReportService()
        
        # 根据参数获取日志报告
        if task_id:
            logs = service.get_logs_by_task_id(task_id, limit)
        elif env_id:
            logs = service.get_logs_by_env_id(env_id, limit)
        else:
            logs = service.get_all_logs_report(limit, skip)
        
        # 如果提供了background_tasks，添加一个后台任务记录此次查询
        if background_tasks:
            async def log_query(query_type, query_value=None, result_count=0):
                logging.info(f"Agent日志报告查询: 类型={query_type}, 值={query_value}, 结果数量={result_count}")
            
            query_type = "task_id" if task_id else ("env_id" if env_id else "all")
            query_value = task_id or env_id or "all"
            background_tasks.add_task(log_query, query_type, query_value, len(logs))
        
        return Response.build(logs)
    except Exception as e:
        logging.error(f"获取Agent日志报告失败: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg=f"获取Agent日志报告失败: {str(e)}")


@router.get("/task_ids", response_model=Response)
async def get_all_task_ids(limit: int = Query(10000, description="返回记录的最大数量")):
    """
    获取所有任务ID列表
    
    返回系统中所有任务的唯一ID列表，可用于后续查询特定任务的详细信息
    
    Args:
        limit: 返回记录的最大数量，默认为1000
    
    Returns:
        包含任务ID列表的响应对象
    """
    try:
        # 创建服务实例
        service = AgentLogAggregationService()
        
        # 获取所有任务ID
        task_ids = service.get_all_task_ids(limit=limit)
        result = []
        if task_ids:
            for task_id in task_ids:
                result.append({task_id:service.aggregate_by_task_id(task_id)})
        return Response.build(result)
    except Exception as e:
        logging.error(f"获取任务ID列表失败: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg=f"获取任务ID列表失败: {str(e)}")


@router.get("/task_logs/{task_id}", response_model=Response)
async def get_task_logs_by_service_key(task_id: str = Path(..., description="任务ID")):
    """
    获取指定任务ID的日志，按service_key分组
    
    返回指定任务的所有日志记录，按service_key分组，并按时间排序
    
    Args:
        task_id: 任务ID
    
    Returns:
        按service_key分组的日志记录
    """
    try:
        # 创建服务实例
        service = AgentLogAggregationService()
        
        # 获取指定任务ID的日志记录
        aggregated_logs = service.aggregate_by_task_id(task_id)
        
        if not aggregated_logs:
            return Response.from_exception(ExceptionEnum.NOT_FOUND, msg=f"未找到任务ID为 {task_id} 的日志记录")
        
        return Response.build(aggregated_logs)
    except Exception as e:
        logging.error(f"获取任务日志失败: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg=f"获取任务日志失败: {str(e)}")


@router.get("/jobs_aggregate/hours", response_model=Response)
async def aggregate_jenkins_jobs(
    start_date: str = Query(..., description="开始日期 YYYY-MM-DD", example="2024-01-01"),
    end_date: str = Query(..., description="结束日期 YYYY-MM-DD", example="2024-01-31"),
    env_id: Optional[str] = Query(None, description="环境ID过滤(可选)"),
    principal: Optional[str] = Query(None, description="环境维护负责人过滤(可选)"),
    task_id: Optional[str] = Query(None, description="任务ID过滤(可选)"),
    group_by_env: bool = Query(False, description="是否按环境分组"),
    group_by_principal: bool = Query(False, description="是否按负责人分组"),
    group_by_task_id: bool = Query(False, description="是否按任务ID分组")
):
    """
    根据时间范围聚合Jenkins任务信息，支持manual_processing_hours求和

    该接口提供以下功能：
    - 按时间范围查询Jenkins任务
    - 统计总任务数、唯一任务数
    - 计算manual_processing_hours总和
    - 支持按环境ID、负责人、任务ID过滤
    - 支持按环境、负责人、任务ID分组统计

    Args:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        env_id: 可选的环境ID过滤条件
        principal: 可选的环境维护负责人过滤条件
        task_id: 可选的任务ID过滤条件
        group_by_env: 是否按环境分组返回统计结果
        group_by_principal: 是否按负责人分组返回统计结果
        group_by_task_id: 是否按任务ID分组返回统计结果

    Returns:
        聚合统计结果，包含总数和manual_processing_hours求和
    """
    try:
        # 验证日期格式
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return Response.from_exception(
                ExceptionEnum.PARAMETER_ERROR,
                msg="日期格式错误，请使用YYYY-MM-DD格式"
            )

        # 验证日期范围
        if start_date > end_date:
            return Response.from_exception(
                ExceptionEnum.PARAMETER_ERROR,
                msg="开始日期不能晚于结束日期"
            )

        # 调用统一的聚合服务
        result = JenkinsJobService.aggregate_jenkins_jobs(
            start_date=start_date,
            end_date=end_date,
            env_id=env_id,
            principal=principal,
            task_id=task_id,
            group_by_env=group_by_env,
            group_by_principal=group_by_principal,
            group_by_task_id=group_by_task_id
        )

        # 构建日志信息
        log_info = f"Jenkins任务聚合成功: {start_date} 到 {end_date}, 总任务数: {result.total_jobs}, 总人工时间: {result.total_manual_hours}小时"
        if principal:
            log_info += f", 负责人: {principal}"
        if task_id:
            log_info += f", 任务ID: {task_id}"

        logging.info(log_info)
        return Response.build(result.model_dump())

    except Exception as e:
        logging.error(f"Jenkins任务聚合失败: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"Jenkins任务聚合失败: {str(e)}"
        )
