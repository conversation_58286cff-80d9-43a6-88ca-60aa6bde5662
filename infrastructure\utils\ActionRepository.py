import asyncio
import json
from urllib.parse import urlencode

import httpx

from infrastructure.logger.logger import logger


class ActionRepository:

    def __init__(self, db="action"):
        self.url = "https://zxmte.zte.com.cn:3303/tdl/biz"
        self.db = db

    async def query(self, condition):
        conditionStr = urlencode(condition)
        url = f"{self.url}?db={self.db}&{conditionStr}"
        async with httpx.AsyncClient(timeout=5, proxies={}) as client:
            try:
                response = await client.get(url=url, timeout=5)
                resp = response.json()
                if resp["result"]:
                    return resp["data"]
                else:
                    logger.error(f"查询错误, err: {resp['fail_reason']}")
            except Exception as err:
                logger.error(f"查询错误, err: {err}")
        return []

    async def query_one(self, condition):
        res = await self.query(condition)
        if type(res) == list and len(res) > 0:
            return res[0]
        return {}

    # 更新所有action并删除不存在的action
    async def update_all(self, data):
        url = f"{self.url}?db={self.db}&opt=updateActions"
        async with httpx.AsyncClient(timeout=5, proxies={}) as client:
            await client.post(url=url, json=data, timeout=5)

    async def sync_action_name(self, actions):
        action_id_map = self._get_action_id_map(actions)
        try:
            url = "https://zxmte.zte.com.cn:3303/tdl/pipeline/synchronization/action_name"
            async with httpx.AsyncClient(timeout=5, proxies={}) as client:
                await client.post(url=url, json=action_id_map, timeout=5)
        except Exception as err:
            logger.error(f"sync_action_name failed: {err}")

    def _get_action_id_map(self, actions):
        return {action.get("actionId"): action.get("actionName") for action in actions}


if __name__ == '__main__':
    actionRepo = ActionRepository()
    # res = asyncio.run(actionRepo.query_one({}))
    result = asyncio.run(actionRepo.query_one({}))
    print(type(result))
    print(result)
