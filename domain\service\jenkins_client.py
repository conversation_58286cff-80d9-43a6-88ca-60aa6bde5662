import time
from typing import Optional
from urllib.parse import urljoin
import requests.exceptions
from requests import Session
from requests.auth import HTTPBasicAuth

# Adjust import path based on how the script is run
try:
    # When imported as a module within the package
    from infrastructure.logger.logger import logger
except ImportError:
    try:
        # When run as a module
        from ci_agents.infrastructure.logger.logger import logger
    except ImportError:
        # Fallback to a simple logger if neither import works
        import logging
        logging.basicConfig(level=logging.INFO,
                          format='%(asctime)s | %(levelname)-7s | %(filename)s:%(lineno)d | %(message)s')
        logger = logging.getLogger(__name__)


class JenkinsClient:
    def __init__(self, jenkins_url: str, username: str, password: str):
        """
        初始化 JenkinsClient 实例。

        :param jenkins_url: Jenkins 服务器地址
        :param username: Jenkins 用户名
        :param password: Jenkins 密码或 API Token
        """
        self.jenkins_url = jenkins_url.rstrip('/')
        self.auth = HTTPBasicAuth(username, password)
        self.headers = {'Content-Type': 'application/json'}
        self.session = Session()

    def _make_request(self, method: str, endpoint: str, return_full_response: bool = False, **kwargs):
        """
        发送HTTP请求到Jenkins API

        :param method: HTTP方法
        :param endpoint: API端点
        :param return_full_response: 是否返回完整的响应对象而不是JSON
        :param kwargs: 请求参数
        :return: JSON响应或完整的响应对象
        """
        url = urljoin(f"{self.jenkins_url}/", endpoint.lstrip('/'))
        kwargs.update({
            'auth': self.auth,
            'headers': self.headers
        })

        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()

            # 如果需要返回完整的响应对象
            if return_full_response:
                return response

            # 否则尝试解析JSON
            return response.json() if response.text else {}
        except Exception as e:
            logger.error(f"Jenkins API request failed: {str(e)}")
            raise

    def trigger_job(self, job_name: str, parameters: Optional[dict] = None) -> int:
        """
        触发 Jenkins Job。

        :param job_name: Jenkins Job 名称
        :param parameters: 可选，Job 参数字典
        :return: 触发后的构建编号
        """
        try:
            # 获取下一个构建号
            job_info = self._make_request('GET', f'job/{job_name}/api/json')
            next_build_number = job_info.get('nextBuildNumber')
            logger.info(f"Next build number for job {job_name}: {next_build_number}")

            # 触发构建，获取完整响应对象
            if parameters:
                endpoint = f"job/{job_name}/buildWithParameters"
                full_response = self._make_request('POST', endpoint, return_full_response=True, params=parameters)
            else:
                endpoint = f"job/{job_name}/build"
                full_response = self._make_request('POST', endpoint, return_full_response=True)

            # 从响应头中获取队列URL
            queue_url = full_response.headers.get('Location')
            if not queue_url:
                logger.warning(f"No Location header in response for job {job_name}. Using next build number: {next_build_number}")
                return next_build_number

            logger.info(f"Queue URL for job {job_name}: {queue_url}")

            # 从队列URL中提取队列ID
            try:
                queue_id = queue_url.split('/')[-2]
                logger.info(f"Queue ID for job {job_name}: {queue_id}")
            except Exception as e:
                logger.warning(f"Failed to extract queue ID from URL {queue_url}: {str(e)}. Using next build number: {next_build_number}")
                return next_build_number

            # 等待构建开始并获取构建号
            max_retries = 30  # 最大重试次数
            retry_count = 0
            while retry_count < max_retries:
                try:
                    queue_info = self._make_request('GET', f'queue/item/{queue_id}/api/json')
                    if 'executable' in queue_info:
                        build_number = queue_info['executable']['number']
                        logger.info(f"Build number for job {job_name}: {build_number}")
                        return build_number
                except Exception as e:
                    logger.warning(f"Error checking queue item {queue_id}: {str(e)}. Retrying...")

                time.sleep(2)  # 等待2秒后重试
                retry_count += 1

            # 如果队列信息获取失败，返回预计的构建号
            logger.warning(f"Failed to get build number from queue. Using next build number: {next_build_number}")
            return next_build_number

        except Exception as e:
            logger.error(f"Failed to trigger job {job_name}: {str(e)}")
            raise


    def get_job_status(self, job_name: str) -> str:
        """
        获取 Jenkins Job 当前的状态。

        :param job_name: Jenkins Job 名称
        :return: Job 的状态
        """
        try:
            job_info = self._make_request('GET', f'job/{job_name}/api/json')
            last_build = job_info.get('lastBuild')
            if not last_build:
                return 'NO_BUILD'

            build_info = self._make_request('GET', f'job/{job_name}/{last_build["number"]}/api/json')
            return build_info.get('result', 'BUILDING') if not build_info.get('building') else 'BUILDING'
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                return f"Job '{job_name}' not found"
            raise

    def get_job_info(self, job_name: str, build_num: Optional[int] = None) -> str:
        """
        获取 Jenkins Job 信息。

        :param job_name: Jenkins Job 名称
        :param build_num: 可选参数，指定构建编号
        :return: Job 的状态
        """
        try:
            if build_num is None:
                job_info = self._make_request('GET', f'job/{job_name}/api/json')
                last_build = job_info.get('lastBuild')
                if not last_build:
                    return 'NO_BUILD'
                build_num = last_build['number']

            build_info = self._make_request('GET', f'job/{job_name}/{build_num}/api/json')

            if build_info.get('building'):
                return 'BUILDING'

            return build_info.get('result', 'UNKNOWN_RESULT')
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                return f"NOTFOUND"
            return f"Error occurred: {str(e)}"

    def get_build_parameters(self, job_name: str, build_number: int) -> Optional[dict]:
        """
        获取指定构建的参数。

        :param job_name: Jenkins Job 名称
        :param build_number: 构建号
        :return: 参数字典，如果没有找到则返回None
        """
        try:
            build_info = self._make_request('GET', f'job/{job_name}/{build_number}/api/json')

            for action in build_info.get('actions', []):
                if action.get('_class') == 'hudson.model.ParametersAction':
                    return {
                        param['name']: param['value']
                        for param in action.get('parameters', [])
                    }
            return None
        except Exception as e:
            if getattr(e, 'response', None) and e.response.status_code == 404:
                logger.error(f"Build {build_number} not found for job {job_name}")
            else:
                logger.error(f"Failed to get build parameters: {str(e)}")
            raise

    def prepare_job_trigger(self, job_name: str, parameters: Optional[dict] = None) -> dict:
        """
        准备触发 Jenkins Job 的参数和 URL，但不实际触发。

        :param job_name: Jenkins Job 名称
        :param parameters: 可选，Job 参数字典
        :return: 包含触发信息的字典，包括 URL 和参数
        """
        try:
            # 验证 job 是否存在
            try:
                self._make_request('GET', f'job/{job_name}/api/json')
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    logger.error(f"Job '{job_name}' not found")
                    raise ValueError(f"Job '{job_name}' not found")
                raise

            # 准备触发信息
            if parameters:
                endpoint = f"job/{job_name}/buildWithParameters"
                trigger_url = urljoin(f"{self.jenkins_url}/", endpoint.lstrip('/'))
                return {
                    "job_name": job_name,
                    "trigger_url": trigger_url,
                    "parameters": parameters,
                    "method": "POST",
                    "auth_required": True,
                    "status": "prepared"
                }
            else:
                endpoint = f"job/{job_name}/build"
                trigger_url = urljoin(f"{self.jenkins_url}/", endpoint.lstrip('/'))
                return {
                    "job_name": job_name,
                    "trigger_url": trigger_url,
                    "parameters": {},
                    "method": "POST",
                    "auth_required": True,
                    "status": "prepared"
                }
        except Exception as e:
            logger.error(f"Failed to prepare job trigger: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试代码
    js = JenkinsClient(
        "https://cloudci.zte.com.cn/wireless-g5-nr-v3-hf/",
        "5g-hfc-ci",
        "G5.zte-H.FC"
    )
    # param = js.get_build_parameters("smoke-1014-ci-sh-hf", 943)
    #
    # if param is None:
    #     print("No build parameters found or build does not exist")
    # else:
    #     print(f"Build parameters: {param}")
    res = js.trigger_job("smoke123-1013-dailybuild_test_rebuild",{"trigger_downstream":"false"})
    print(res)