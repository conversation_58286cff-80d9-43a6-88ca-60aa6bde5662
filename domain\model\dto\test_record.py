from pydantic import BaseModel, Field
from datetime import datetime
from typing import Literal, Optional, Dict, Any
import uuid
class VersionExecuteInfo(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    env_id: str = Field(..., example="RAN3-T17-10466")
    version:str = ''
    test_result: Optional[Literal['success', "fail"]]
    version_task_dict: str = Field(default="{}")
    create_time: str = Field(default_factory=lambda: datetime.now().isoformat(), frozen=True)