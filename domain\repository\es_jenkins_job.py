from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document
from datetime import datetime


@Singleton
class EsJenkinsJob(Document):

    def __init__(self):
        Document.__init__(self, 'jenkins_job', 'jenkins_job')

    def bulk_default_value_filter_rules(self, defaultValueFilterRules):
        actions = []
        for defaultValueFilterRule in defaultValueFilterRules:
            defaultValueFilterRule.update({"timestamp": datetime.now()})
            defaultValueFilterRule.update({"_index": self._index, "_type": self._docType})
            actions.append(defaultValueFilterRule)
        self.bulk(actions)

    def delete_raw_data(self, dataId):
        queryDsl = {"query": {"bool": {"must": [{"match": {"dataId": dataId}}]}}}
        return self.delete_by_query(body=queryDsl, params={'request_timeout': 60})

    def query_exit_task(self, env_id, task_id):
        result = self.query_by_filter_without_sort({"env_id": env_id, "task_id": task_id})
        return result[1]

    def query_by_time_range(self, start_date, end_date, env_id=None, size=10000):
        """
        根据时间范围查询jenkins任务

        Args:
            start_date (str): 开始日期 YYYY-MM-DD
            end_date (str): 结束日期 YYYY-MM-DD
            env_id (str, optional): 环境ID过滤
            size (int): 返回记录数量限制

        Returns:
            tuple: (total_count, jobs)
        """
        must_match = []

        # 时间范围过滤
        time_range = {
            "range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00",
                    "lte": f"{end_date}T23:59:59"
                }
            }
        }
        must_match.append(time_range)

        # 环境ID过滤
        if env_id:
            must_match.append({"match_phrase": {"env_id": env_id}})

        query_dsl = {
            "query": {
                "bool": {
                    "must": must_match
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": size
        }

        return self.search(body=query_dsl)

    def get_earliest_by_task_ids(self, task_ids, size=10000):
        """
        根据task_id列表获取每个task_id最早的记录

        Args:
            task_ids (list): task_id列表
            size (int): 返回记录数量限制

        Returns:
            tuple: (total_count, jobs)
        """
        if not task_ids:
            return 0, []

        query_dsl = {
            "query": {
                "terms": {
                    "task_id": task_ids
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": size
        }

        return self.search(body=query_dsl)

    def aggregate_by_time_range(self, start_date, end_date, env_id=None, task_id=None,
                              group_by_env=False, group_by_task_id=False):
        """
        根据时间范围聚合jenkins任务，支持多维度过滤和分组

        Args:
            start_date (str): 开始日期 YYYY-MM-DD
            end_date (str): 结束日期 YYYY-MM-DD
            env_id (str, optional): 环境ID过滤
            task_id (str, optional): 任务ID过滤
            group_by_env (bool): 是否按环境分组
            group_by_task_id (bool): 是否按任务ID分组

        Returns:
            dict: 聚合结果包含总数、manual_processing_hours总和等
        """
        must_match = []

        # 时间范围过滤
        time_range = {
            "range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00",
                    "lte": f"{end_date}T23:59:59"
                }
            }
        }
        must_match.append(time_range)

        # 环境ID过滤
        if env_id:
            must_match.append({"match_phrase": {"env_id": env_id}})

        # 任务ID过滤
        if task_id:
            must_match.append({"match_phrase": {"task_id": task_id}})

        # 构建基础聚合查询
        aggs = {
            "total_jobs": {
                "value_count": {
                    "field": "task_id"
                }
            },
            "total_manual_hours": {
                "sum": {
                    "field": "manual_processing_hours"
                }
            },
            "unique_task_count": {
                "cardinality": {
                    "field": "task_id"
                }
            }
        }

        # 如果按环境分组
        if group_by_env:
            aggs["env_groups"] = {
                "terms": {
                    "field": "env_id.keyword",
                    "size": 1000
                },
                "aggs": {
                    "total_jobs": {
                        "value_count": {
                            "field": "task_id"
                        }
                    },
                    "total_manual_hours": {
                        "sum": {
                            "field": "manual_processing_hours"
                        }
                    },
                    "unique_task_count": {
                        "cardinality": {
                            "field": "task_id"
                        }
                    }
                }
            }

        # 如果按任务ID分组
        if group_by_task_id:
            aggs["task_id_groups"] = {
                "terms": {
                    "field": "task_id.keyword",
                    "size": 1000
                },
                "aggs": {
                    "total_jobs": {
                        "value_count": {
                            "field": "task_id"
                        }
                    },
                    "total_manual_hours": {
                        "sum": {
                            "field": "manual_processing_hours"
                        }
                    },
                    "env_id": {
                        "terms": {
                            "field": "env_id.keyword",
                            "size": 1
                        }
                    }
                }
            }

        query_dsl = {
            "query": {
                "bool": {
                    "must": must_match
                }
            },
            "aggs": aggs,
            "size": 0  # 只返回聚合结果，不返回具体文档
        }

        result = self.search(body=query_dsl)

        # 处理聚合结果
        aggregations = result[1].get('aggregations', {})

        response = {
            "total_jobs": aggregations.get('total_jobs', {}).get('value', 0),
            "total_manual_hours": aggregations.get('total_manual_hours', {}).get('value', 0.0),
            "unique_task_count": aggregations.get('unique_task_count', {}).get('value', 0),
            "time_range": {
                "start_date": start_date,
                "end_date": end_date
            }
        }

        # 如果按环境分组，添加环境分组信息
        if group_by_env and 'env_groups' in aggregations:
            env_buckets = aggregations['env_groups'].get('buckets', [])
            response["env_groups"] = []
            for bucket in env_buckets:
                env_data = {
                    "env_id": bucket['key'],
                    "total_jobs": bucket.get('total_jobs', {}).get('value', 0),
                    "total_manual_hours": bucket.get('total_manual_hours', {}).get('value', 0.0),
                    "unique_task_count": bucket.get('unique_task_count', {}).get('value', 0)
                }
                response["env_groups"].append(env_data)

        # 如果按任务ID分组，添加任务ID分组信息
        if group_by_task_id and 'task_id_groups' in aggregations:
            task_buckets = aggregations['task_id_groups'].get('buckets', [])
            response["task_id_groups"] = []
            for bucket in task_buckets:
                env_buckets = bucket.get('env_id', {}).get('buckets', [])
                env_id = env_buckets[0]['key'] if env_buckets else ""

                task_data = {
                    "task_id": bucket['key'],
                    "total_jobs": bucket.get('total_jobs', {}).get('value', 0),
                    "total_manual_hours": bucket.get('total_manual_hours', {}).get('value', 0.0),
                    "env_id": env_id
                }
                response["task_id_groups"].append(task_data)

        return response


