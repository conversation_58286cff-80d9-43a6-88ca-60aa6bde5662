from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document
from datetime import datetime


@Singleton
class EsJenkinsJob(Document):

    def __init__(self):
        Document.__init__(self, 'jenkins_job', 'jenkins_job')

    def bulk_default_value_filter_rules(self, defaultValueFilterRules):
        actions = []
        for defaultValueFilterRule in defaultValueFilterRules:
            defaultValueFilterRule.update({"timestamp": datetime.now()})
            defaultValueFilterRule.update({"_index": self._index, "_type": self._docType})
            actions.append(defaultValueFilterRule)
        self.bulk(actions)

    def delete_raw_data(self, dataId):
        queryDsl = {"query": {"bool": {"must": [{"match": {"dataId": dataId}}]}}}
        return self.delete_by_query(body=queryDsl, params={'request_timeout': 60})

    def query_exit_task(self, env_id, task_id):
        result = self.query_by_filter_without_sort({"env_id": env_id, "task_id": task_id})
        return result[1]

    def query_by_time_range(self, start_date, end_date, env_id=None, size=10000):
        """
        根据时间范围查询jenkins任务

        Args:
            start_date (str): 开始日期 YYYY-MM-DD
            end_date (str): 结束日期 YYYY-MM-DD
            env_id (str, optional): 环境ID过滤
            size (int): 返回记录数量限制

        Returns:
            tuple: (total_count, jobs)
        """
        must_match = []

        # 时间范围过滤
        time_range = {
            "range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00",
                    "lte": f"{end_date}T23:59:59"
                }
            }
        }
        must_match.append(time_range)

        # 环境ID过滤
        if env_id:
            must_match.append({"match_phrase": {"env_id": env_id}})

        query_dsl = {
            "query": {
                "bool": {
                    "must": must_match
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": size
        }

        return self.search(body=query_dsl)

    def get_earliest_by_task_ids(self, task_ids, size=10000):
        """
        根据task_id列表获取每个task_id最早的记录

        Args:
            task_ids (list): task_id列表
            size (int): 返回记录数量限制

        Returns:
            tuple: (total_count, jobs)
        """
        if not task_ids:
            return 0, []

        query_dsl = {
            "query": {
                "terms": {
                    "task_id": task_ids
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": size
        }

        return self.search(body=query_dsl)

    def aggregate_by_time_range(self, start_date, end_date, env_id=None, task_id=None,
                              group_by_env=False, group_by_task_id=False):
        """
        根据时间范围聚合jenkins任务，支持多维度过滤和分组
        manual_processing_hours在内存中处理，避免ES字段类型问题

        Args:
            start_date (str): 开始日期 YYYY-MM-DD
            end_date (str): 结束日期 YYYY-MM-DD
            env_id (str, optional): 环境ID过滤
            task_id (str, optional): 任务ID过滤
            group_by_env (bool): 是否按环境分组
            group_by_task_id (bool): 是否按任务ID分组

        Returns:
            dict: 聚合结果包含总数、manual_processing_hours总和等
        """
        must_match = []

        # 时间范围过滤
        time_range = {
            "range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00",
                    "lte": f"{end_date}T23:59:59"
                }
            }
        }
        must_match.append(time_range)

        # 环境ID过滤
        if env_id:
            must_match.append({"match_phrase": {"env_id": env_id}})

        # 任务ID过滤
        if task_id:
            must_match.append({"match_phrase": {"task_id": task_id}})

        # 简化查询，获取原始数据在内存中处理
        query_dsl = {
            "query": {
                "bool": {
                    "must": must_match
                }
            },
            "size": 10000,  # 获取原始数据
            "_source": ["task_id", "env_id", "manual_processing_hours", "timestamp"]
        }

        # 直接使用ES搜索，获取完整响应
        es_result = self._es.search(index=self._index, doc_type=self._docType, body=query_dsl)
        hits = es_result.get('hits', {}).get('hits', [])

        # 在内存中处理所有聚合逻辑
        total_jobs = len(hits)
        unique_tasks = set()
        total_manual_hours = 0.0
        env_groups_data = {}
        task_groups_data = {}

        for hit in hits:
            source = hit.get('_source', {})
            task_id = source.get('task_id', '')
            env_id = source.get('env_id', '')
            manual_hours = float(source.get('manual_processing_hours', 0.0))

            # 统计唯一任务
            unique_tasks.add(task_id)

            # 累计人工处理时间
            total_manual_hours += manual_hours

            # 按环境分组统计
            if group_by_env:
                if env_id not in env_groups_data:
                    env_groups_data[env_id] = {
                        "env_id": env_id,
                        "total_jobs": 0,
                        "total_manual_hours": 0.0,
                        "unique_tasks": set()
                    }
                env_groups_data[env_id]["total_jobs"] += 1
                env_groups_data[env_id]["total_manual_hours"] += manual_hours
                env_groups_data[env_id]["unique_tasks"].add(task_id)

            # 按任务ID分组统计
            if group_by_task_id:
                if task_id not in task_groups_data:
                    task_groups_data[task_id] = {
                        "task_id": task_id,
                        "total_jobs": 0,
                        "total_manual_hours": 0.0,
                        "env_id": env_id  # 使用第一个遇到的env_id
                    }
                task_groups_data[task_id]["total_jobs"] += 1
                task_groups_data[task_id]["total_manual_hours"] += manual_hours

        # 构建响应
        response = {
            "total_jobs": total_jobs,
            "total_manual_hours": total_manual_hours,
            "unique_task_count": len(unique_tasks),
            "time_range": {
                "start_date": start_date,
                "end_date": end_date
            }
        }

        # 添加环境分组信息
        if group_by_env and env_groups_data:
            response["env_groups"] = []
            for env_data in env_groups_data.values():
                response["env_groups"].append({
                    "env_id": env_data["env_id"],
                    "total_jobs": env_data["total_jobs"],
                    "total_manual_hours": env_data["total_manual_hours"],
                    "unique_task_count": len(env_data["unique_tasks"])
                })

        # 添加任务ID分组信息
        if group_by_task_id and task_groups_data:
            response["task_id_groups"] = list(task_groups_data.values())

        return response


