import threading
import time


class Once:
    def __init__(self, func):
        self.__done = threading.Event()
        self.__func = func

    def __call__(self):
        if not self.__done.is_set():
            self.__done.set()
            self.__func()


if __name__ == '__main__':
    @Once
    def test1():
        time.sleep(1)
        print("done")

    threading.Thread(target=test1).start()
    threading.Thread(target=test1).start()
    threading.Thread(target=test1).start()
    threading.Thread(target=test1).start()
