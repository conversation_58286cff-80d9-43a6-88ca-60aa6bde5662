user  root;
worker_processes  16;

#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events
{
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/csa_access.log;
    error_log   /var/log/nginx/csa_error.log;

    sendfile        on;

    keepalive_timeout  65;

    gzip  on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_comp_level 4;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    map $sent_http_content_type $expires {
        default                    off;
        #text/html                  epoch; # 不缓存
        text/css                   30d; # 缓存30天，长时缓存可以设置为max
        application/javascript     30d;
        ~image/                    30d;
    }

    server {
        listen       8848;
        server_name  localhost;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Headers' '*';
        add_header X-Frame-Options SAMEORIGIN;
        add_header Access-Control-Allow-Credentials  true;
        index index.html;

        location / {
            add_header  Access-Control-Allow-Origin *;
            add_header  X-Frame-Options SAMEORIGIN;
            root /usr/share/nginx/html/;
            try_files $uri $uri/ @router;
            index  index.html index.htm;
            #add_header Access-Control-Allow-Origin $cors_origin;
        }
        location /ci-agents-front {
            add_header  Access-Control-Allow-Origin *;
            add_header  Cache-Control no-store;
            add_header  X-Frame-Options SAMEORIGIN;
            if_modified_since off;
            add_header  Last-Modified  "";
            etag off;
            alias /usr/share/nginx/html/;
            try_files $uri $uri/ @router;
            index  index.html index.htm;
            #add_header Access-Control-Allow-Origin $cors_origin;
        }
        location @router {
            add_header  Access-Control-Allow-Origin *;
            add_header  X-Frame-Options SAMEORIGIN;
            #add_header Access-Control-Allow-Origin $cors_origin;
            rewrite ^.*$ /index.html last;
        }
  }
}
