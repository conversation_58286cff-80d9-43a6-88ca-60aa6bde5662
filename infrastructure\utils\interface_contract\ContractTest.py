# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
from infrastructure.device.CmdResult import CmdResult
from infrastructure.utils.interface_contract.ContractExcutor import ContractExcutor
from infrastructure.utils.interface_contract.ContractParser import ContractParser
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister


class TestDomain(object):
    def __init__(self, alias):
        self.alias = alias
        self.tool = TestTool()
        ContractRegister().register(self.alias, 'DSPMONITOR_CLIENT', self.tool)

    def execute_cmd(self):
        print('execute_cmd')

    def excute_cmd(self):
        print('excute_cmd')


class TestTool(object):
    def excute_cmd(self, cmd, expected, timeout):
        print(cmd)
        print('--------------------------------------')
        cmdResult = CmdResult()
        return cmdResult

    def func_test(self, s):
        print("functest: " + s)


def execute_contract(contractPath, paraDict):
    contractExcutor = ContractExcutor(contractPath, paraDict)
    return contractExcutor.excute()


if __name__ == '__main__':
    t = TestDomain('TEST')
    print(getattr(t, 'execute_cmd', getattr(t, 'excute_cmd'))())
    paraDict = {'gnbDspAlias': 'TEST'}
    contractPath = 'DmacToPhyVbpServiceContract.dl_4flow_gnb_flag'
    print(execute_contract(contractPath, paraDict))
