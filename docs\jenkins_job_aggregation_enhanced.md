# Jenkins任务聚合功能增强版

## 概述

本功能实现了根据时间范围聚合JenkinsJobInfo的能力，支持多维度过滤和分组，包括环境维护负责人和任务ID聚合。

## 功能特性

- **时间范围查询**: 支持按开始时间和结束时间查询Jenkins任务
- **manual_processing_hours求和**: 自动计算指定时间范围内的人工处理时间总和
- **多维度过滤**: 支持按环境ID、环境维护负责人、任务ID过滤查询结果
- **多维度分组**: 支持按环境、负责人、任务ID分组统计，获取详细统计信息
- **任务去重**: 统计唯一任务数量，避免重复计算
- **负责人关联**: 自动从BizCoreEnv获取环境维护负责人信息

## API接口

### 聚合查询接口

**接口地址**: `GET /jenkins/jobs/aggregate`

**请求参数**:
- `start_date` (必需): 开始日期，格式为YYYY-MM-DD
- `end_date` (必需): 结束日期，格式为YYYY-MM-DD  
- `env_id` (可选): 环境ID过滤条件
- `principal` (可选): 环境维护负责人过滤条件
- `task_id` (可选): 任务ID过滤条件
- `group_by_env` (可选): 是否按环境分组，默认为false
- `group_by_principal` (可选): 是否按负责人分组，默认为false
- `group_by_task_id` (可选): 是否按任务ID分组，默认为false

**响应格式**:
```json
{
  "success": true,
  "data": {
    "total_jobs": 100,
    "total_manual_hours": 25.5,
    "unique_task_count": 80,
    "time_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "env_groups": [
      {
        "env_id": "RAN3-T17-10466",
        "total_jobs": 50,
        "total_manual_hours": 12.0,
        "unique_task_count": 40
      }
    ],
    "principal_groups": [
      {
        "principal": "张三",
        "total_jobs": 60,
        "total_manual_hours": 15.0,
        "unique_task_count": 50,
        "env_ids": ["RAN3-T17-10466", "RAN3-T17-10467"]
      }
    ],
    "task_id_groups": [
      {
        "task_id": "task123",
        "total_jobs": 5,
        "total_manual_hours": 2.5,
        "env_id": "RAN3-T17-10466",
        "principal": "张三"
      }
    ]
  }
}
```

## 使用示例

### 基本查询
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31"
```

### 按环境过滤
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&env_id=RAN3-T17-10466"
```

### 按负责人过滤
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&principal=张三"
```

### 按任务ID过滤
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&task_id=task123"
```

### 按环境分组
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&group_by_env=true"
```

### 按负责人分组
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&group_by_principal=true"
```

### 按任务ID分组
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&group_by_task_id=true"
```

### 组合查询
```bash
curl "http://localhost:8000/jenkins/jobs/aggregate?start_date=2024-01-01&end_date=2024-01-31&principal=张三&group_by_env=true"
```

## 技术实现

### 数据模型

#### JenkinsJobAggregationResponse
- `total_jobs`: 总任务数
- `total_manual_hours`: 总人工处理时间(小时)
- `unique_task_count`: 唯一任务数
- `time_range`: 查询时间范围
- `env_groups`: 环境分组统计(可选)
- `principal_groups`: 负责人分组统计(可选)
- `task_id_groups`: 任务ID分组统计(可选)

#### PrincipalGroupStats
- `principal`: 环境维护负责人
- `total_jobs`: 该负责人的总任务数
- `total_manual_hours`: 该负责人的总人工处理时间
- `unique_task_count`: 该负责人的唯一任务数
- `env_ids`: 该负责人负责的环境ID列表

#### TaskIdGroupStats
- `task_id`: 任务ID
- `total_jobs`: 该任务的总任务数
- `total_manual_hours`: 该任务的总人工处理时间
- `env_id`: 该任务的环境ID
- `principal`: 该任务环境的维护负责人

### 核心组件

#### Repository层 (EsJenkinsJob)
- `aggregate_by_time_range()`: 统一的Elasticsearch聚合查询方法
- 支持时间范围过滤、环境过滤、任务ID过滤和多维度分组
- 使用Elasticsearch的聚合功能进行高效计算

#### Service层 (JenkinsJobService)
- `aggregate_jenkins_jobs()`: 统一的业务逻辑处理方法
- `get_principal_by_env_ids()`: 批量获取环境负责人信息
- `_generate_principal_groups()`: 生成负责人分组统计
- `_merge_aggregation_results()`: 合并多个聚合结果

#### API层 (jenkins_job.py)
- `/jenkins/jobs/aggregate`: 统一的RESTful API接口
- 支持所有过滤和分组参数
- 完善的参数验证和错误处理

## 负责人信息获取

系统通过以下方式获取环境维护负责人信息：

1. **数据源**: 从BizCoreEnv表获取principal字段
2. **查询方式**: 根据env_id查询对应的principal
3. **批量处理**: 支持批量获取多个环境的负责人信息
4. **容错处理**: 当无法获取负责人信息时，显示为"未知"

## 性能优化

- 使用Elasticsearch聚合查询，避免大量数据传输
- 支持size=0参数，只返回聚合结果不返回原始文档
- 批量获取负责人信息，减少数据库查询次数
- 统一的聚合方法，根据参数自动选择最优查询策略

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加新的过滤维度
- 支持更多的分组统计方式
- 可以扩展到其他时间维度的聚合
- 支持更复杂的多维度组合查询
