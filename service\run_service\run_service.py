import asyncio
from datetime import datetime
import json
import redis
import os

from domain.model.dto.corn_task import Needed, CornTask
from domain.model.dto.test_record import VersionExecuteInfo
from domain.repository.version_execute import EsVersionExecute
from infrastructure.db.redis.Redisdb import RedisDB
import requests
from domain.repository.cron_task import EsCronTask
import aiohttp

from service.logging_service import LoggingService
from service.tools.EmailService import EmailService, EmailData
from service.tools.ActionService import ActionService
from service.tools.UmeService import UmeService
from infrastructure.utils.Repeat import retries_during_time
from domain.model.tools.ActionExecuteInfo import ActionExecuteStatusEnum
from infrastructure.logger.logger import logger
from typing import Dict, List, Tuple, Optional
import paramiko

# 定义服务基类
class Service:
    def __init__(self, service_type, subtype, state, needed, es):
        self.service_type = service_type
        self.subtype = subtype
        self.state = state
        self.needed = needed
        self._es = es
        self._es_version_execute = EsVersionExecute()

    async def execute(self):
        raise NotImplementedError("Subclasses should implement this method.")

    # async def send_post_message(self, url,body):
    #     async with aiohttp.ClientSession() as session:
    #         async with session.post(url, json=body) as response:
    #             if response.status == 200:
    #                 print("Message sent successfully.")
    #             else:
    #                 print(f"Failed to send message. Status: {response.status}, Response: {await response.text()}")

    def send_post_message(self, url, body):
        try:
            # 使用 requests 发送 POST 请求
            response = requests.post(url, json=body)

            if response.status_code == 200:
                print("Message sent successfully.")
            else:
                print(f"Failed to send message. Status: {response.status_code}, Response: {response.text}")

        except Exception as e:
            print(f"Error occurred: {e}")

    def _update_task_status(self, task_id, cron_task_id, status, env_id, trace_info):
        self._es.update_by_query(
            {"task_id": task_id, "service_type": self.service_type, "cron_task_id": cron_task_id},
            {"state": status,
             "update_time": datetime.now().isoformat()}
        )

        message = {"subtype": self.subtype, "needed": self.needed, "state": status}
        LoggingService.log_execution(
            service_type=self.service_type,
            operation="cron_task_" + self.service_type,
            status="success",
            task_id=task_id,
            env_id=env_id,
            details={"message": message, "trace_info": trace_info},
            service_key=self.service_type
        )

    def _update_version_execute(self, task_id: str, version: str, env_id: str, status: str = "执行中",
                                result: str = None):
        """
        更新所有相同task_id的记录并插入当前version的新记录
        
        Args:
            task_id: 任务ID
            version: 当前版本
            env_id: 环境ID
            status: 版本执行状态 ("执行中", "已完成" 等)
            result: 执行结果 ("success", "fail" 等)
        """
        try:
            # 获取最新的执行记录
            records = self._es_version_execute.get_execute_info(task_id)

            # 获取最新的version_task_dict
            version_task_dict = {}
            if records:
                latest_record = records[0]
                version_task_dict_str = latest_record.get("version_task_dict", "{}")
                try:
                    if isinstance(version_task_dict_str, str):
                        version_task_dict = eval(version_task_dict_str)
                    else:
                        version_task_dict = version_task_dict_str
                except Exception as e:
                    logger.error(f"解析version_task_dict失败: {e}")
                    version_task_dict = {}

            # 更新version_task_dict中当前version的状态
            if version not in version_task_dict:
                version_task_dict[version] = {}
            version_task_dict[version]["status"] = status
            version_task_dict[version]["result"] = result

            # 创建新的执行记录
            version_execute_info = VersionExecuteInfo(
                task_id=task_id,
                env_id=env_id,
                version=version,
                test_result=result,
                version_task_dict=str(version_task_dict)
            )

            # 保存新记录
            self._es_version_execute.save_version_execute_task(version_execute_info)

            # 更新所有相同task_id的记录的version_task_dict
            self._es_version_execute.update_by_query(
                {"task_id": task_id},
                {"version_task_dict": str(version_task_dict)}
            )

        except Exception as e:
            logger.error(f"更新版本执行记录失败: {str(e)}")
            raise

    def _general_next_version(self, task_id: str, service_type: str, version: str) -> Optional[str]:
        """
        根据服务类型生成下一个要测试的版本
        
        Args:
            task_id: 任务ID
            service_type: 服务类型 ("version_rollback" 或 "version_upgrade")
            version: 当前版本
        
        Returns:
            str: 下一个要测试的版本，如果没有更多版本或出现异常则返回None
        """
        try:
            # 获取版本执行记录
            records = self._es_version_execute.get_execute_info(task_id)
            print("records", records)
            if not records:
                logger.error(f"未找到任务 {task_id} 的执行记录")
                return None

            # 解析version_task_dict
            latest_record = records[0]
            version_task_dict = self._parse_version_task_dict(latest_record)
            print("~~~version_task_dict", version_task_dict)
            if not version_task_dict:
                return None
            is_found, find_version = self._is_version_search_complete(version_task_dict)
            # 检查是否已经找到问题版本
            if is_found:
                return None
            print("xxxxxxxxxxxxxxxxx")
            # 根据服务类型选择不同的版本查找策略
            if service_type == "version_rollback":
                next_vesion = self._find_next_version_for_rollback(version_task_dict, version)
                print("verison   1111", next_vesion)
                return next_vesion
            elif service_type == "version_upgrade":
                next_vesion = self._find_next_version_for_upgrade(version_task_dict, version)
                print("verison   2222", next_vesion)
                return next_vesion
            else:
                logger.error(f"未知的服务类型: {service_type}")
                return None

        except Exception as e:
            logger.error(f"生成下一个版本时出错: {str(e)}")
            self._send_error_email(task_id, str(e))
            return None

    def _parse_version_task_dict(self, record) -> Dict:
        """解析version_task_dict"""
        try:
            version_task_dict_str = record.get("version_task_dict", "{}")
            if isinstance(version_task_dict_str, str):
                return eval(version_task_dict_str)
            return version_task_dict_str
        except Exception as e:
            logger.error(f"解析version_task_dict失败: {e}")
            return {}

    def _is_version_search_complete(self, version_task_dict: Dict) -> Tuple[bool, Optional[str]]:
        """
        检查是否已经找到问题版本

        Returns:
            Tuple[bool, Optional[str]]: (是否完成, 问题版本号)
                - 如果最早版本失败，返回 (True, earliest_version)
                - 如果所有已完成版本都失败，返回 (True, earliest_version)
                - 如果找到相邻的成功和失败版本，返回 (True, fail_version)
                - 如果搜索未完成，返回 (False, None)
        """
        if not version_task_dict:
            self._send_email("回溯流程结束", f"未找到对应的回溯记录表{version_task_dict}")
            return True, None

        versions = sorted(version_task_dict.keys(),
                          key=lambda x: int(x.split('_')[-1]))  # 按日期排序

        # 检查最早的版本
        earliest_version = versions[0]
        if (version_task_dict[earliest_version]["status"] == "已完成" and
                version_task_dict[earliest_version]["result"] == "fail"):
            return True, earliest_version

        # 检查是否所有已完成的版本都失败
        all_failed = True
        has_completed = False
        for version in versions:
            if version_task_dict[version]["status"] == "已完成":
                has_completed = True
                if version_task_dict[version]["result"] == "success":
                    all_failed = False
                    break

        if has_completed and all_failed:
            self._send_email("回溯流程结束", f"疑似故障版本为{earliest_version}")
            return True, earliest_version
        print("!@!@!@#!@@")
        # 查找相邻的成功和失败版本
        for i in range(len(versions) - 1):
            curr_version = versions[i]
            next_version = versions[i + 1]

            curr_status = version_task_dict[curr_version]
            next_status = version_task_dict[next_version]

            # 只在两个版本都已完成时进行检查
            if curr_status["status"] == "已完成" and next_status["status"] == "已完成":
                if curr_status["result"] == "success" and next_status["result"] == "fail":
                    print("121212122")
                    self._send_email("回溯流程结束", f"疑似故障版本为{next_version}")
                    return True, next_version
                # 检查异常情况：前一个失败后一个成功
                elif curr_status["result"] == "fail" and next_status["result"] == "success":
                    self._send_email("回溯流程结束",
                                     f"错误: 前一个版本 {curr_version} 失败，后一个版本 {next_version} 成功，这种情况不应该出现")
                    raise ValueError(
                        f"错误: 前一个版本 {curr_version} 失败，后一个版本 {next_version} 成功，这种情况不应该出现")

        # 如果没有找到问题版本，返回False和None
        return False, None

    def _binary_search_version(self, versions: List[str], start_idx: int, end_idx: int) -> Optional[str]:
        """
        执行二分查找，在偶数个版本时选择左侧中间版本
        
        Args:
            versions: 版本列表
            start_idx: 起始索引
            end_idx: 结束索引
        
        Returns:
            str: 选择的版本号，如果没有合适的版本则返回None
        """
        if start_idx >= end_idx:
            return None

        # 计算区间内的版本数量
        version_count = end_idx - start_idx + 1

        # 如果是偶数个版本，选择左侧的中间位置
        if version_count % 2 == 0:
            mid_idx = start_idx + (version_count // 2 - 1)
        else:
            # 奇数个版本，选择中间位置
            mid_idx = start_idx + (version_count // 2)
        logger.info(f"start_idx为{start_idx},mid_idx为{mid_idx}")
        print("mid_idx", start_idx, version_count, (version_count // 2 - 1), mid_idx)
        return versions[mid_idx]

    def _find_next_version_for_rollback(self, version_task_dict: Dict, current_version: str) -> Optional[str]:
        """为回退操作找到下一个要测试的版本"""
        versions = sorted(version_task_dict.keys(),
                          key=lambda x: int(x.split('_')[-1]))  # 按日期排序

        # 找到最后一个成功的版本
        last_success_version = None
        current_idx = versions.index(current_version)
        logger.info(f"已经进入_find_next_version_for_rollback流程")
        for version in versions[:current_idx]:
            if (version_task_dict[version]["status"] == "已完成" and
                    version_task_dict[version]["result"] == "success"):
                last_success_version = version

        if not last_success_version:
            return None

        # 在最后一个成功版本和当前版本之间进行二分查找
        start_idx = versions.index(last_success_version)
        end_idx = current_idx
        print("ssssss", versions, start_idx, end_idx)
        return self._binary_search_version(versions, start_idx, end_idx)

    def _find_next_version_for_upgrade(self, version_task_dict: Dict, current_version: str) -> Optional[str]:
        """为升级操作找到下一个要测试的版本"""
        versions = sorted(version_task_dict.keys(),
                          key=lambda x: int(x.split('_')[-1]))  # 按日期排序

        # 找到第一个失败的版本
        first_fail_version = None
        current_idx = versions.index(current_version)
        logger.info(f"已经进入_find_next_version_for_upgrade流程")
        for version in versions[current_idx:]:
            if (version_task_dict[version]["status"] == "已完成" and
                    version_task_dict[version]["result"] == "fail"):
                first_fail_version = version
                break

        if not first_fail_version:
            return None

        # 在当前版本和第一个失败版本之间进行二分查找
        start_idx = current_idx
        end_idx = versions.index(first_fail_version)
        return self._binary_search_version(versions, start_idx, end_idx)

    def _send_email(self, subject, content):
        """发送邮件通知"""
        email_service = EmailService()
        email_data = EmailData(
            subject=subject,
            content=content,
            recipients=["<EMAIL>"],
            # cc=["<EMAIL>"]
            cc=[]
        )
        email_service.email_send(email_data)

    def _send_error_email(self, task_id: str, error_msg: str):
        """发送错误通知邮件"""
        subject = f"版本回溯异常 - 任务 {task_id}"
        content = f"在执行版本回溯过程中发生异常：\n{error_msg}"
        # 调用邮件服务发送通知
        self._send_email(subject=subject, content=content)

    def get_bs_version(self, env_id, max_tries=12, delay_secs=5, duration_secs=60):
        """
         Get base station version with retry mechanism

         Args:
             env_id (str): Environment ID
             max_tries (int): Maximum number of retry attempts
             delay_secs (int): Time interval between retries in seconds
             duration_secs (int): Maximum total wait time in seconds

         Returns:
             str: Base station version string if successful, empty string if no data found

         Raises:
             Exception: If version retrieval fails
         """
        execute_result = UmeService.get_bs_current_version(env_id)

        @retries_during_time(
            durationSecs=duration_secs,
            everyTryDelaySecs=delay_secs,
            maxTries=max_tries,
            isRaiseException=True,
            flag=False,
            returnType='1'
        )
        def get_execution_status():
            exe_info = ActionService.get_execution_info(execute_result.execute_result)
            # 只在成功或失败时返回结果
            if exe_info.data.status in [ActionExecuteStatusEnum.SUCCESS, ActionExecuteStatusEnum.FAIL]:
                print(exe_info.data.output)
                if exe_info.data.output and exe_info.data.output.get('data'):
                    # 获取data字典中的第一个值
                    version_data = exe_info.data.output['data']
                    print("version_datea", version_data)
                    if version_data:
                        # 返回第一个键值对的值
                        return next(iter(version_data.values()), '')
                return ''
            logger.debug(f"Current status: {exe_info.data.status}, continuing to poll...")
            return False  # 继续重试，直到成功或失败

        try:
            return get_execution_status()
        except Exception as e:
            logger.error(f"Failed to get base station version: {str(e)}")
            raise

    def save_local_test_task(self, task_id, env_id, tar_path, service_type, subtype, job_name, build_number,
                             pipelineUuid, pipelineStageId, recordId, log_path="", log_name=""):
        needed = Needed(
            tar_path=tar_path,
            log_path=log_path,
            log_name=log_name,
            job_name=job_name,
            build_number=build_number,
            pipelineUuid=pipelineUuid,
            pipelineStageId=pipelineStageId,
            recordId=recordId,
        )
        corn_task = CornTask(
            task_id=task_id,
            env_id=env_id,
            needed=needed,
            service_type=service_type,
            state="running",
            subtype=subtype
        )
        self._es.save_corn_task(corn_task)
        LoggingService.log_execution(
            service_type=service_type,
            operation="cron_task_" + service_type,
            status="in_progress",
            task_id=task_id,
            env_id=env_id,
            service_key=service_type
        )

    def get_bs_backup_version(self, env_id, max_tries=12, delay_secs=5, duration_secs=60):
        """
         Get base station version with retry mechanism

         Args:
             env_id (str): Environment ID
             max_tries (int): Maximum number of retry attempts
             delay_secs (int): Time interval between retries in seconds
             duration_secs (int): Maximum total wait time in seconds

         Returns:
             str: Base station version string if successful, empty string if no data found

         Raises:
             Exception: If version retrieval fails
         """
        execute_result = UmeService.get_bs_backup_version(env_id)

        @retries_during_time(
            durationSecs=duration_secs,
            everyTryDelaySecs=delay_secs,
            maxTries=max_tries,
            isRaiseException=True,
            flag=False,
            returnType='1'
        )
        def get_execution_status():
            exe_info = ActionService.get_execution_info(execute_result.execute_result)
            # 只在成功或失败时返回结果
            if exe_info.data.status in [ActionExecuteStatusEnum.SUCCESS, ActionExecuteStatusEnum.FAIL]:
                print(exe_info.data.output)
                if exe_info.data.output and exe_info.data.output.get('data'):
                    # 获取data字典中的第一个值
                    version_data = exe_info.data.output['data']
                    print("backup_version_data", version_data)
                    if version_data:
                        # 返回第一个键值对的值
                        return version_data
                return ''
            logger.debug(f"Current status: {exe_info.data.status}, continuing to poll...")
            return False  # 继续重试，直到成功或失败

        try:
            return get_execution_status()
        except Exception as e:
            logger.error(f"Failed to get base station version: {str(e)}")
            raise

    def get_env_xml_url(self, env_id,max_tries=12, delay_secs=5, duration_secs=60):
        execute_result = UmeService.get_env_xml_url(env_id)
        @retries_during_time(
            durationSecs=duration_secs,
            everyTryDelaySecs=delay_secs,
            maxTries=max_tries,
            isRaiseException=True,
            flag=False,
            returnType='1'
        )
        def get_execution_status():
            exe_info = ActionService.get_execution_info(execute_result.execute_result)
            # 只在成功或失败时返回结果
            if exe_info.data.status in [ActionExecuteStatusEnum.SUCCESS, ActionExecuteStatusEnum.FAIL]:
                print(exe_info.data.output)
                if exe_info.data.output and exe_info.data.output.get('data'):
                    # 获取data字典中的第一个值
                    version_xml_url = exe_info.data.output['data']
                    print("version_xml_url", version_xml_url)
                    sftp_urls = version_xml_url.get("sftpUrls",{})
                    if sftp_urls:
                        # 返回第一个键值对的值
                        return next(iter(sftp_urls.values()), '')
                return ''
            logger.debug(f"Current status: {exe_info.data.status}, continuing to poll...")
            return False  # 继续重试，直到成功或失败

        try:
            return get_execution_status()
        except Exception as e:
            logger.error(f"Failed to get env xml url,env_id = {env_id}: {str(e)}")
            raise

    def download_xml(self,env_id,tar_name,url):
        cur_path = os.getcwd()
        nr_version = f"{env_id}_{tar_name}.xml"
        save_path = os.path.join(cur_path,nr_version)
        print(save_path)
        try:
            response = requests.get(url,verify=False,timeout=60)
            response.raise_for_status()
            os.makedirs(os.path.dirname(save_path),exist_ok=True)
            with open(save_path, "wb") as f:
                f.write(response.content)

            print(f"文件已成功保存到: {save_path}")
        except requests.exceptions.RequestException as e:
            print("下载失败：", e)

    def upload_file_to_ftp(self, local_path, remote_path):
        server_ip = "**********"
        server_user = "root"
        server_password = "TD-test123."
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        try:
            # Connect to the server
            ssh.connect(server_ip, username=server_user, password=server_password)

            # Open an SFTP session
            sftp = ssh.open_sftp()
            sftp.put(local_path, remote_path)
            print(f"File uploaded successfully to {remote_path}")
            os.remove(local_path)

            # Close the SFTP session and SSH connection
            sftp.close()
            ssh.close()
            return True
        except Exception as e:
            print(f"File uploaded successfully to str{e}")
            return False
    def compare_versions(self, version1, version2):
        """
        比较两个版本字符串的大小，使用简化的字符串处理方法

        支持格式:
        - NR-V4.20.20.10F10R03_241127211
        - NR-V4.20.20.10F10R03

        返回值:
        -1: version1 < version2
         0: version1 = version2
         1: version1 > version2
        """
        # 分离基础版本和时间戳
        v1_base = version1.split('_')[0]
        v2_base = version2.split('_')[0]

        # 如果基础版本不同，直接比较
        if v1_base != v2_base:
            # 提取R后面的部分进行比较
            r1_part = v1_base.split('R')[1] if 'R' in v1_base else ""
            r2_part = v2_base.split('R')[1] if 'R' in v2_base else ""

            # 如果R后面的数字可以直接比较
            if r1_part.isdigit() and r2_part.isdigit():
                r1_num = int(r1_part)
                r2_num = int(r2_part)
                return -1 if r1_num < r2_num else (1 if r1_num > r2_num else 0)

            # 如果不能直接比较数字，回退到字符串比较
            return -1 if v1_base < v2_base else (1 if v1_base > v2_base else 0)

        # 如果基础版本相同，但一个有时间戳一个没有
        if '_' in version1 and '_' not in version2:
            return 1  # 有时间戳的版本较大
        if '_' not in version1 and '_' in version2:
            return -1  # 没有时间戳的版本较小

        # 如果都有或都没有时间戳，直接比较完整字符串
        return -1 if version1 < version2 else (1 if version1 > version2 else 0)



if __name__ == "__main__":
    se = Service("","","","",EsVersionExecute())
    # backup_version = se.get_bs_backup_version("RAN3-上海高频CI团队-VAT1017")
    # print(backup_version)
    xml = se.get_env_xml_url("RAN3-上海高频CI团队-VAT1021")
    print(xml)
    # test_cases = [
    #     ("NR-V4.20.20.10F10R03_241127211", "NR-V4.20.20.10F10R05_2411292332"),
    #     ("NR-V4.20.20.10F10R03", "NR-V4.20.20.10F10R05"),
    #     ("NR-V4.20.20.10F10R05", "NR-V4.20.20.10F10R05_2411292332"),
    #     ("NR-V4.20.20.10F10R05_2411292332", "NR-V4.20.20.10F10R05_2411292333"),
    #     ("NRV4.30.10.00B210-2_2506032331", "NRV4.30.10.00B210-2_2506032332"),
    #     ("NR-V4.20.20.10F10R05", "NR-V4.20.20.10F11R03")
    # ]
    #
    # for v1, v2 in test_cases:
    #     result = se.compare_versions(v1, v2)
    #     print(f"比较 {v1} 和 {v2}: {result} ({'小于' if result < 0 else '大于' if result > 0 else '等于'})")