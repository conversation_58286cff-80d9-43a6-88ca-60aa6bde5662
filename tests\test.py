from domain.model.dto.env_info import VersionEnvInfo
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from service.jenkins_job_service import JenkinsJobService
from service.env_service import VersionEnvService
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from domain.repository.es_env import EsJenkinsServerEnv, EsPcEnv, EsVersionEnv, EsDeviceEnv
from domain.repository.version_execute import EsVersionExecute
from domain.repository.es_jenkins_job import <PERSON>sJenkinsJob
from domain.repository.agents_log import EsAgentsLog
from domain.model.dto.logdto import LogJobInfo
from domain.repository.cron_task import EsCronTask
from domain.repository.es_env import EsPcEnv
from domain.model.dto.env_info import PcEnvInfo, DeviceEnvInfo
from datetime import datetime
from infrastructure.utils.json2 import marshal
from infrastructure.utils.Env import Env

msg = VersionEnvInfo(
    env_id="RAN3-T17-10466",
    last_success_version="NR-V4.20.20.20MainR107_2504071834",
    curr_version="NR-V4.20.20.20MainR107_2504071834",
    jenkins_job_name="1003-下行udp灌包流量不足.html",
    jenkins_build_number="",
    version_branch="",
    version_test_result="True",
    full_path="",
    task_id="",
    version_list=""
)

service_msg_dict = {
    "env_id": "1013",
    "test_uc_tag": "CI1_SH_HF_V3_MAIL_1013_test_rebuild",
    "test_work_dir": r"D:\workspace\smoke-1013-dailybuild\script_v3\5GNR",
    "test_testcase_dir": r"D:\workspace\smoke-1013-dailybuild\script_v3\5GNR\test\testcases\NR3_17_高频",
    "jenkins_log_name": "1003-下行udp灌包流量不足.html",
    "jenkins_job_name": "smoke123-1013-dailybuild_test_rebuild",
    "jenkins_build_number": "56",
    "version_branch": "",
    "full_path": "https://artsz.zte.com.cn/artifactory/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Main/NR-V4.20.20.20MainR107/NR-V4.20.20.20MainR107_2504072300",
    "version_test_result": "True"
}

msg1 = LogJobInfo(
    log_name="smoke123-1013-dailybuild_test_rebuild",
    job_name="smoke123-1013-dailybuild_test_rebuild",
    build_number="56",
    env_id="RAN3-T17-10466",
    task_id="11111",
    service_type="local_test"
)

pc_info = PcEnvInfo(pc_ip='**************',
                    pc_username="admin",
                    pc_password='Devops_123!!!',
                    env_id="1013")

if __name__ == '__main__':
    Document.esConn = Elasticsearch(
        hosts=[{
            'host': Env.get_config().ES.ip,
            'port': Env.get_config().ES.port
        }],
        maxsize=1000,
        http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
        sniff_on_start=False,
        sniff_on_connection_fail=False,
        retry_on_timeout=True,
        max_retries=2,
        timeout=30,
        sniff_timeout=60
    )

    # task_list = EsCronTask().query_by_filter_or_without_sort({"state.keyword": ["running", "completed"]})
    # for temp in task_list[1]:
    #     for k, v in temp.items():
    #         if k == "state": print(v)
    # VersionEnvService().save_version_info(msg)
    # EsVersionEnv().delete(id="2025-04-08T19:46:18.466978")
    # EsCronTask().delete(id="2025-04-09T11:25:43.819254")
    # EsPcEnv().delete(id="1743577249")
    EsVersionExecute().delete(id="2025-06-09T17:01:00.364539")

    # EsPcEnv().index(id=datetime.now().isoformat(), body=eval(marshal(pc_info)))
    # EsDeviceEnv().index(id=datetime.now().isoformat(), body=eval(marshal(device)))
    # aa = JenkinsJobInfo(**service_msg_dict)
    # JenkinsJobService(service_msg_dict).save_jenkins_job()

    # msg = JenkinsJobInfo(
    #     env_id="RAN3-T17-10466",
    #     test_uc_tag="UC-CODE-XYZ",
    #     test_work_dir="/path/to/workdir",
    #     test_testcase_dir="/path/to/testcases",
    #     jenkins_log_name="1003-下行udp灌包流量不足.html",
    #     jenkins_job_name="smoke123-1013-dailybuild_test_rebuild",
    #     jenkins_build_number="56",
    #     service_type="jenkins_job_save",
    #     version_branch="",
    #     full_path="https://artsz.zte.com.cn/artifactory/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Main/NR-V4.20.20.20MainR107/NR-V4.20.20.20MainR107_2504072300",
    #     version_test_result="False"
    # )
    # print(msg)
    # VersionEnvService().save_version_env_info(msg)
