from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from datetime import datetime
from domain.model.dto.corn_task import CornTask
from infrastructure.utils.json2 import marshal

@Singleton
class EsCronTask(Document):

    def __init__(self):
        Document.__init__(self, 'cron_task', 'cron_task')

    def query_running_task(self):
        result = self.query_by_filter_without_time({"state": "running"})
        return result[1]

    def save_corn_task(self, document: CornTask):
        return self.index(id=datetime.now().isoformat(), body=eval(marshal(document)))


if __name__ == "__main__":
    from infrastructure.utils.Env import Env
    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)

    body = {"service_type": "local_retest", "subtype": "first_time", "state": "running",
            "needed": {"log_path": "xxx", "log_name": "aaa", "current_version": "ccc", "aim_version": "",
                       "timestamp": datetime.now().isoformat()}}
    task = EsCronTask()
    # task.index(id=datetime.now().isoformat(),body=body)
    # task.index(**body)
    result = task.query_by_filter_without_time({"state": ""})
    print(result)
    # task.query_all()
