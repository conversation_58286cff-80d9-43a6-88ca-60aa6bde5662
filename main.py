from apscheduler.schedulers.background import BackgroundScheduler

from fastapi import FastAPI

import asyncio
from elasticsearch import Elasticsearch

from api.Router import init_router
from api.north.env_info.router import init_env_router
from infrastructure.db.elastic_search.elastic_search import Document
from infrastructure.utils.Env import Env
from infrastructure.logger.logger import logger
from api.middleware import init_middlewire

app = FastAPI(
    title="CI Agents服务",
    description="基于 FastAPI 的CI Agents服务",
    version="1.0.0"
)

init_middlewire(app)
init_router(app)
init_env_router(app)


# 可以根据需要添加更多的 REST 接口，比如 POST, PUT, DELETE 等

async def listen():
    from ci_agents_service.__main__ import main
    asyncio.create_task(main())
    # 保持 listen 函数运行，防止主循环结束
    while True:
        await asyncio.sleep(1)


# 初始化CronTaskService并添加定时任务
def init_scheduler():
    from service.run_service.cron_task_service import CronTaskService
    cron_task_service = CronTaskService()
    scheduler = BackgroundScheduler()
    # 添加定时任务，每5分钟执行一次handle_running_task方法
    scheduler.add_job(
        cron_task_service.handle_running_task,
        'interval',
        minutes=5,
        id='handle_running_task'
    )

    # 启动调度器
    scheduler.start()
    logger.info("Scheduler started with handle_running_task job (every 5 minutes)")


def exe_main():
    logger.info("app start!")
    # init_scheduler()
    import asyncio, uvicorn
    # 将 uvicorn.run 放在一个单独的协程中
    loop = asyncio.get_event_loop()
    tasks = [listen(), asyncio.to_thread(uvicorn.run, app, host="0.0.0.0", port=3303, access_log=False)]
    try:
        loop.run_until_complete(asyncio.gather(*tasks))
    except KeyboardInterrupt:
        logger.info("exe_main KeyboardInterrupt!")
        pass
    finally:
        logger.info("exe_main close!")
        loop.close()


if __name__ == '__main__':
    exe_main()

