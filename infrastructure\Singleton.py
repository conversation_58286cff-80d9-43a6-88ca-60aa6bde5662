# thread unsafe
import threading
from functools import wraps


def Singleton(cls):
    _instance = {}

    def _singleton(*args, **kargs):
        k = f'{cls}{args}{kargs}'
        if k not in _instance:
            _instance[k] = cls(*args, **kargs)
        return _instance[k]

    return _singleton


def SingletonPro(cls):
    _i = dict()
    _i_l = threading.Lock()

    @wraps(cls)
    def _wrapper(*args, **kwargs):
        if cls not in _i:
            with _i_l:
                if cls not in _i:
                    _i[cls] = cls(*args, **kwargs)
        return _i[cls]

    return _wrapper
