export default {
  path: "/dev",
  redirect: "/dev/table",
  meta: {
    icon: "ri/information-line",
    // showLink: false,
    title: "调试页面",
    rank: 9
  },
  children: [
    {
      path: "/dev/table",
      name: "table",
      component: () => import("@/views/dev/table.vue"),
      meta: {
        title: "统计报表"
      }
    },
    {
      path: "/dev/unknown",
      name: "unknown",
      component: () => import("@/views/dev/unknown.vue"),
      meta: {
        title: "待开发页面"
      }
    }
  ]
} satisfies RouteConfigsTable;
