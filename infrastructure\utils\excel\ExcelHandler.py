import logging
import os
import re
import xlrd
import xlwt
import xlutils

from xlutils import copy
from xlutils.filter import process, XLRDReader, XLWTWriter


class ExcelHandler:

    def __init__(self, path):
        self._rdWorkbook = None
        self._isContentChanged = False
        self._fileName = self._handle_file_path(path)
        self._read_sheet_ptr = 0
        self._read_row_ptr = 1
        # self._load_formatted_list()
        self._reload_excel_file_for_read()
        self._formatting_info = True

    def _handle_file_path(self, path):
        matchRes = re.match('^[a-g]:', path.lower())
        if matchRes is not None:
            return path
        currentDir = os.path.dirname(os.path.realpath(__file__))
        return os.path.join(currentDir.split('testlib5g')[0], path)

    def open_exist_excel_file(self):
        if not os.path.exists((self._fileName)):
            raise Exception('%s is not exist' % self._fileName)
        try:
            self._rdWorkbook = xlrd.open_workbook(
                filename=self._fileName, formatting_info=self._formatting_info)
        except IOError as e1:
            raise Exception('Excel is abnormal in setup, err={0}'.format(e1))
        return True

    def create_empty_excel_file(self, sheetNameList=None):
        temWorkbook = xlwt.Workbook()
        if sheetNameList is None:
            sheetNameList = ['Sheet1', 'Sheet2', 'Sheet3']
        for sheetName in sheetNameList:
            if sheetName != "":
                temWorkbook.add_sheet(sheetName, cell_overwrite_ok=True)
        self._create_path_if_not_exist(self._fileName)
        temWorkbook.save(self._fileName)
        logging.info('create excel file: {0}'.format(self._fileName))
        return True

    def _load_formatted_list(self):
        try:
            currentDir = os.path.dirname(os.path.realpath(__file__))
            formatterFileDir = os.path.join(currentDir.split('testlib5g')[
                                                0],
                                            r'testlib5g\app_service\basic\aau\excel_template_file\FormatterBox.xls')
            rdWorkbook = xlrd.open_workbook(
                filename=formatterFileDir, formatting_info=True, on_demand=True)
            self._rbs = rdWorkbook.get_sheet(0)
            self._style = self.user_copy_format(rdWorkbook)
        except Exception as e1:
            raise Exception('Excel is abnormal in  reading setup, err={0}'.format(e1))

    def _create_path_if_not_exist(self, fileName):
        index = fileName.rfind('\\')
        if index <= 0:
            index = fileName.rfind('/')
        if index > 0:
            fileNametemp = fileName[0:index]
            if os.path.exists((fileNametemp)):
                return
            os.makedirs(fileNametemp)

    def _reload_excel_file_for_read(self, formatting_flag=False):
        if (self._rdWorkbook is None or self._isContentChanged is True) and (os.path.exists(self._fileName)):
            try:
                self._rdWorkbook = xlrd.open_workbook(
                    filename=self._fileName, formatting_info=formatting_flag)
                self._isContentChanged = False
            except IOError as e1:
                raise Exception('Excel is abnormal in reading reload, err={0}'.format(e1))

    def _reload_excel_file_for_write(self):
        try:
            self._rdWorkbook = xlrd.open_workbook(
                filename=self._fileName, formatting_info=self._formatting_info)
            self._wrWorkbook = xlutils.copy.copy(self._rdWorkbook)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing reload, err={0}'.format(e1))

    def read_excel_cell_value(self, sheetName, row, col):
        self._reload_excel_file_for_read()
        currentSheet = self._get_current_sheet_obj(sheetName, 'read')
        return currentSheet.cell_value(int(row), int(col))

    def read_excel_cell_value_by_position(self, sheetName, positionInfo):
        row, col = self._get_row_col_num_by_position_info(positionInfo)
        return self.read_excel_cell_value(sheetName, row, col)

    def _get_row_col_num_by_position_info(self, positionInfo):
        row = re.search('[A-Z]+(\d+)', positionInfo).group(1)
        row = int(row) - 1
        colLetters = re.search('([A-Z]+)\d+', positionInfo).group(1)
        col = self._convert_letters_into_nums(colLetters)
        return row, col

    def _convert_letters_into_nums(self, letters):
        retNum, bitIndx, length = 0, 1, len(letters)
        for letter in letters:
            num = ord(letter) - ord('A') + 1
            num = num * pow(26, length - bitIndx)
            retNum = retNum + num
            bitIndx = bitIndx + 1
        return retNum - 1

    def read_excel_row_values(self, sheetName, row):
        self._reload_excel_file_for_read()
        currentSheet = self._get_current_sheet_obj(sheetName, 'read')
        return currentSheet.row_values(int(row))

    def read_excel_col_values(self, sheetName, col):
        self._reload_excel_file_for_read()
        currentSheet = self._get_current_sheet_obj(sheetName, 'read')
        return currentSheet.col_values(int(col))

    def _get_current_sheet_obj(self, sheetName, optType='read'):
        try:
            if optType == 'read':
                currentSheet = self._rdWorkbook.sheet_by_name(sheetName)
            else:
                if isinstance(sheetName, int):
                    currentSheet = self._wrWorkbook.get_sheet(sheetName)
                else:
                    index = self._wrWorkbook._Workbook__worksheet_idx_from_name.get(sheetName.lower())
                    currentSheet = self._wrWorkbook.get_sheet(index)
        except Exception as error:
            raise Exception('Excel get worksheet fail, err={0}'.format(error))
        return currentSheet

    def _get_current_sheet_info(self, sheetName):
        self._reload_excel_file_for_read()
        currSheet = self._get_current_sheet_obj(sheetName, 'read')
        self._currHeads = currSheet.row_values(0)
        self._currRowNum = currSheet.nrows
        return True

    def _get_result_and_format(self, result):
        if result is True:
            return 'Pass', self._get_style(1)
        else:
            return 'Fail', self._get_style(2)

    def write_block_data(self, sheetName, dataList):
        if dataList is None or len(dataList) == 0:
            return
        self._get_current_sheet_info(sheetName)
        self._reload_excel_file_for_write()
        currentSheet = self._get_current_sheet_obj(sheetName, 'write')
        for item in dataList:
            self._write_one_row_data(currentSheet, item)
        self._wrWorkbook.save(self._fileName)
        self._isContentChanged = True

    def _write_one_row_data(self, worksheet, dictData):
        for (key, value) in dictData.items():
            if key not in self._currHeads:
                continue
            col = self._currHeads.index(key)
            if key == 'result':
                testRes, resFormat = self._get_result_and_format(value)
                worksheet.write(self._currRowNum, col, testRes, resFormat)
            else:
                worksheet.write(self._currRowNum, col, value)
        self._currRowNum = self._currRowNum + 1

    def _get_style(self, row):
        try:
            style = self._style[self._rbs.cell_xf_index(int(row), 0)]
        except Exception as e1:
            raise Exception('Excel is abnormal in getting format, row={0}, err="{3}"'.format(row, e1))
        return style

    def write_excel_cell_value(self, sheetName, row, col, newValue, formatRow=0):
        self._reload_excel_file_for_write()
        currentSheet = self._get_current_sheet_obj(sheetName, 'write')
        style = self._get_style(formatRow)
        try:
            currentSheet.write(int(row), int(col), newValue, style)
            self._wrWorkbook.save(self._fileName)
            self._isContentChanged = True
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, row={0}, col={1}, Value={2}, err="{3}"'.format(
                row, col, newValue, e1))
        return True

    def write_excel_row_value(self, sheetName, row, start_column, value_list, formatRow=0):
        import time
        self._reload_excel_file_for_write()
        currentSheet = self._get_current_sheet_obj(sheetName, 'write')
        style = self._get_style(formatRow)
        try:
            col = int(start_column)
            for value in value_list:
                currentSheet.write(int(row), col, value, style)
                col += 1
            self._wrWorkbook.save(self._fileName)
            time.sleep(0.5)
            self._isContentChanged = True
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, row={0}, startcol={1}, Value={2}, err="{3}"'.format(
                row, start_column, value_list, e1))
        return True

    def user_copy_format(self, wb):
        w = XLWTWriter()
        process(XLRDReader(wb, 'unknown.xls'), w)
        return w.style_list

    def read(self, expect_dict={}):
        result = []
        for sheet in self._rdWorkbook.sheets():
            for rx in range(1, sheet.nrows):
                row_data = self._get_row_data_from_sheet(sheet, rx)
                if row_data and self._is_match_data(row_data, expect_dict):
                    result.append(row_data)
        return result

    def read_by_index(self, index):
        for sheet in self._rdWorkbook.sheets():
            if index >= sheet.nrows - 1:
                index -= sheet.nrows - 1
                continue
            return self._get_row_data_from_sheet(sheet, index + 1)

    def read_next(self):
        """
        :returns: A dictionary of data in one row.

        Will return None at end of sheets.
        """
        row_data = None
        while (not row_data):
            if self._read_sheet_ptr >= len(self._rdWorkbook.sheets()):
                return None
            row_data = self._get_row_data_from_sheet(self._rdWorkbook.sheets()[self._read_sheet_ptr],
                                                     self._read_row_ptr)
            self._read_row_ptr += 1
            self._format_read_ptr()
        return row_data

    def read_next_of_specified_sheel(self, sheelName):
        row_data = None
        while (not row_data):
            row_data = self._get_row_data_from_sheet(self._rdWorkbook.sheet_by_name(sheelName),
                                                     self._read_row_ptr)
            self._read_row_ptr += 1
        return row_data

    def _get_row_data_from_sheet(self, sheet, rx):
        row_data = {}
        if self._is_blank_row(sheet, rx):
            return None
        head_list = self._get_head(sheet)
        for elem_index in range(len(sheet.row_values(rx))):
            row_data[head_list[elem_index]] = sheet.row_values(rx)[elem_index]
        return row_data

    def _is_blank_row(self, sheet, rowNum):
        for cell in sheet.row_values(rowNum):
            if '' != cell:
                return False
        return True

    def _get_head(self, sheet):
        head_list = []
        if (sheet.nrows != 0):
            for cell_value in sheet.row_values(0):
                head_list.append(cell_value)
        return head_list

    def _is_match_data(self, data_of_row, expect_dict):
        for key, value in expect_dict.items():
            if key not in data_of_row or data_of_row[key] != value:
                return False
        return True

    def _format_read_ptr(self):
        if self._read_row_ptr >= self._rdWorkbook.sheets()[self._read_sheet_ptr].nrows:
            self._read_row_ptr -= self._rdWorkbook.sheets(
            )[self._read_sheet_ptr].nrows - 1
            self._read_sheet_ptr += 1

    def get_worksheet_row_col_num(self, sheetName):
        self._reload_excel_file_for_read()
        currentSheet = self._get_current_sheet_obj(sheetName, 'read')
        return (currentSheet.nrows, currentSheet.ncols)

    def read_excel_sheet_names(self):
        self._reload_excel_file_for_read()
        return self._rdWorkbook.sheet_names()

    def set_excel_col_width(self, col, colWidth, sheetIdx=1):
        self._reload_excel_file_for_write()
        currentSheet = self._get_current_sheet_obj(sheetIdx, 'write')
        currentSheet.col(col).width = 256 * colWidth
        self._wrWorkbook.save(self._fileName)
