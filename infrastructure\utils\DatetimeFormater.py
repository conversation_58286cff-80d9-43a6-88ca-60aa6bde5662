'''
Created on Mar 6, 2017

@author: 10193332
'''

from datetime import datetime, timedelta
BJ_FORMAT = "%Y-%m-%d %H:%M:%S"


def string_to_datetime(string, date_format):
    return datetime.strptime(string, date_format)


def datetime_to_string(datetime, date_format):
        return datetime.strftime(date_format)


def now():
    return datetime_to_string(datetime.now(), BJ_FORMAT)


def get_current_time():
    now = datetime.utcnow()
    bjs = now + timedelta(hours=8)
    return bjs.strftime(BJ_FORMAT)
