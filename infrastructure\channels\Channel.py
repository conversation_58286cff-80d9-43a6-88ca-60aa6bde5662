from abc import ABC, abstractmethod


class Channel(ABC):

    def __init__(self, host, port, name):
        self._tn = None
        self._name = name
        self._host = host
        self._port = port
        self._id = self._name + self._host + str(self._port)

    def get_id(self):
        return self._id

    @abstractmethod
    def get_status(self):
        pass

    @abstractmethod
    def connect(self, *args):
        pass

    @abstractmethod
    def disconnect(self, *args):
        pass

    @abstractmethod
    async def clear_buff(self, *args):
        pass

    async def execute_cmd(self, cmdObj):
        if cmdObj.command is not None:
            cmdObj = await self.write(cmdObj)
        if cmdObj.expected is not None and cmdObj.cmd_result.result:
            cmdObj = await self.read(cmdObj)
        return cmdObj.cmd_result


if __name__ == '__main__':
    class test(Channel):
        def __init__(self, host, port, name):
            super().__init__(host, port, name)

        def get_status(self):
            print('11111')

        def read(self, cmdObj):
            return super().read(cmdObj)

    test('111', '2222', '333')
