import pymysql
from dbutils.pooled_db import PooledDB


class DB_Pool(object):
    def __init__(self, db_conf):
        self._pool = PooledDB(
            creator=pymysql,  # 使用链接数据库的模块
            maxconnections=6,  # 连接池允许的最大连接数，0和None表示不限制连接数
            mincached=1,  # 初始化时，链接池中至少创建的空闲的链接，0表示不创建
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
            ping=0,
            # ping MySQL服务端，检查是否服务可用。如：0 = None = never, 1 = default = whenever it is requested, 2 = when a cursor is created, 4 = when a query is executed, 7 = always
            host=db_conf["host"],
            port=db_conf["port"],
            user=db_conf["user"],
            password=db_conf["password"],
            database=db_conf["db"],
            charset='utf8',
            connect_timeout=10,
            read_timeout=10,
            write_timeout=10,
            cursorclass=pymysql.cursors.DictCursor
        )

    def __new__(cls, *args, **kw):
        if not hasattr(cls, '_instance'):
            cls._instance = object.__new__(cls)
        return cls._instance

    @property
    def pool(self):
        return self._pool


class DBMysql(object):
    def __init__(self, db_conf):
        self.db_pool = DB_Pool(db_conf).pool

    def open(self):
        self.conn = self.db_pool.connection()
        self.cursor = self.conn.cursor()

    def close(self):
        self.cursor.close()
        self.conn.close()

    def fetch(self, table_name=None, fields=(), where=None, many=False):
        self.open()
        if where:
            sql = f'select {",".join(fields)} from {table_name} where {where}'
        else:
            sql = f'select {",".join(fields)} from {table_name}'
        self.cursor.execute(sql)
        if many:
            data = self.cursor.fetchmany()
        else:
            data = self.cursor.fetchone()
        self.close()
        return data
