import redis
from config.DbCfg import REDIS_DB
from infrastructure.Singleton import Singleton

dbCfg = REDIS_DB


@Singleton
class RedisClientPool(object):
    def __init__(self):
        self._host = dbCfg["ip"]
        self._port = dbCfg["port"]
        self._password = dbCfg["password"]
        self._pool = None
        self._client = None

    def get_client(self):
        if self._client is None:
            return redis.StrictRedis(connection_pool=self._get_pool())
        return self._client

    def _get_pool(self):
        if self._pool is None:
            self._connect()
        return self._pool

    def _connect(self):
        pool = redis.ConnectionPool(host=self._host, port=self._port, password=self._password,
                                    socket_connect_timeout=5,
                                    socket_timeout=5,
                                    max_connections=10,
                                    decode_responses=True,
                                    retry_on_timeout=True)
        self._pool = pool


pool: RedisClientPool = RedisClientPool()
redisClient = pool.get_client
