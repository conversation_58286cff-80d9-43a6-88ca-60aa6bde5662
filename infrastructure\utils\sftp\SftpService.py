from infrastructure.utils.Env import Env
from infrastructure.utils.sftp.Sftp import Sftp


class SftpService(object):

    def __init__(self, sftp_conf={}):
        if not sftp_conf:
            sftp_conf = Env.get_config().ENV_SFTP.model_dump()
        self._sftp = Sftp(sftp_conf)

    def upload(self, locale_path, remote_path, new_name=None):
        return self._sftp.upload_multi(locale_path, remote_path, new_name=new_name)

    def download_file(self, remote_path, locale_path):
        return self._sftp.download_file(remote_path, locale_path)


if __name__ == "__main__":
    conf_dict = {
        "host": "************",
        "port": 22,
        "user": "sftpuser",
        "pwd": "Sftp@132132"
    }
    sftp = Sftp(conf_dict)
    sftp.upload_muti(r"D:\Users\Desktop\0530\1", "/sftpuser/test")
