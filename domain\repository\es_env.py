from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document


@Singleton
class EsJenkinsServerEnv(Document):

    def __init__(self):
        Document.__init__(self, 'jenkins_server_env', 'jenkins_server_env')


@Singleton
class EsPcEnv(Document):

    def __init__(self):
        Document.__init__(self, 'pc_env', 'pc_env')


@Singleton
class EsBizCoreEnv(Document):

    def __init__(self):
        Document.__init__(self, 'biz_core_env', 'biz_core_env')


@Singleton
class EsVersionEnv(Document):

    def __init__(self):
        Document.__init__(self, 'version_env', 'version_env')

    def query_version_list(self, task_id):
        result = self.query_by_filter_without_sort({"task_id": task_id})
        return result[1]


@Singleton
class EsDeviceEnv(Document):

    def __init__(self):
        Document.__init__(self, 'device_env', 'device_env')
