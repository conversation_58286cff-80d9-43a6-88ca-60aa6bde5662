from domain.repository.cron_task import EsCronTask
from domain.repository.es_jenkins_job import <PERSON>s<PERSON><PERSON>kinsJ<PERSON>
from domain.repository.es_env import <PERSON>sJenkinsServerEnv, EsBizCoreEnv, EsPcEnv
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from domain.service.jenkins_client import JenkinsClient
from infrastructure.channels.ssh.execute_cmd import execute_remote_command
import logging
import threading

from service.logging_service import LoggingService
from service.tools.EmailService import EmailService, EmailData
from domain.model.dto.corn_task import *

class LocalTestJobService:
    def __init__(self, service_msg_dict):
        self._es_jenkins_job = EsJenkinsJob()
        self._es_pc_env = EsPcEnv()
        self.document = service_msg_dict

    def get_jenkins_job(self):
        result = self._es_jenkins_job.query_by_filter_sort(
            {"env_id": self.document.get("env_id", ""), "jenkins_job_name": self.document.get("job_name", "")},
            "timestamp")
        jenkins_job_list = result[1]
        if jenkins_job_list:
            job_info = jenkins_job_list[0]
        else:
            data = EmailData(
                subject="本地复测执行",
                content="没有环境JOB信息",
                recipients=["<EMAIL>"],
                cc=[]
            )
            EmailService().email_send(data)
            raise Exception("没有环境JOB信息")
        return job_info

    def get_env_device_info(self):
        result = self._es_pc_env.query_by_filter_without_sort({"env_id": self.document.get("env_id")})
        pc_info_list = result[1]
        if pc_info_list:
            pc_info = pc_info_list[0]
        else:
            data = EmailData(
                subject="本地复测执行",
                content="没有环境JOB信息",
                recipients=["<EMAIL>"],
                cc=[]
            )
            EmailService().email_send(data)
            raise Exception("没有环境PC信息")
        return pc_info

    async def run(self):
        # 数据库获取cmd信息拼接；ssh信息数据库读取
        job_info = self.get_jenkins_job()
        print(f"xxxxxxxxxxxxxxx {job_info}")
        uc_tag = job_info.get("test_uc_tag")
        work_dir = job_info.get("test_work_dir")
        testcase_dir = job_info.get("test_testcase_dir")
        log_path, log_name = self.get_log_info(job_info)
        from service.run_service.cron_task_service import CronTaskService
        CronTaskService().save_local_test_task(job_info, self.document, log_path, log_name)
        log_file = f"{log_path}/{log_name}"
        cmd = f"pybot --loglevel TRACE --include {uc_tag} --log {log_file} -d {log_path} -P {work_dir} {testcase_dir}"
        pc_info = self.get_env_device_info()
        hostname = pc_info.get("pc_ip")
        username = pc_info.get("pc_username")
        password = pc_info.get("pc_password")
        logging.warning(execute_remote_command(hostname, 22, username, password, cmd))
        thread = threading.Thread(target=lambda: execute_remote_command(hostname, 22, username, password, cmd))
        thread.daemon = True  # Make the thread a daemon thread
        thread.start()
        print("ending")

    def get_log_info(self, job_info):
        env_id = self.document.get("env_id")
        task_id = self.document.get("task_id")
        log_path = job_info.get("test_work_dir")
        log_name = env_id + "_" + task_id + ".html"
        return log_path, log_name


class OnlineTestJobService:
    def __init__(self, service_msg_dict):
        self._es_jenkins_server_info = EsJenkinsServerEnv()
        self._es_biz_core_env = EsBizCoreEnv()
        self.document = service_msg_dict
        self.env_id = service_msg_dict.get("env_id", "")
        self.job_name = service_msg_dict.get("job_name", "")
        self.build_number = service_msg_dict.get("build_number", "")
        self.task_id = service_msg_dict.get("task_id", "")
        self._es = EsCronTask()
        self.subtype = service_msg_dict.get("subtype", "normal")
    def get_jenkins_loginfo(self):
        result = self._es_jenkins_server_info.query_all_without_time()
        print(result)
        return result

    def match_login_info(self):
        result = self._es_biz_core_env.query_by_filter_without_sort({"env_id": self.env_id})
        print(result)
        return result

    def get_jenkins_client(self):
        all_loginfo = self.get_jenkins_loginfo()
        env_biz_info = self.match_login_info()
        try:
            jenkins_url = env_biz_info[1][0].get("jenkins_url","")
        except:
            data = EmailData(
                subject="线上复测问题失败",
                content=f"没有维护{self.env_id}对应的biz_core表信息",
                recipients=["<EMAIL>"],
                cc=["<EMAIL>"]
            )
            EmailService().email_send(data)
            return
        username = ""
        password = ""
        for loginfo in all_loginfo[1]:
            if loginfo.get("jenkins_url", "") == jenkins_url:
                username = loginfo.get("jenkins_username")
                password = loginfo.get("jenkins_password")
        print("user_name~~~~", jenkins_url, username, password)
        return JenkinsClient(jenkins_url=jenkins_url, username=username, password=password)

    async def run(self):
        all_loginfo = self.get_jenkins_loginfo()
        env_biz_info = self.match_login_info()
        jenkins_url = env_biz_info[1][0].get("jenkins_url", "")
        username = ""
        password = ""
        for loginfo in all_loginfo[1]:
            if loginfo.get("jenkins_url", "") == jenkins_url:
                username = loginfo.get("jenkins_username")
                password = loginfo.get("jenkins_password")
        js = JenkinsClient(
            jenkins_url,
            username,
            password
        )
        param = js.get_build_parameters(self.job_name, self.build_number)
        # if self.subtype == "normal":
        #     param.update({"trigger_downstream":"false"})
        # else:
        #     param.update({"trigger_downstream": "true"})
        param.update({"task_id":self.task_id})
        next_build_number = js.trigger_job(self.job_name, param)

        needed = Needed(
            job_name=self.job_name,
            build_number=str(next_build_number),
        )

        corn_task = CornTask(
            task_id=self.task_id,
            env_id=self.env_id,
            needed=needed,
            service_type="online_test",
            state="running",
            subtype=self.subtype  # 指定需要本地复测
        )
        self._es.save_corn_task(corn_task)
        LoggingService.log_execution(
            service_type="online_test",
            operation="cron_task_" + "online_test",
            status="in_progress",
            task_id=self.task_id,
            env_id=self.env_id,
            details={"message": self.document},
            service_key="online_test"
        )

if __name__ == "__main__":
    from infrastructure.utils.Env import Env

    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)
    # document = {"env_id":"1013","jenkins_url":"smoke123-1013-dailybuild_test_rebuild"}
    # OnlineTestJobService(document).get_jenkins_client()
    # OnlineTestJobService(document).match_login_info()
    document = {"env_id": "1013"}
    LocalTestJobService(document).get_env_device_info()
