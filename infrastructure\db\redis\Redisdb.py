# coding=utf-8
'''
Created on 2018年12月14日

@author: 10111815
'''
import redis


class RedisClient(object):

    def __init__(self, dbCfg):
        self._host = dbCfg["ip"]
        self._port = dbCfg["port"]
        self._password = dbCfg["password"]
        self._redis = None

    def get_instance(self):
        if self._redis is None:
            return self._connect()
        return self._redis

    def _connect(self):
        pool = redis.ConnectionPool(host=self._host, port=self._port, password=self._password,
                                    socket_connect_timeout=5,
                                    socket_timeout=5,
                                    max_connections=10,
                                    decode_responses=True,
                                    retry_on_timeout=True)
        return redis.StrictRedis(connection_pool=pool)

    def subscribe(self, channel):
        client = self.get_instance()
        pubsub = client.pubsub(ignore_subscribe_messages=True)
        pubsub.subscribe(channel)
        return pubsub


class RedisDB(object):
    redisConn = None

    def __init__(self):
        self._redis = RedisDB.redisConn

    def set(self, *args, **kwargs):
        return self._redis.set(*args, **kwargs)

    def get(self, *args, **kwargs):
        return self._redis.get(*args, **kwargs)

    def hset(self, *args, **kwargs):
        return self._redis.hset(*args, **kwargs)

    def hget(self, *args, **kwargs):
        return self._redis.hget(*args, **kwargs)

    def rpush(self, *args, **kwargs):
        return self._redis.rpush(*args, **kwargs)

    def sadd(self, *args, **kwargs):
        return self._redis.sadd(*args, **kwargs)

    def smembers(self, *args, **kwargs):
        return self._redis.smembers(*args, **kwargs)

    def delete(self, *args, **kwargs):
        return self._redis.delete(*args, **kwargs)

    def hexists(self, *args, **kwargs):
        return self._redis.hexists(*args, **kwargs)

    def exists(self, *args, **kwargs):
        return self._redis.exists(*args, **kwargs)

    def publish(self, *args, **kwargs):
        return self._redis.publish(*args, **kwargs)

    def lpush(self, name, value):
        return self._redis.lpush(name, value)

    def lpop(self, name):
        return self._redis.lpop(name)

    def blpop(self, *args, **kwargs):
        return self._redis.blpop(*args, **kwargs)
