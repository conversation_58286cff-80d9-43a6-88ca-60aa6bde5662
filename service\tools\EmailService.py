import requests
from infrastructure.logger.logger import logger
from infrastructure.utils.HttpResponse import HttpResponse


class EmailData:
    def __init__(self, subject, content, recipients, cc):
        self.subject = subject
        self.content = content
        self.recipients = recipients
        self.cc = cc
class EmailService:
    _mail_api_url = "http://*************:9010/bizci/openapi/v1/mail"

    @staticmethod
    def email_send(data):
        try:
            body = {
                "subject": data.subject,
                "content": data.content,
                "recipients": data.recipients,
                "cc": data.cc
            }
            
            response = requests.post(
                url=EmailService._mail_api_url,
                json=body,
                timeout=30
            )
            
            response_data = response.json()
            http_response = HttpResponse(response_data)
            
            if http_response.result:
                logger.info(f"邮件发送成功: {data.subject}")
                return {"success": True, "message": "邮件发送成功"}
            else:
                error_msg = f"邮件发送失败: {http_response.failReason}"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}
                
        except Exception as e:
            error_msg = f"邮件发送异常: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}


if __name__ == "__main__":
    class EmailData:
        def __init__(self, subject, content, recipients, cc):
            self.subject = subject
            self.content = content
            self.recipients = recipients
            self.cc = cc
    data = EmailData(
    subject="本地复测执行",
    content="执行成功,service_type:first_time",
    recipients=["<EMAIL>"],
    cc=[]
)
    EmailService().email_send(data)