# coding=utf-8
from datetime import datetime
from time import sleep
import traceback
from elasticsearch import helpers
from infrastructure.Singleton import Singleton
from infrastructure.logger.logger import logger


class Document(object):
    esConn = None

    def __init__(self, index=None, docType=None):
        self._index = index
        self._docType = docType

    @property
    def _es(self):
        # Always get the latest connection from the class variable
        if Document.esConn is None:
            from elasticsearch import Elasticsearch
            from infrastructure.utils.Env import Env
            # Initialize Elasticsearch connection if not already done
            Document.esConn = Elasticsearch(
                hosts=[{
                    'host': Env.get_config().ES.ip,
                    'port': Env.get_config().ES.port
                }],
                maxsize=1000,
                http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                sniff_on_start=False,
                sniff_on_connection_fail=False,
                retry_on_timeout=True,
                max_retries=2,
                timeout=30,
                sniff_timeout=60
            )
        return Document.esConn

    def index(self, **kwargs):
        return self._es.index(index=self._index, doc_type=self._docType, refresh=True, **kwargs)

    def search(self, **kwargs):
        result = self._es.search(index=self._index, doc_type=self._docType,
                                 params={'request_timeout': 30, 'timeout': '30s'}, **kwargs)
        return result['hits']['total'], self.get_source(result)

    def search_return_all(self, **kwargs):
        response = self._es.search(index=self._index, doc_type=self._docType,
                                   params={'request_timeout': 30, 'timeout': '30s'}, **kwargs)
        # 提取查询结果中的 hits 部分
        hits = response.get('hits', {}).get('hits', [])

        # 处理每个命中的文档，确保返回 _id 和 _source
        results = []
        for hit in hits:
            result = {
                '_id': hit['_id'],  # 提取 _id
                '_source': hit['_source']  # 提取文档内容
            }
            results.append(result)

        return results

    def search_with_id(self, **kwargs):
        result = self._es.search(index=self._index, doc_type=self._docType,
                                 params={'request_timeout': 30, 'timeout': '30s'}, **kwargs)
        return result['hits']['total'], self.get_rawdatas(result)

    def search_rawdatas(self, **kwargs):
        result = self._es.search(index=self._index, doc_type=self._docType, size=10000, params={'request_timeout': 30},
                                 **kwargs)
        return result['hits']['total'], self.get_rawdatas(result)

    def search_all(self, queryDsl, scroll='5m', timeout="1m"):
        es_result = helpers.scan(
            client=self._es,
            query=queryDsl,
            scroll=scroll,
            index=self._index,
            doc_type=self._docType,
            timeout=timeout
        )
        return es_result

    def search_paras_agg_result(self, aggsParaNameList, filterList, isKeyWord=True):
        aggsDsl = self._generate_query_aggs_dsl(aggsParaNameList, isKeyWord)
        totalCounts, typeAndCountDic = self._get_para_agg_result(aggsDsl, filterList, aggsParaNameList)
        return totalCounts, typeAndCountDic

    def _get_para_agg_result(self, aggsDsl, filterList, aggsParaNameList):
        queryDsl = self._generate_aggs_query_dsl(filterList, aggsDsl)
        totalCounts, aggsResult = self.search_for_aggs(body=queryDsl)
        return totalCounts, self._parse_aggs_result(aggsResult, aggsParaNameList)

    def _parse_aggs_result(self, aggsResult, propNameList):
        typeAndCountDic = {}
        for propName in propNameList:
            propAgsDic = {}
            aggsResult = self._unicode_to_str(aggsResult)
            if '{0}_type'.format(propName) in aggsResult:
                for item in aggsResult['{0}_type'.format(propName)]['buckets']:
                    propAgsDic[item['key']] = item['doc_count']
                typeAndCountDic[propName] = propAgsDic
        return typeAndCountDic

    def _unicode_to_str(self, aggsResult):
        for v in aggsResult.keys():
            if isinstance(v, str):
                aggsResult[str(v)] = aggsResult[v]
        return aggsResult

    def _generate_aggs_query_dsl(self, filterList, aggsDsl):
        queryDsl = {
            "query": {"bool": {"filter": filterList}},
            "aggs": aggsDsl
        }
        return queryDsl

    def _generate_query_aggs_dsl(self, propNameList, isKeyWord):
        aggsPropDic = {}
        for propName in propNameList:
            aggPropName = "{0}.keyword".format(propName) if isKeyWord is True else propName
            aggsPropDic['{0}_type'.format(propName)] = \
                {"terms": {"field": "{0}".format(aggPropName),
                           "size": 1000,
                           "shard_size": 100,
                           "missing": "N/A"}
                 }
        return aggsPropDic

    def search_for_aggs_and_results(self, **kwargs):
        try:
            result = self._es.search(index=self._index, doc_type=self._docType, **kwargs)
            return result['hits']['total'], self.get_rawdatas(result), result['aggregations']
        except Exception as e:
            logger.info('es basic search_for_aggs failed! reason is {0}'.format(e))
            traceback.print_exc()
            return 0, {}, {}

    def search_for_aggs(self, **kwargs):
        try:
            result = self._es.search(index=self._index, doc_type=self._docType, **kwargs)
            return result['hits']['total'], result['aggregations']
        except Exception as e:
            logger.info('es basic search_for_aggs failed! reason is {0}'.format(e))
            traceback.print_exc()
            return 0, {}

    def search_all_by_filter(self, filters, includeFields=[]):
        queryDsl = {"query": {"bool": {"filter": filters}}}
        if includeFields:
            queryDsl.update({"_source": {"include": includeFields}})
        for item in self.search_all(queryDsl):
            yield item['_source']

    def get_rawdatas(self, resData):
        hit_source = resData['hits']['hits']
        rawdatas = []
        for hit in hit_source:
            id = hit["_id"]
            source = hit["_source"]
            dict = {"id": id}
            source.update(dict)
            rawdatas.append(source)
        return rawdatas

    def get_source(self, resData):
        return [hit["_source"] for hit in resData['hits']['hits']]

    def get_id(self, resData):
        return [hit["_id"] for hit in resData['hits']['hits']]

    def delete(self, **kwargs):
        try:
            self._es.delete(index=self._index, doc_type=self._docType, refresh=True, **kwargs)
        except Exception as e:
            logger.info('es basic delete failed! reason is {0}'.format(e))
            traceback.print_exc()
            return False
        return True

    def delete_by_query(self, **kwargs):
        try:
            self._es.delete_by_query(index=self._index, **kwargs)
        except Exception as e:
            logger.info('es basic delete_by_query failed! reason is {0}'.format(e))
            traceback.print_exc()
            return False
        return True

    def update(self, **kwargs):
        try:
            self._es.update(index=self._index, doc_type=self._docType, **kwargs)
        except Exception as e:
            logger.info('es basic update failed! reason is {0}'.format(e))
            traceback.print_exc()
            return False
        return True
    def update_by_query(self, query_dict:dict, filter:dict,fromId=0, size=50, flag=True):
        try:
            # 根据查询条件查找对应的文档
            mustMatch = []

            # 处理查询字典，为每个字段创建单独的match_phrase查询
            for key, value in query_dict.items():
                mustMatch.append({"match_phrase": {key: value}})

            queryDsl = {"query": {"bool": {"must": mustMatch}}, "from": fromId,
                        "size": size}

            # 执行查询操作，检查是否找到对应的文档
            search_results = self._es.search(index=self._index, body=queryDsl)

            # 根据 Elasticsearch 7.x 及以上版本的返回结构处理 total 字段
            total_hits = search_results['hits']['total']
            if isinstance(total_hits, dict):  # ES 7.x returns a dict with 'value' and 'relation'
                total_hits = total_hits['value']

            if total_hits > 0:
                # 如果找到了文档，获取第一个匹配的文档的 _id
                doc_id = search_results['hits']['hits'][0]['_id']

                # 执行更新操作
                update_body = {
                    "doc": filter
                }
                self._es.update(index=self._index, doc_type=self._docType, id=doc_id, body=update_body)
                logger.info(f"Updated document with query {query_dict} to {filter}")
            else:
                logger.warning(f"Update failed: No document found matching query {query_dict}")
        except Exception as e:
            logger.error(f'ES update by query failed! Query: {query_dict}, Reason: {e}')
            traceback.print_exc()
            return False

        return True
    def update_by_task_id(self, task_id, timestamp, filter:dict,fromId=0, size=50, flag=True):
        # 使用更通用的update_by_query方法
        return self.update_by_query({"task_id": task_id, "timestamp": timestamp}, filter, fromId, size, flag)

    def update_by_id(self, **kwargs):
        try:
            # 提供需要更新的数据
            # kwargs 需要包含文档的 _id 和其他字段信息
            for doc_id, update_fields in kwargs.items():
                # 每个 kwargs 条目应该包含文档 ID 和更新字段
                # doc_id 是文档的唯一标识符，update_fields 是需要更新的字段内容

                # 执行更新操作
                print(update_fields)
                self._es.update(index=self._index, doc_type=self._docType, id=doc_id, body={"doc": update_fields})

        except Exception as e:
            logger.info('ES basic update failed! Reason is {0}'.format(e))
            traceback.print_exc()
            return False
        return True

    def get(self, **kwargs):
        try:
            result = self._es.get(index=self._index, doc_type=self._docType, **kwargs)
            return result['_source']
        except Exception as e:
            logger.info('es basic get failed! reason is {0}'.format(e))
            traceback.print_exc()
            return None

    def exists(self, **kwargs):
        return self._es.exists(index=self._index, doc_type=self._docType, **kwargs)

    def query_all(self, fromId=0, size=50, flag=True, sortFlag='timestamp'):
        queryDsl = {"query": {"match_all": {}}, "sort": {sortFlag: {"order": "desc"}}, "from": fromId, "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_all_without_time(self, fromId=0, size=50, flag=True):
        queryDsl = {"query": {"match_all": {}}, "from": fromId, "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter_without_sort(self, filters, fromId=0, size=50, flag=True):
        mustMatch = []
        for key, value in filters.items():
            if isinstance(value, list):
                mustMatch.append({"terms": {key: value}})
            else:
                mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "from": fromId,
                    "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter_or_without_sort(self, filters, fromId=0, size=50, flag=True):
        should_match = []
        for key, value in filters.items():
            if isinstance(value, list):
                # 使用 terms 查询并添加 .keyword 后缀
                should_match.append({"terms": {f"{key}.keyword": value}})
            else:
                # 使用 term 查询并添加 .keyword 后缀
                should_match.append({"term": {f"{key}.keyword": value}})
        queryDsl = {
            "query": {
                "bool": {
                    "should": should_match,
                    "minimum_should_match": 1
                }
            },
            "from": fromId,
            "size": size
        }
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter_with_sort(self, filters, order_key, fromId=0, size=50, flag=True):
        mustMatch = []
        for key, value in filters.items():
            if isinstance(value, list):
                mustMatch.append({"terms": {key: value}})
            else:
                mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "sort": {order_key: {"order": "desc"}},"from": fromId,
                    "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter(self, filters, fromId=0, size=50, flag=True):
        mustMatch = []
        for key, value in filters.items():
            if isinstance(value, list):
                mustMatch.append({"terms": {key: value}})
            else:
                mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "sort": {"timestamp": {"order": "desc"}}, "from": fromId,
                    "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter_sort(self, filters, sort_field, fromId=0, size=10000, flag=True):
        mustMatch = []
        for key, value in filters.items():
            if isinstance(value, list):
                mustMatch.append({"terms": {key: value}})
            else:
                mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "sort": {sort_field: {"order": "desc"}}, "from": fromId,
                    "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def update_attr(self, uid, attrDict):
        attrDict.update({"timestamp": datetime.now()})
        return self.update(body={"doc": attrDict}, id=uid, refresh=True)

    def bulk(self, actions):
        try:
            helpers.bulk(self._es, actions, request_timeout=120)
        except Exception as e:
            logger.info('es basic bulk failed! reason is {0}'.format(e))
            traceback.print_exc()
            sleep(5)

    def get_indices(self):
        indices = self._es.cat.indices(params={"format": 'json', 'h': 'index', 'request_timeout': 120})
        return [indexInfo['index'] for indexInfo in indices]

    def query_by_filter_interval(self, filters, fromId=0, size=50, flag=True, sortFlag='timestamp'):
        mustMatch = []
        for key, value in filters.items():
            if isinstance(value, list):
                mustMatch.append({"terms": {key: value}})
            elif isinstance(value, dict):
                mustMatch.append({key: value})
            else:
                mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"filter": mustMatch, }}, "sort": {sortFlag: {"order": "desc"}}, "from": fromId,
                    "size": size}
        return self.search(body=queryDsl) if flag else self.search_with_id(body=queryDsl)

    def query_by_filter_prefix(self, filters, fromId=0, size=50, flag=True):
        mustMatch = [{"match_phrase_prefix": {key: value}} for key, value in filters.items()]
        queryDsl = {"query": {"bool": {"should": mustMatch}}, "sort": {"timestamp": {"order": "desc"}}, "from": fromId,
                    "size": size}
        return self.search(body=queryDsl)


@Singleton
class EsUsers(Document):

    def __init__(self):
        Document.__init__(self, 'users', 'user')

    def delete_user(self, workId):
        return self.delete(id=workId)

    def get_user(self, userId):
        return self.get(id=userId)

    def add_user(self, user):
        user.update({"timestamp": datetime.now()})
        result = self.index(body=user, id=user['loginName'])
        return result['result'] == 'created'

    def update_user(self, user):
        user.update({"timestamp": datetime.now()})
        return self.update(body={"doc_as_upsert": True, "doc": user}, id=user['loginName'], refresh=True)


@Singleton
class EsRawData(Document):

    def __init__(self):
        Document.__init__(self, 'raw_datas', 'raw_data')

    def update_raw_data(self, rawData):
        rawData.update({"timestamp": datetime.now()})
        return self.update(body={"doc_as_upsert": True, "doc": rawData}, id=rawData['dataId'], refresh=True)

    def delete_raw_data(self, dataId):
        return self.delete(id=dataId)
