import asyncio
from urllib.parse import urlencode

import httpx

from infrastructure.logger.logger import logger
from infrastructure.utils.ResilientRequests import ResilientRequests


class ContextRepository:

    def __init__(self):
        self.url = "https://zxmte.zte.com.cn:3303/tdl/biz"
        self.db = "context"

    async def query(self, condition):
        conditionStr = urlencode(condition)
        url = f"{self.url}?db={self.db}&{conditionStr}"
        async with httpx.AsyncClient(timeout=5, proxies={}) as client:
            try:
                response = await client.get(url=url, timeout=5)
                resp = response.json()
                if resp["result"]:
                    return resp["data"]
                else:
                    logger.error(f"查询错误, err: {resp['fail_reason']}")
            except Exception as err:
                logger.error(f"查询错误, err: {err}")
        return []

    async def query_one(self, condition) -> dict:
        res = await self.query(condition)
        if type(res) == list and len(res) > 0:
            return res[-1]
        return {}

    # 更新 condition 对应的 数据
    # @retries_on_exception(3, everyTryDelaySecs=5)
    async def update(self, data, condition):
        url = f"{self.url}?db={self.db}&opt=update"
        body = {"condition": condition, "data": data}
        response = ResilientRequests().request(url=url, method="POST", json=body, timeout=5)
        if response:
            logger.info(f"context更新成功")
            return response
        else:
            logger.error(f"context更新失败，稍后将自动重试")


if __name__ == '__main__':
    contextRepo = ContextRepository()
    res = asyncio.run(contextRepo.query_one({}))
    print(res)
