# -*-coding:utf-8-*-
import sys, os
sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))
from service.tools.ActionService import ActionService


class UmeService(object):
    @staticmethod
    def version_rollback(env_id, tar_path, group_no=""):
        action_params = {
            "actionId": "e31d6e00534e70f4027d6ed1c471a668",
            "para": {
                "opType": "回退",
                "tarPath": tar_path
            }
        }
        return ActionService.execute(env_id, action_params, group_no)

    @staticmethod
    def version_upgrade(env_id, tar_path, group_no=""):
        action_params = {
            "actionId": "e31d6e00534e70f4027d6ed1c471a668",
            "para": {
                "opType": "基站升级",
                "tarPath": tar_path
            }
        }
        return ActionService.execute(env_id, action_params, group_no)

    @staticmethod
    def get_mcs_and_bler(env_id, group_no=""):
        action_params = {
            "actionId": "86db0578995996b712f1ee580c393c83",
            "actionType": "composite"
        }
        return ActionService.execute(env_id, action_params, group_no)

    @staticmethod
    def get_current_alarms(env_id, group_no=""):
        action_params = {
            "actionId": "d7dd77b794b947a0e1457886d7db82a8"
        }
        return ActionService.execute(env_id, action_params, group_no)
    
    @staticmethod
    def get_bs_current_version(env_id, group_no=""):
        action_params = {
            "actionId": "201553593b68c4a9ef51431200c422e2",
            "actionType": "composite"
        }
        return ActionService.execute(env_id, action_params, group_no)


    @staticmethod
    def get_bs_backup_version(env_id, group_no=""):
        action_params = {
            "actionId": "808a3e0bfdc21c4cc6a5f8b64c83ab8a",
            "actionType": "composite"
        }
        return ActionService.execute(env_id, action_params, group_no)

    @staticmethod
    def get_env_xml_url(env_id, group_no=""):
        action_params = {
            "actionId": "720b02729d6f8a17203c064eccd6fcdd",
            "actionType": "basic"
        }
        return ActionService.execute(env_id, action_params, group_no)

if  __name__ == '__main__':
    # UmeService.get_mcs_and_bler("RAN3-上海高频CI团队-VAT1014")
    # UmeService.get_current_alarms("RAN3-上海高频CI团队-VAT1014")
    # print(UmeService.get_bs_current_version("RAN3-上海高频CI团队-VAT1014"))
    # UmeService.version_rollback("RAN3-上海高频CI团队-VAT1014", "NR-V4.20.20.20R17_2504020156.tar")
    # UmeService.version_upgrade("RAN3-上海高频CI团队-VAT1014", "https://artsz.zte.com.cn/artifactory/webapp/#/artifacts/browse/tree/General/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Bugfix/NR-V4.20.20.20R02/NR-V4.20.20.20R02_2503180157/01-gNB/litepass/PKG/NR-V4.20.20.20R02_2503180157.tar")

    print(UmeService.get_bs_backup_version("RAN3-上海高频CI团队-VAT1014"))