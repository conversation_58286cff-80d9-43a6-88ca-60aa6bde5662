from typing import List, Dict, Optional
from datetime import datetime
from domain.repository.cron_task import EsCronTask

class CronTaskService:
    """Service for cron task information"""

    def __init__(self):
        self._es = EsCronTask()

    def get_all(self, from_id: int = 0, size: int = 50) -> List[Dict]:
        """Get all cron tasks"""
        _, results = self._es.query_all_without_time(fromId=from_id, size=size, flag=False)
        return results

    def get_by_id(self, task_id: str) -> List[Dict]:
        """Get cron task by ID"""
        _, results = self._es.query_by_filter_without_sort({"task_id": task_id}, flag=False)
        return results

    def get_running_tasks(self) -> List[Dict]:
        """Get all running cron tasks"""
        _, results = self._es.query_by_filter_without_sort({"state": "running"}, flag=False)
        return results

    def update_state(self, task_id: str, new_state: str, operator: str) -> Dict:
        """Update the state of a cron task"""
        # First, get the task to make sure it exists
        tasks = self.get_by_id(task_id)
        if not tasks or len(tasks) == 0:
            return {"success": False, "message": f"Task with ID {task_id} not found"}

        # Get the document ID from the task
        doc_id = tasks[0].get("id")
        if not doc_id:
            return {"success": False, "message": "Task found but document ID is missing"}

        # Update the task state
        try:
            update_data = {
                "state": new_state,
                "update_time": datetime.now().isoformat(),
                "update_user": operator
            }

            result = self._es.update(id=doc_id, body={"doc": update_data})
            return {
                "success": True,
                "message": f"Task {task_id} state updated to {new_state}",
                "task_id": task_id,
                "operator": operator,
                "timestamp": datetime.now().isoformat(),
                "result": result
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Error updating task state: {str(e)}",
                "task_id": task_id
            }

    def delete(self, task_id: str, operator: str) -> Dict:
        """Delete a cron task"""
        # First, get the task to make sure it exists
        tasks = self.get_by_id(task_id)
        if not tasks or len(tasks) == 0:
            return {"success": False, "message": f"Task with ID {task_id} not found"}

        # Get the document ID from the task
        doc_id = tasks[0].get("id")
        if not doc_id:
            return {"success": False, "message": "Task found but document ID is missing"}

        # Delete the task
        try:
            result = self._es.delete(id=doc_id)
            return {
                "success": True,
                "message": f"Task {task_id} deleted successfully",
                "task_id": task_id,
                "operator": operator,
                "timestamp": datetime.now().isoformat(),
                "result": result
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Error deleting task: {str(e)}",
                "task_id": task_id
            }
