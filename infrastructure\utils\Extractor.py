import os
import tarfile
import zipfile


class Extractor(object):

    def __init__(self, _type, inFile, outPath):
        self.type = _type
        self.inFile = inFile
        self.outPath = os.path.join(outPath, os.path.basename(inFile).split(".")[0])

    def extract(self):
        if not os.path.exists(self.outPath):
            os.makedirs(self.outPath)
        if self.inFile.endswith((".tar", ".tar.gz", ".tgz")):
            self.un_tar()
        if self.inFile.endswith(".zip"):
            self.un_zip()

    def un_tar(self):
        tar = tarfile.open(self.inFile)
        tar.extractall(self.outPath)
        tar.close()

    def un_zip(self):
        zipFile = zipfile.ZipFile(self.inFile)
        zipFile.extractall(self.outPath)
        zipFile.close()
