#coding=utf-8
'''
Created on 2014年11月21日

@author: 10077668
'''
import os
import inspect


class ChannelData(object):
    def __init__(self):
        self._channelPackageDict = {}

        for root, dirs, files in os.walk(self._get_current_path()):
            for fileName in files:
                className = fileName.split(".")[0]
                if self._is_need_to_record(root, fileName, className):
                    rootList = root[root.rfind('infrastructure'):].split('\\')
                    # rootList.insert(0, 'biz-core')
                    self._channelPackageDict[className] = '.'.join(rootList)

    def get_package(self, channelType):
        return self._channelPackageDict[str(channelType)] + '.' + channelType

    def _get_current_path(self):
        thisFile = inspect.getfile(inspect.currentframe())
        return os.path.abspath(os.path.dirname(thisFile))

    def _is_need_to_record(self, root, fileName, className):
        if self._is_python_file(fileName )\
                and not self._is_current_path(root) \
                and not self._is_init_file(className ):
            return True
        return False

    def _is_current_path(self, path):
        if self._get_current_path() == path:
            return True
        return False

    def _is_python_file(self, name):
        if '.py' == os.path.splitext(name)[1]:
            return True
        return False

    def _is_init_file(self, name):
        if '__init__' == name:
            return True
        return False


if __name__ == '__main__':
    channelData = ChannelData()
    print(channelData._channelPackageDict)
    print(channelData.get_package('Telnet'))
