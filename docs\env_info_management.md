# Environment Information Management

This document describes the implementation of the Environment Information Management feature, which allows users to create, read, update, and delete environment information for various environment types.

## Overview

The Environment Information Management feature provides a comprehensive interface for managing different types of environment information:

1. Jenkins Server Environment
2. PC Environment
3. BizCore Environment
4. Version Environment
5. Device Environment

The feature also includes a logging system that records all operations performed on the environment information.

## Backend Implementation

### Data Models

- **Environment Information Models**: Located in `domain/model/dto/env_info.py`, these models define the structure of different environment types.
- **Environment Maintenance Log Model**: Located in `domain/model/dto/env_maintain_log.py`, this model defines the structure of the maintenance logs.

### Repositories

- **Environment Repositories**: Located in `domain/repository/es_env.py`, these repositories provide access to the Elasticsearch database for different environment types.
- **Environment Maintenance Log Repository**: Located in `domain/repository/es_env_maintain_log.py`, this repository provides access to the Elasticsearch database for maintenance logs.

### Services

- **Environment Information Services**: Located in `service/env_info_service.py`, these services provide business logic for managing environment information.
- **Environment Maintenance Log Service**: Also located in `service/env_info_service.py`, this service provides business logic for retrieving maintenance logs.

### API Endpoints

- **Environment Information API**: Located in `api/north/env_info/`, these endpoints provide RESTful API access to the environment information services.
- **Environment Maintenance Log API**: Located in `api/north/env_info/logs.py`, this endpoint provides RESTful API access to the maintenance logs.

### API URL

The API is accessible at `http://10.68.25.22:3303/env_info/` with the following endpoints:

- Jenkins Server Environment: `http://10.68.25.22:3303/env_info/jenkins_server_env`
- PC Environment: `http://10.68.25.22:3303/env_info/pc_env`
- BizCore Environment: `http://10.68.25.22:3303/env_info/biz_core_env`
- Version Environment: `http://10.68.25.22:3303/env_info/version_env`
- Device Environment: `http://10.68.25.22:3303/env_info/device_env`
- Maintenance Logs: `http://10.68.25.22:3303/env_info/logs`

## Frontend Implementation

### Components

- **EnvInfoView**: The main page component that displays the environment information management interface.
- **EnvInfoTable**: A reusable component that displays a table of environment information.
- **EnvInfoForm**: A reusable component that provides a form for creating and editing environment information.
- **EnvLogsTable**: A reusable component that displays a table of maintenance logs.

### Services

- **envInfoApi**: Located in `front/src/services/envInfoApi.js`, this service provides access to the backend API endpoints.

## Features

### Environment Information Management

- **View All**: Users can view all environment information for each environment type.
- **View by ID**: Users can view environment information for a specific environment ID.
- **Create**: Users can create new environment information.
- **Update**: Users can update existing environment information.
- **Delete**: Users can delete environment information.

### Maintenance Logs

- **View Logs**: Users can view all maintenance logs.
- **Filter Logs**: Users can filter logs by environment ID, environment type, operation, and operator.
- **View Details**: Users can view detailed information about each log, including the before and after state of the environment information.

## Usage

1. Navigate to the Environment Information Management page.
2. Select the environment type tab you want to manage.
3. Use the table to view existing environment information.
4. Use the "Add New" button to create new environment information.
5. Use the "Edit" and "Delete" buttons to modify or remove existing environment information.
6. Switch to the "Maintenance Logs" tab to view the history of operations.

## Security

- All operations are logged with the operator's username.
- Sensitive information like passwords is masked in the UI.

## Future Enhancements

- Add pagination for large datasets
- Add more advanced filtering options
- Add export functionality for environment information
- Add user role-based access control
