from fastapi import APIRouter, Query, HTTPException
from typing import Optional
from datetime import datetime

from domain.model.response.response import Response, ExceptionEnum
from domain.model.dto.business_process_stats import (
    BusinessProcessStatsRequest, 
    BusinessProcessStatsResponse
)
from service.business_process_stats_service import BusinessProcessStatsService
from infrastructure.logger.logger import logger

router = APIRouter(prefix="/business_process_stats")


@router.get("/", response_model=Response)
async def get_business_process_stats(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD，默认为当天"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD，默认为开始日期"),
    env_id: Optional[str] = Query(None, description="环境ID过滤，可选")
):
    """
    获取业务流程统计数据
    
    业务流程包含：
    - 用例通过：version_test_result为True，service_key是jenkins_job_save，status是success
    - 智能分析：version_test_result为False，service_key只包含jenkins_job_save、log_analysis
    - 环境检测：version_test_result为False，service_key只包含jenkins_job_save、log_analysis、env_check
    - 用例复测：version_test_result为False，service_key只包含jenkins_job_save、log_analysis、env_check、online_test
    - 用例回溯：service_key包含version_upgrade或version_rollback，或details.message.subtype包含非"normal"
    
    返回数据包含：
    - by_date: 按日期统计各业务流程的记录数
    - by_env: 按环境统计各业务流程的记录数
    - total_stats: 总体统计
    - date_range: 统计时间范围
    """
    try:
        # 验证日期格式
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return Response.from_exception(
                    ExceptionEnum.PARAM_ERROR,
                    msg="开始日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return Response.from_exception(
                    ExceptionEnum.PARAM_ERROR,
                    msg="结束日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        # 创建请求对象
        request = BusinessProcessStatsRequest(
            start_date=start_date,
            end_date=end_date,
            env_id=env_id
        )
        
        # 调用服务获取统计数据
        service = BusinessProcessStatsService()
        result = service.get_business_process_stats(request)
        
        return Response.build(result.model_dump())
        
    except Exception as e:
        logger.error(f"获取业务流程统计数据失败: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"获取业务流程统计数据失败: {str(e)}"
        )


@router.get("/summary", response_model=Response)
async def get_business_process_summary(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD，默认为当天"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD，默认为开始日期")
):
    """
    获取业务流程统计摘要数据（仅总体统计）
    """
    try:
        # 验证日期格式
        if start_date:
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return Response.from_exception(
                    ExceptionEnum.PARAM_ERROR,
                    msg="开始日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        if end_date:
            try:
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return Response.from_exception(
                    ExceptionEnum.PARAM_ERROR,
                    msg="结束日期格式错误，请使用 YYYY-MM-DD 格式"
                )
        
        # 创建请求对象
        request = BusinessProcessStatsRequest(
            start_date=start_date,
            end_date=end_date
        )
        
        # 调用服务获取统计数据
        service = BusinessProcessStatsService()
        result = service.get_business_process_stats(request)
        
        # 只返回总体统计和时间范围
        summary = {
            "total_stats": result.total_stats,
            "date_range": result.date_range,
            "total_count": sum(result.total_stats.values())
        }
        
        return Response.build(summary)
        
    except Exception as e:
        logger.error(f"获取业务流程统计摘要失败: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"获取业务流程统计摘要失败: {str(e)}"
        )


@router.get("/detail", response_model=Response)
async def get_business_process_detail(
    date: Optional[str] = Query(None, description="日期 YYYY-MM-DD，可选，不指定则查询时间范围内所有数据"),
    process_type: Optional[str] = Query(None, description="业务流程类型，可选，不指定则返回所有流程类型"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD，默认为date参数"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD，默认为date参数"),
    env_id: Optional[str] = Query(None, description="环境ID过滤，可选")
):
    """
    获取指定日期的业务流程详细信息（按业务流程类型聚合）

    返回该日期下的业务流程统计信息，按业务流程类型聚合，包括：
    - 业务流程类型
    - 任务数量
    - 涉及的环境数量
    - 环境分布统计
    - 任务ID列表
    - 人工处理时间
    - 当前状态
    """
    try:
        # 验证日期格式（如果提供了日期）
        if date:
            try:
                datetime.strptime(date, '%Y-%m-%d')
            except ValueError:
                return Response.from_exception(
                    ExceptionEnum.PARAM_ERROR,
                    msg="日期格式错误，请使用 YYYY-MM-DD 格式"
                )

        # 设置查询时间范围
        if start_date and end_date:
            query_start_date = start_date
            query_end_date = end_date
        elif date:
            query_start_date = start_date or date
            query_end_date = end_date or date
        else:
            # 如果没有指定任何日期，使用今天
            today = datetime.now().strftime('%Y-%m-%d')
            query_start_date = start_date or today
            query_end_date = end_date or today

        # 创建请求对象
        request = BusinessProcessStatsRequest(
            start_date=query_start_date,
            end_date=query_end_date,
            env_id=env_id
        )

        # 调用服务获取详细数据
        service = BusinessProcessStatsService()
        detail_result = service.get_business_process_detail(request, date, process_type)

        return Response.build(detail_result)

    except Exception as e:
        logger.error(f"获取业务流程详细信息失败: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.SERVER_FAILED,
            msg=f"获取业务流程详细信息失败: {str(e)}"
        )



