class Iterator:

    def __init__(self, iterable, total_iter_num):
        self.iterator = iter(iterable)
        self.total_iter_num = total_iter_num
        self.curr_iter = 0

    def __iter__(self):
        return self

    def __next__(self):
        self.curr_iter += 1
        return next(self.iterator)

    @property
    def next(self):
        self.curr_iter += 1
        return next(self.iterator)

    @property
    def has_next(self):
        return self.curr_iter != self.total_iter_num


class BasicIterator(Iterator):

    def __init__(self, iterable):
        total_iter_num = len(iterable)
        super().__init__(iterable, total_iter_num)


class RangeIterator(Iterator):

    def __init__(self, start, end, step):
        total_iter_num = self.calculate_iter_num(start, end, step)
        super().__init__(range(start, end, step), total_iter_num)

    def calculate_iter_num(self, start, end, step):
        count = 0
        if step > 0:
            count = (end - start) // step + 1
            if start + step * (count - 1) >= end:
                count -= 1
        elif step < 0:
            count = (start - end) // abs(step) + 1
            if start + step * (count - 1) <= end:
                count -= 1
        else:
            if start <= end:
                count = 1
        return count if count > 0 else 0


class EnumIterator(Iterator):

    def __init__(self, iterable, start=0):
        super().__init__(enumerate(iterable, start), len(iterable))


class ZipIterator(Iterator):

    def __init__(self, *iterables):
        total_iter_num = self.calculate_zip_min_length(*iterables)
        super().__init__(zip(*iterables), total_iter_num)

    def calculate_zip_min_length(self, *iterables):
        lengths = map(len, iterables)
        min_length = min(lengths)
        return min_length


if __name__ == '__main__':
    a = ["a", "b", "c"]

    iterator = RangeIterator(20, -20, -3)
    print(iterator.next)
    print(iterator.next)
    print(iterator.next)
    print(iterator.next)
