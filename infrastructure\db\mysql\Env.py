import os
import yaml

from infrastructure.Singleton import Singleton
from infrastructure.Utils import get_project_root

default_env_name = "db_mysql"


@Singleton
class Env(object):
    @property
    def config(self):
        db_cfg_path = os.path.join(get_project_root(), "config/config.yaml")
        with open(db_cfg_path, "r", encoding="utf-8") as f:
            db_cfg = yaml.load(f, Loader=yaml.FullLoader)
        config = db_cfg.get(default_env_name, default_env_name)
        return config
