import json

from infrastructure.utils.HttpCodeEnum import HttpCodeEnum, HttpMessageIdEnum


DATA = "data"
FAILREASON = "failReason"
RESULT = "result"


class HttpResponse(object):
    def __init__(self, http_response):
        self._http_response = http_response

    @property
    def data(self):
        return self._http_response[DATA]

    @property
    def failReason(self):
        return self._http_response[FAILREASON]

    @property
    def result(self):
        return self._http_response[RESULT]

    def is_success(self):
        return True == self.result

    def __str__(self):
        return json.dumps(self._http_response, indent=4)
