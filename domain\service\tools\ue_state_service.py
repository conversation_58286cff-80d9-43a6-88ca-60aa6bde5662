import warnings
import paramiko
import re
import logging
warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)


class UeInfoService(object):
    @staticmethod
    def create_ssh_client(ip, username, password):
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=ip,
            username=username,
            password=password,
            timeout=10
        )
        return ssh

    @staticmethod
    def get_ue_ipconfig_info(data):
        try:
            pc_ip = data.get("pc_ip")
            pc_username = data.get("pc_username")
            pc_password = data.get("pc_password")
            ue_ip = data.get("ue_ip")
            ssh = UeInfoService.create_ssh_client(pc_ip, pc_username, pc_password)
            # 执行命令
            admin_command = 'ipconfig'
            stdin, stdout, stderr = ssh.exec_command(admin_command)
            output = stdout.read().decode('gbk')  # 读取标准输出并解码
            # 提取所有IPv4地址
            ipv4_pattern = r'IPv4 地址[ .:]+(\d+\.\d+\.\d+\.\d+)'
            ipv4_addresses = re.findall(ipv4_pattern, output)
            # 判断ueip中的所有元素是否在提取的IPv4地址中
            missing_ips = [ip for ip in ue_ip if ip not in ipv4_addresses]
            if not missing_ips:
                flag = True
            else:
                flag = False
            # 获取命令输出
            error = stderr.read().decode('gbk')
            if error:
                print(f"命令执行错误: {error}")
            # 关闭连接
            ssh.close()
            print(ipv4_addresses)
            # return {"result": flag, "details": {"ipv4_addresses": ipv4_addresses, "missing_ips": missing_ips}}
            return {"result": flag, "details": {"missing_ips": missing_ips}}
        except paramiko.AuthenticationException:
            print("认证失败：请检查用户名和密码")
            return {"result": False, "details": {"error": "认证失败：请检查用户名和密码"}}
        except paramiko.SSHException as ssh_exception:
            print(f"SSH连接错误: {str(ssh_exception)}")
            return {"result": False, "details": {"error": f"SSH连接错误: {str(ssh_exception)}"}}
        except Exception as e:
            print(f"发生错误111: {str(e)}")
            return {"result": False, "details": {"error": f"发生错误: {str(e)}"}}

    @staticmethod
    def get_ue_com_info(data):
        try:
            pc_ip = data.get("pc_ip")
            pc_username = data.get("pc_username")
            pc_password = data.get("pc_password")
            device_id = data.get("device_id")
            ssh = UeInfoService.create_ssh_client(pc_ip, pc_username, pc_password)
            # 获取设备管理器中的端口信息
            powershell_command = 'powershell -Command "Get-CimInstance Win32_PnPEntity | Where-Object { $_.Caption -like \'*COM*\' } | Select-Object DeviceID, Caption, Description"'
            stdin, stdout, stderr = ssh.exec_command(powershell_command)
            output = stdout.read().decode('gbk')  # 读取标准输出并解码
            all_present = all(device in output for device in device_id)
            if all_present:
                flag = True
            else:
                flag = False
            # 获取命令输出
            error = stderr.read().decode('gbk')
            if error:
                print(f"命令执行错误: {error}")
            # 关闭连接
            ssh.close()
            print(output)
            # return {"result": flag, "details": {"output": output, "missing_devices": [d for d in device_id if d not in output]}}
            return {"result": flag, "details": {"missing_devices": [d for d in device_id if d not in output]}}
        except paramiko.AuthenticationException:
            print("认证失败：请检查用户名和密码")
            return {"result": False, "details": {"error": "认证失败：请检查用户名和密码"}}
        except paramiko.SSHException as ssh_exception:
            print(f"SSH连接错误: {str(ssh_exception)}")
            return {"result": False, "details": {"error": f"SSH连接错误: {str(ssh_exception)}"}}
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return {"result": False, "details": {"error": f"发生错误: {str(e)}"}}
