import logging
import os, sys

sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))

from infrastructure.const.common import SERVER_ENV_KEY
from infrastructure.const.common import DEFAULT_SERVER_NAME
from infrastructure.utils.Config import Config


SUFFIX = "_cfg"


class Env(object):
    _CONFIG = None  # 添加类属性存储配置

    @classmethod
    def get_config(cls):
        if not cls._CONFIG:
            cls.load_config()
        return cls._CONFIG    

    @staticmethod
    def load_config():
        env_name = os.getenv(SERVER_ENV_KEY, DEFAULT_SERVER_NAME)
        logging.debug("OS ENV: %s" % (env_name,))
        default_env_name = ("%s%s" % (DEFAULT_SERVER_NAME, SUFFIX)).lower()
        effective_env_name = ("%s%s" % (env_name, SUFFIX)).lower()
        try:
            module = __import__(f"config.{effective_env_name}", globals(), locals())
            Env._CONFIG = Config(**eval(f"module.{effective_env_name}.CONFIG"))
        except ImportError:
            module = __import__(f"config.{default_env_name}", globals(), locals())
            Env._CONFIG = Config(**eval(f"module.{default_env_name}.CONFIG"))


if  __name__ == '__main__':
    print(Env.get_config())