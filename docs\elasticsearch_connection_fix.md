# Elasticsearch Connection Fix

## Overview

This document describes the fix for the Elasticsearch connection issue in the environment information management feature.

## Problem

The environment information management feature was encountering an error when trying to access the Elasticsearch database:

```
AttributeError: 'NoneType' object has no attribute 'search'
```

This error occurred because the `_es` attribute in the `Document` class was `None`. The Elasticsearch connection was being set in `main.py` using `Document.esConn = Elasticsearch(...)`, but this connection was not being properly passed to the `Document` instances.

## Solution

The solution was to modify the `Document` class to ensure it always has a valid Elasticsearch connection:

1. Changed the `_es` attribute from an instance variable to a property
2. Added logic to initialize the Elasticsearch connection if it's not already initialized
3. Removed debug print statements from the code
4. Updated API endpoints to use `model_dump()` instead of the deprecated `dict()` method

### Code Changes

#### 1. Modified the `Document` class in `infrastructure/db/elastic_search/elastic_search.py`:

```python
class Document(object):
    esConn = None

    def __init__(self, index=None, docType=None):
        self._index = index
        self._docType = docType
        
    @property
    def _es(self):
        # Always get the latest connection from the class variable
        if Document.esConn is None:
            from elasticsearch import Elasticsearch
            from infrastructure.utils.Env import Env
            # Initialize Elasticsearch connection if not already done
            Document.esConn = Elasticsearch(
                hosts=[{
                    'host': Env.get_config().ES.ip,
                    'port': Env.get_config().ES.port
                }],
                maxsize=1000,
                http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                sniff_on_start=False,
                sniff_on_connection_fail=False,
                retry_on_timeout=True,
                max_retries=2,
                timeout=30,
                sniff_timeout=60
            )
        return Document.esConn
```

#### 2. Removed debug print statements:

- Removed `print(f"wwwwwwwwwwwww: {self._es}")` from `query_all_without_time` method
- Removed `print(f"yyyyyy: {self._es}")` from `get_all` method in `EnvInfoService`
- Removed debug print statements from `jenkins_server_env.py`

#### 3. Updated API endpoints to use `model_dump()` instead of `dict()`:

```python
# Before
return service.create(env_info.dict(), x_operator)

# After
return service.create(env_info.model_dump(), x_operator)
```

## Benefits

1. **Robust Connection Handling**: The Elasticsearch connection is now automatically initialized if it's not already set, ensuring that the application can always connect to the database.

2. **Clean Code**: Removed debug print statements and fixed deprecated method calls, making the code cleaner and more maintainable.

3. **Future-Proof**: Updated to use the recommended `model_dump()` method instead of the deprecated `dict()` method, ensuring compatibility with future versions of Pydantic.

## Testing

The fix has been tested by:

1. Starting the application
2. Accessing the environment information management API endpoints
3. Verifying that the application can successfully connect to the Elasticsearch database and perform CRUD operations

## Conclusion

This fix ensures that the environment information management feature can properly connect to the Elasticsearch database, allowing users to create, read, update, and delete environment information as intended.
