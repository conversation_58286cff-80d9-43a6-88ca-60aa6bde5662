# -*-coding:utf-8-*-
import json
from typing import List, Dict, Any
from collections import defaultdict
from datetime import datetime

from domain.repository.agents_log import EsAgentsLog
from infrastructure.logger.logger import logger

service_type_map = {
    "JenkinsJobService": "任务保存",
    "CiAgentsQueue": "消息队列",
    "env_check": "环境检测",
    "online_test": "线上复测",
    "version_upgrade": "回溯--版本升级",
    "version_rollback": "回溯--版本回退",
    "env_backtrack": "回溯",
    "log_save": "日志保存"
}

class AgentLogAggregationService:
    """
    Agent日志聚合服务，用于按照task_id和service_key汇聚agent_log数据
    """
    
    def __init__(self):
        """初始化服务"""
        self._agents_log = EsAgentsLog()
    
    def aggregate_by_task_id(self, task_id: str, limit: int = 1000) -> Dict[str, List[Dict[str, Any]]]:
        """
        按照task_id汇聚日志数据，并按service_key分组
        
        Args:
            task_id: 任务ID
            limit: 返回记录的最大数量
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 按service_key分组的日志记录
        """
        try:
            # 获取指定任务ID的日志记录
            _, logs = self._agents_log.get_logs_by_task_id(task_id, limit)
            
            if not logs:
                logger.warning(f"未找到任务ID为 {task_id} 的日志记录")
                return {}
            
            # 按service_key分组并处理
            return self._process_logs_by_service_key(logs)
            
        except Exception as e:
            logger.error(f"汇聚任务 {task_id} 的日志记录时发生错误: {str(e)}")
            return {}
    
    def _process_logs_by_service_key(self, logs: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        处理日志记录，按service_key分组，并仅保留特定操作的记录
        
        Args:
            logs: 原始日志记录列表
        
        Returns:
            Dict[str, List[Dict[str, Any]]]: 按service_key分组的日志记录
        """
        # 初始化结果字典
        result = defaultdict(list)
        
        # 按时间排序日志记录
        sorted_logs = sorted(logs, key=lambda x: x.get("timestamp", ""))
        
        # 跟踪哪些task_id已经被标记为env_backtrack
        backtrack_task_ids = set()
        
        # 记录第一次成功的online_test的时间
        first_online_test_time = {}
        
        # 筛选并分组日志记录
        for log in sorted_logs:
            # 只保留operation为"run"或"process_message"的记录
            operation = log.get("operation", "")
            if operation in ["create_agents_service", "push_info"]:
                continue
            
            # 获取task_id和service_key
            task_id = log.get("task_id", "")
            service_key = log.get("service_key", "")
            service_type = log.get("service_type", "")
            timestamp = log.get("timestamp", "")
            
            if not service_key:
                continue
            
            # 记录第一次成功的online_test的时间
            if service_key == "online_test" and task_id not in first_online_test_time:
                # 只记录operation为"cron_task_online_test"且状态为success的online_test
                operation = log.get("operation", "")
                status = log.get("status", "")
                if operation == "cron_task_online_test" and status == "success":
                    first_online_test_time[task_id] = timestamp
            
            # 检查是否需要将service_key更改为env_backtrack
            if service_key in ["version_rollback", "version_upgrade"]:
                # 将当前记录的task_id添加到backtrack_task_ids集合中
                if task_id:
                    backtrack_task_ids.add(task_id)
                service_key = "env_backtrack"
            elif task_id in backtrack_task_ids:
                # 如果task_id已经在backtrack_task_ids中，则将service_key更改为env_backtrack
                service_key = "env_backtrack"
            elif service_key == "online_test":
                # 处理特殊情况：online_test的subtype
                subtype = self._get_subtype(log)
                if subtype != "normal":
                    # 将非normal的online_test归类到env_backtrack
                    service_key = "env_backtrack"
                    # 同时将该task_id添加到backtrack_task_ids中
                    if task_id:
                        backtrack_task_ids.add(task_id)
            elif service_key in ["log_analysis", "env_check"] and task_id in first_online_test_time:
                # 如果是log_analysis或env_check，且发生在第一次online_test之后，归类到env_backtrack
                if timestamp > first_online_test_time[task_id]:
                    service_key = "env_backtrack"
                    if task_id:
                        backtrack_task_ids.add(task_id)

            # 构建简化的记录
            simplified_log = {
                "时间": log.get("timestamp", ""),
                "操作": operation,
                "步骤": service_type_map.get(service_type, "未知"),
                "状态": log.get("status", ""),
                "details": log.get("details", {})
            }
            
            # 添加到对应的service_key组
            result[service_key].append(simplified_log)
        
        # 对每个service_key组内的记录按时间排序
        for service_key in result:
            result[service_key] = sorted(
                result[service_key], 
                key=lambda x: x.get("时间", ""), 
                reverse=False  # 按时间升序排序
            )
        
        return dict(result)
    
    def _get_subtype(self, log: Dict[str, Any]) -> str:
        """
        从日志中提取子类型信息
        
        Args:
            log: 日志记录
            
        Returns:
            str: 子类型
        """
        # 首先检查日志本身的subtype字段
        subtype = log.get("subtype", "")
        if subtype:
            return subtype
        
        # 然后检查details中的subtype字段
        details = log.get("details", {})
        if isinstance(details, dict):
            subtype = details.get("subtype", "")
            if subtype:
                return subtype
            
            # 检查message中的subtype
            message = details.get("message", {})
            if isinstance(message, dict):
                subtype = message.get("subtype", "")
                if subtype:
                    return subtype

        
        # 默认为normal
        return "normal"
    
    def get_all_task_ids(self, limit: int = 1000) -> List[str]:
        """
        获取所有任务ID
        
        Args:
            limit: 返回记录的最大数量
            
        Returns:
            List[str]: 任务ID列表
        """
        try:
            # 获取所有日志记录
            query = {
                "query": {"match_all": {}},
                "sort": [{"timestamp": {"order": "desc"}}],
                "size": limit,
                "_source": ["task_id"]  # 只获取task_id字段
            }
            _, logs = self._agents_log.search(body=query)
            
            # 提取唯一的task_id
            task_ids = set()
            for log in logs:
                task_id = log.get("task_id")
                if task_id:
                    task_ids.add(task_id)
            
            return list(task_ids)
            
        except Exception as e:
            logger.error(f"获取所有任务ID时发生错误: {str(e)}")
            return []


if __name__ == "__main__":
    # 创建服务实例
    service = AgentLogAggregationService()
    
    # 获取所有任务ID
    print("正在获取所有任务ID...")
    task_ids = service.get_all_task_ids(limit=100)  # 限制为最近的100条记录
    
    if not task_ids:
        print("未找到任何任务ID")
    else:
        print(f"找到 {len(task_ids)} 个任务ID")
        
        # 选择第一个任务ID进行演示
        demo_task_id = task_ids[0]
        demo_task_id = "260f0c924c754cbf8c70f2c7b3b4d11c"
        print(f"\n使用任务ID {demo_task_id} 进行演示:")
        
        # 汇聚该任务ID的日志记录
        aggregated_logs = service.aggregate_by_task_id(demo_task_id)
        
        if not aggregated_logs:
            print(f"未找到任务ID为 {demo_task_id} 的日志记录")
        else:
            print(f"找到 {len(aggregated_logs)} 个service_key组")
            
            # 打印每个service_key组的日志记录
            for service_key, logs in aggregated_logs.items():
                print(f"\nservice_key: {service_key}, 记录数: {len(logs)}")
                
                # 打印该service_key组的前3条记录
                for i, log in enumerate(logs[:3]):
                    print(f"\n  记录 {i+1}:")
                    # 打印时间、操作和状态
                    print(f"  时间: {log.get('时间')}")
                    print(f"  操作: {log.get('操作')}")
                    print(f"  步骤: {log.get('步骤')}")
                    print(f"  状态: {log.get('状态')}")
                    
                    # 打印details的部分内容（如果太长则截断）
                    details_str = json.dumps(log.get('details'), ensure_ascii=False)
                    if len(details_str) > 100:
                        details_str = details_str[:100] + "..."
                    print(f"  details: {details_str}")
                
                # 如果记录数超过3条，显示省略信息
                if len(logs) > 3:
                    print(f"\n  ... 还有 {len(logs) - 3} 条记录未显示")