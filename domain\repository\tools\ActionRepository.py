# -*-coding:utf-8 -*-
from infrastructure.tdl.TdlApi import ActionApi


class ActionRepository(object):
    @staticmethod
    def execute(params):
        from domain.model.tools.Action import ActionExecuteResultResponse
        ret = ActionApi.execute(params)
        return ActionExecuteResultResponse(**ret)
    
    @staticmethod
    def get_execution_info(params):
        from domain.model.tools.ActionExecuteInfo import ActionExecuteInfoResponse
        ret = ActionApi.get_execution_info(params)
        return ActionExecuteInfoResponse(**ret)

    @staticmethod
    def get_execution_logs(params):
        from domain.model.tools.ActionExecuteLogs import ActionExecuteLogsResponse
        ret = ActionApi.get_execution_logs(params)
        return ActionExecuteLogsResponse(**ret)