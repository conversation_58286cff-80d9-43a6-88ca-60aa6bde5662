# -*&- coding: utf-8 -*-
from pydantic import BaseModel, Field

class VersionRollback(BaseModel):
    env_id: str = Field(description="环境ID")
    group_no: str = Field(default="", description="组ID")
    tar_path: str = Field(description="tar包路径")


class VersionUpgrade(BaseModel):
    env_id: str = Field(description="环境ID")
    group_no: str = Field(default="", description="组ID")
    tar_path: str = Field(description="tar包路径")
