#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/8/14 11:31
# <AUTHOR> 10263601

import socket

def get_host_ip_address():
    # 创建一个UDP套接字
    sock = None
    try:
        # 获取本地IP地址, '*********'为公司深圳地区主DNS服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.connect(('*********', 80))
        localIp = sock.getsockname()[0]
    except Exception as _:
        localIp = None
    finally:
        if sock is not None:
            sock.close()
    return localIp


if __name__ == '__main__':
    print(get_host_ip_address())
