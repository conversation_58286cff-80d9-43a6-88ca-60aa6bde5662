from fastapi import APIRouter, Query
from typing import Dict, Optional

from service.env_info_service import EnvMaintainLogService

router = APIRouter(prefix="/env_info/logs")
service = EnvMaintainLogService()

@router.get("/")
async def get_all_logs(
    from_id: int = Query(0, description="Starting index for pagination"),
    size: int = Query(50, description="Number of items to return"),
    env_id: Optional[str] = Query(None, description="Filter by environment ID"),
    env_type: Optional[str] = Query(None, description="Filter by environment type"),
    operation: Optional[str] = Query(None, description="Filter by operation type"),
    operator: Optional[str] = Query(None, description="Filter by operator")
):
    """Get environment maintenance logs with optional filters"""
    filters = {}
    if env_id:
        filters["env_id"] = env_id
    if env_type:
        filters["env_type"] = env_type
    if operation:
        filters["operation"] = operation
    if operator:
        filters["operator"] = operator
    
    return service.get_logs(filters=filters if filters else None, from_id=from_id, size=size)
