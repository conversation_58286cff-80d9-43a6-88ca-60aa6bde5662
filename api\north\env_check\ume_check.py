import requests
import urllib3
import rsa
import base64
import json
import logging
import random
from datetime import datetime, timedelta
urllib3.disable_warnings()
from fastapi import APIRouter, Body
from service.tools.ume_state_service import *
import warnings
import paramiko
warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)
from infrastructure.logger.logger import logger

router = APIRouter(prefix="/ume")

UME_API_MAP = {
    'getUmePublicKey': 'api/oauth2/v1/login/publickey',
    'loginByRsa': 'api/oauth2/v1/login_rsa',
    'getAccessToken': 'api/oauth2/v1/usercred/access_token',
    'logout': 'api/oauth2/v1/logout',
    'queryPackage': 'openoapi/catalog/v1/catalog/packages?packageType={}',
    'deletePackage': 'openoapi/catalog/v1/catalog/package?packageType={}&packageId={}&flag={}'
}

RANDOM = str(random.randint(10**10, 10**11-1))
session_id = ''

class Ume:
    def __init__(self,ip,port=28001,username='',pwd=''):
        self.ip = ip
        self.port = port
        self.username = username
        self.pwd = pwd
        self.baseApiUrl = "https://%s:%s/" % (ip, port)
        self._login_token = "df421cd0874839755a89635e6d08eafb"
        self._header = {"Content-Type": "application/json"}
        self._cookie = ""

    def ip(self):
        return self.ip

    def __get_api_url(self,apiName):
        return self.baseApiUrl + UME_API_MAP[apiName]

    @staticmethod
    def _encrypt_password(password, public_key):
        rsa_public_key = rsa.PublicKey.load_pkcs1_openssl_pem(
            "-----BEGIN PUBLIC KEY-----\n" + public_key + "\n-----END PUBLIC KEY-----")
        encrypted_pwd = rsa.encrypt(password.encode(), rsa_public_key)
        return base64.b64encode(encrypted_pwd).decode()

    def update_header(self):
        public_key_result = requests.request('GET', self.__get_api_url('getUmePublicKey'), headers=self._header,
                                             verify=False)
        public_key = json.loads(public_key_result.text).get("rsaPublicKey")
        password = self._encrypt_password(self.pwd, public_key)
        cookie = 'Z-LOGIN-PUBLIC-CODE-%s=%s' % (
            self.port, public_key_result.cookies.get("Z-LOGIN-PUBLIC-CODE-%s" % self.port))
        login_body = {
            "isEncypted": "true",
            "username": self.username,
            "password": password,
            "loginToken": self._login_token
        }
        self._header = {"Content-Type": "application/json", "Cookie": cookie}
        login_result = requests.request('POST', self.__get_api_url('loginByRsa'), data=json.dumps(login_body),
                                        headers=self._header, verify=False)
        self._cookie = login_result.cookies.get("Z-AUTH-CODE-%s" % self.port)
        cookie = 'Z-AUTH-CODE-%s=%s' % (self.port, login_result.cookies.get("Z-AUTH-CODE-%s" % self.port))
        self._header.update({"username": self.username, "Cookie": cookie})
        return self._header

    def login_ume(self):
        self._header = self.update_header()
        param = {"username": self.username, "password": self.pwd, "grant_type": "PASSWORD"}
        access_token_result = requests.request('POST', self.__get_api_url('getAccessToken'), data=json.dumps(param),
                                               headers=self._header, verify=False)
        self._access_token = json.loads(access_token_result.text).get("access_token")
        # print(self._access_token)

    def login(self):
        try:
            self.login_ume()
            return True
        except Exception:
            logger.error("can't login ume device: %s" % self.ip)
            return False

    def query_package(self, pkg_type):
        url = self.__get_api_url('queryPackage').format(pkg_type)
        response = requests.request('GET', url, headers=self._header, verify=False)
        package_dict = {}
        for each_package_info in response.json()['data']:
            package_dict[each_package_info['modifyDateTime']] = each_package_info['resourceId']
        return package_dict

    def delete_package(self, packageId = '' , pkg_type = 'APP',  flag = False):
        url =self.__get_api_url('deletePackage').format(pkg_type, packageId, flag)
        response = requests.request('POST', url, headers=self._header, verify=False)
        print(response.json())


@router.post("/check_ume_alarm")
async def check_ume_alarm(data: dict = Body(...)):
    return ume_state_service.query_ume_alarm(data)


@router.post("/check_me_connect")
async def check_me_connect(data: dict = Body(...)):
    return ume_state_service.query_me_connect(data)

@router.post("/ne_clean_package1")
async def check_net_state1(data: dict = Body(...)):
    try:
        ip = data.get("ip")
        username = data.get("username")
        password = data.get("password")
        days = data.get("days")

        ume = Ume(ip=ip, username=username, pwd=password)
        ume.login()

        # 获取软件仓库的所有软件
        package_dict = ume.query_package("APP")

        # 获取三天前时间的时间戳
        timestamp_ms = int((datetime.now() - timedelta(days=days)).timestamp()) * 1000

        # 通过对比时间戳查找出存放超过三天的版本包，并进行清理
        for key in package_dict.keys():
            if key < timestamp_ms:
                ume.delete_package(package_dict[key])
                return "已清理"
        return "无需清理"
    except paramiko.AuthenticationException:
        logger.warning("认证失败：请检查用户名和密码")
        return None
    except paramiko.SSHException as ssh_exception:
        logger.warning(f"SSH连接错误: {str(ssh_exception)}")
        return None
    except Exception as e:
        logger.warning(f"发生错误: {str(e)}")
        return None