import paramiko
import warnings
from infrastructure.logger.logger import logger


def execute_remote_command(hostname, port, username, password, command):
    try:
        # 创建 SSH 客户端实例
        ssh_client = paramiko.SSHClient()

        # 自动添加未知主机的密钥
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # 连接到远程主机
        logger.info(f"SSH  Connecting to {hostname}...")
        ssh_client.connect(hostname, port=port, username=username, password=password)

        # 执行命令
        logger.info(f"Executing SSH  command: {command}")
        stdin, stdout, stderr = ssh_client.exec_command(command)

        # 获取命令的输出
        output = stdout.read().decode('utf-8', errors='ignore')  # 忽略无法解码的字符
        error = stderr.read().decode('utf-8', errors='ignore')

        # 打印命令输出和错误（如果有的话）
        if output:
            logger.info(f"SSH  Output: {output}")
            return output
        if error:
            logger.info("SSH   Error: {error}", )
            return error

    except Exception as e:
        logger.error(f"SSH Error occurred: {e}")
    finally:
        # 关闭 SSH 连接
        ssh_client.close()
        logger.info("SSH  Connection closed.")


warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)


def execute_ssh_command(hostname, username, password, command):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        logger.info(f"ssh 正在连接到{hostname}")
        ssh.connect(hostname=hostname, username=username, password=password, timeout=10)
        stdin, stdout, stderr = ssh.exec_command(command)
        output = stdout.read().decode('gbk')
        error = stderr.read().decode('gbk')
        ssh.close()
        if error:
            logger.error(f"SSH 命令执行错误: {error}")
        return output, error
    except paramiko.AuthenticationException:
        logger.info("SSH 认证失败：请检查用户名和密码")
        return None, "认证失败"
    except paramiko.SSHException as ssh_exception:
        logger.info(f"SSH连接错误: {str(ssh_exception)}")
        return None, str(ssh_exception)
    except Exception as e:
        logger.info(f"SSH 发生错误: {str(e)}")
        return None, str(e)
