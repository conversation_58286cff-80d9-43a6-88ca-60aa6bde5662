# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import re
import time
import logging
import traceback

from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.utils.SetAttrDecorate import set_dict_to_obj_attr
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister


@set_dict_to_obj_attr
class CmdExcutor(object):

    '''
    classdocs
    '''

    def __init__(self, cmdExcutorDict):
        '''
        Constructor
        '''
        self.excuteTool = ContractRegister().find(cmdExcutorDict.get('tool'))
        self.stack = None

    async def excute(self, containerIndex=-1):
        cmdResults = []
        if not self.exeTimes:
            self.exeTimes = 1
        for _ in range(int(self.exeTimes)):
            cmdResult = await self._excute_when_default(containerIndex)
            time.sleep(int(self.waitSecs))
            cmdResults.append(cmdResult)
        if len(cmdResults) == 1:
            return cmdResults[0]
        return cmdResults

    async def _exit_proc(self):
        time.sleep(int(self.waitSecs))
        exitTimes = self.stack.size() if self.stack is not None else 0
        for _ in range(exitTimes):
            try:
                await getattr(self.excuteTool, 'execute_contract_cmd')('exit', '#', 2, self.stack)
            except:
                pass

    async def _excute_when_default(self, containerIndex=-1):
        @retries_on_exception(self.failedRetried, self._exit_proc)
        async def _excute(cmd, expected, timeout):
            if self.proc:
                await self.excuteTool.pad_process(self.proc, isAutoLogin=False, processStack=self.stack, containerIndex=containerIndex)
            if self.containerName and not self.proc:
                await self.excuteTool.login_container_by_id(self.containerName)
            cmdResult = await getattr(self.excuteTool, 'execute_contract_cmd')(cmd, expected, timeout, self.stack)
            if cmdResult.result:
                return cmdResult
            if self.isRaiseNotMatchException is False:
                return cmdResult
            else:
                raise Exception('excute cmd: ' + cmd + ' failed')
        cmdResult = None
        try:
            cmdResult = await _excute(self.cmd, self.expected, int(self.timeout))
        except Exception:
            if self.proc and not self.isFailedContinue:
                await self._exit_proc()
            if not re.search("^exit", self.cmd):
                logging.warning(traceback.print_exc())
            if not self.isFailedContinue:
                raise
        return cmdResult
