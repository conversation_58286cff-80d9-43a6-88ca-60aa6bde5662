import asyncio
import uuid

from domain.model.dto.logdto import LogJobInfo
from domain.repository.es_env import EsJenkinsServerEnv, EsBizCoreEnv, EsVersionEnv
from domain.repository.version_execute import EsVersionExecute
from domain.service.jenkins_client import JenkinsClient
from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
from infrastructure.logger.logger import logger
from service.env_service import VersionEnvService
from service.run_service.run_service import Service
from domain.repository.cron_task import EsCronTask
from service.tools.UmeService import UmeService


class IndependenceRollbackService(Service):
    def __init__(self, env_id: str, job_name: str, build_number: int, service_type="", subtype="", state="", needed={},
                 es=EsCronTask()):
        super().__init__(service_type, subtype, state, needed, es)
        self.env_id = env_id
        self.job_name = job_name
        self.build_number = build_number
        self.task_id = str(uuid.uuid4()).replace("-", "")[:32]
        self._es_jenkins_server_info = EsJenkinsServerEnv()
        self._es_biz_core_env = EsBizCoreEnv()
        self.params = self.get_build_params()

    def get_jenkins_loginfo(self):
        result = self._es_jenkins_server_info.query_all_without_time()
        print(result)
        return result

    def match_login_info(self):
        result = self._es_biz_core_env.query_by_filter_without_sort({"env_id": self.env_id})
        print(result)
        return result

    # get env_id and fullPath from interface
    def get_build_params(self):
        all_loginfo = self.get_jenkins_loginfo()
        env_biz_info = self.match_login_info()
        jenkins_url = env_biz_info[1][0].get("jenkins_url", "")
        username = ""
        password = ""
        for loginfo in all_loginfo[1]:
            if loginfo.get("jenkins_url", "") == jenkins_url:
                username = loginfo.get("jenkins_username")
                password = loginfo.get("jenkins_password")
        js = JenkinsClient(
            jenkins_url,
            username,
            password
        )
        param = js.get_build_parameters(self.job_name, self.build_number)
        print(param)
        return param

    # save version_execute_dict by version_list from arifactory
    def save_version_execute(self):
        full_path = self.params.get("ftpAllPath", "")
        versionExe = VersionEnvService()
        full_path_list = full_path.split("/")
        last_success_version = versionExe.get_last_success_version(full_path)
        curr_version = full_path_list[-1].replace('.tar', '')
        version_list = versionExe.get_version_list(full_path, curr_version, last_success_version)
        version_task_dict = versionExe.initialize_version_task_dict(
            version_list,
            last_success_version,
            curr_version,
            False
        )
        versionExe.insert_initial_execute_records(
            self.task_id,
            self.env_id,
            last_success_version,
            curr_version,
            version_task_dict,
            False
        )

    async def execute(self):
        self.save_version_execute()
        # 获取当前版本
        try:
            version = self.get_bs_version(self.env_id)
            print("单点回溯流程版本", version)
            if not version:
                raise ValueError("Failed to get base station version - empty response")
        except Exception as version_error:
            logger.error(f"Error getting base station version: {str(version_error)}")
            self._send_email(
                subject="单点回溯流程 - 版本获取异常",
                content=f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {self.env_id}"
            )
            return
        try:
            next_version = self._general_next_version(self.task_id, "version_rollback", version)
            print(next_version)
            if next_version:
                # 放队列，失败场景，应该先回退再升级。
                # 触发回退任务
                # 存放cron_task，subtype类型为"version_upgrade"
                umeres = UmeService().version_rollback(self.env_id, version + ".tar")
                if not umeres.result:
                    # 发送邮件
                    self._send_email(
                        subject="单点回溯流程-发送版本回退失败",
                        content=f"发送版本回退失败\n最后测试版本: {version}"
                    )
                    return f"单点回溯流程-发送版本回退失败,version:{next_version}"
                else:
                    self._send_email(
                        subject=f"单点回溯流程-发送版本回退成功，next_version版本为{next_version}",
                        content=f"发送版本回退失败\n最后测试版本: {version}"
                    )
                    pipelineStageId = umeres.data.pipelineStageId
                    pipelineUuid = umeres.data.pipelineUuid
                    recordId = umeres.data.recordId
                    self.save_local_test_task(self.task_id, self.env_id, next_version, "version_rollback",
                                              "online_test", self.job_name, self.build_number,
                                              pipelineUuid, pipelineStageId, recordId)
                logger.info(f"Generated next version for version_rollback testing: {next_version}")
                return next_version
            else:
                logger.info("No more versions to test")
                self._send_email(
                    subject="线上复测完成",
                    content=f"版本回溯已完成\n最后测试版本: {version}"
                )
                return "No more versions to test"
        except Exception as next_version_error:
            logger.error(f"Error generating next version: {str(next_version_error)}")
            self._send_email(
                subject="线上复测失败 - 版本生成异常",
                content=f"生成下一个测试版本时发生错误: {str(next_version_error)}\n当前版本: {version}"
            )
            return f"ERROR:{next_version_error}"


class TaskIndependenceRollbackService(Service):
    def __init__(self, task_id: str):
        super().__init__(service_type="", subtype="", state="", needed={},
                         es=EsCronTask())
        self.task_id = task_id
        self._es_version_env = EsVersionEnv()
        self._es_version_execute = EsVersionExecute()

    def execute(self):
        # 1.获取当前task_id执行信息获取version_execute信息，如果没有需要根据version_list重新插入
        # 2.获取当前基站版本
        # 3.获取当前基站版本执行结果从version_execute
        # 4.判定下一步动作是应该回退还是升级还是继续执行线上复测，放入定时任务
        version_env_result = self._es_version_env.query_by_filter_without_sort({"task_id": self.task_id})
        print(version_env_result)
        if not version_env_result[1]:
            self._send_email(
                subject="根据task_id回溯失败",
                content=f"没有此task_id：{self.task_id}的任务"
            )
            return
        else:
            task_version_env_result = version_env_result[1][0]
            version_list = task_version_env_result.get("version_list")
            version_list = eval(version_list) if type(version_list) == str else version_list
            last_success_version = task_version_env_result.get("last_success_version", "")
            version_test_result = task_version_env_result.get("version_test_result", "False")
            env_id = task_version_env_result.get("env_id", "")
            jenkins_job_name = task_version_env_result.get("jenkins_job_name", "")
            jenkins_build_number = task_version_env_result.get("jenkins_build_number", "")
            curr_version = self.get_bs_version(env_id)
        if len(version_list) == 1:
            self._send_email(
                subject="根据task_id回溯结束",
                content=f"此task_id：{self.task_id}的任务只有一个版本说明已经发布"
            )
            return

        version_execute_result = self._es_version_execute.query_by_filter_without_sort({"task_id": self.task_id})
        print(version_execute_result)
        if not version_execute_result:
            # 插入完整的，但是从当前版本进行线上复测，这个时候会有一种极端情况就是他的上一个版本，比二分法回退的版本号要更靠前
            version_task_dict = VersionEnvService().initialize_version_task_dict(
                version_list,
                last_success_version if not bool(eval(version_test_result)) else curr_version,
                curr_version,
                bool(eval(version_test_result))
            )
            logger.info(f"version_task_dict saved for job: {version_task_dict}")
            # 插入初始执行记录
            VersionEnvService().insert_initial_execute_records(
                self.task_id,
                env_id,
                last_success_version if not bool(eval(version_test_result)) else curr_version,
                curr_version,
                version_task_dict,
                bool(eval(version_test_result))
            )
            msg = LogJobInfo(
                log_name="",
                job_name=jenkins_job_name,
                build_number=jenkins_build_number,
                env_id=env_id,
                task_id=self.task_id,
                service_type="online_test",
                subtype="normal"
            )
            asyncio.run(CiAgentsQueue().push_info(msg))
            logger.info(f"单点触发线上复测, task_id :{self.task_id}")

        else:
            # 寻找当前版本执行结果如果有判断回退还是升级，没有的话进入复测
            version_execute_result_list = version_execute_result[1]
            test_result = ""
            for execute_dict in version_execute_result_list:
                if curr_version == execute_dict.get("version", ""):
                    test_result = execute_dict.get("test_result", "")
            if test_result == "":
                # 没有直接放入复测队列
                msg = LogJobInfo(
                    log_name="",
                    job_name=jenkins_job_name,
                    build_number=jenkins_build_number,
                    env_id=env_id,
                    task_id=self.task_id,
                    service_type="online_test",
                    subtype="normal"
                )
                asyncio.run(CiAgentsQueue().push_info(msg))
                logger.info(f"单点触发线上复测, task_id :{self.task_id}")
            elif test_result == "fail":
                # 回退
                try:
                    version = self.get_bs_version(env_id)
                    print("单点回溯流程版本", version)
                    if not version:
                        raise ValueError("Failed to get base station version - empty response")
                except Exception as version_error:
                    logger.error(f"Error getting base station version: {str(version_error)}")
                    self._send_email(
                        subject="单点回溯流程 - 版本获取异常",
                        content=f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {env_id}"
                    )
                    return
                try:
                    next_version = self._general_next_version(self.task_id, "version_rollback", version)
                    print(next_version)
                    if next_version:
                        # 放队列，失败场景，应该先回退再升级。
                        # 触发回退任务
                        # 存放cron_task，subtype类型为"version_upgrade"
                        umeres = UmeService().version_rollback(env_id, version + ".tar")
                        if not umeres.result:
                            # 发送邮件
                            self._send_email(
                                subject="单点回溯流程-发送版本回退失败",
                                content=f"发送版本回退失败\n最后测试版本: {version}"
                            )
                            return f"单点回溯流程-发送版本回退失败,version:{next_version}"
                        else:
                            self._send_email(
                                subject=f"单点回溯流程-发送版本回退成功，next_version版本为{next_version}",
                                content=f"发送版本回退失败\n最后测试版本: {version}"
                            )
                            pipelineStageId = umeres.data.pipelineStageId
                            pipelineUuid = umeres.data.pipelineUuid
                            recordId = umeres.data.recordId
                            self.save_local_test_task(self.task_id, env_id, next_version, "version_rollback",
                                                      "online_test", jenkins_job_name, jenkins_build_number,
                                                      pipelineUuid, pipelineStageId, recordId)
                        logger.info(f"Generated next version for version_rollback testing: {next_version}")
                        return next_version
                    else:
                        logger.info("No more versions to test")
                        self._send_email(
                            subject="线上复测完成",
                            content=f"版本回溯已完成\n最后测试版本: {version}"
                        )
                        return "No more versions to test"
                except Exception as next_version_error:
                    logger.error(f"Error generating next version: {str(next_version_error)}")
                    self._send_email(
                        subject="线上复测失败 - 版本生成异常",
                        content=f"生成下一个测试版本时发生错误: {str(next_version_error)}\n当前版本: {version}"
                    )
                    return f"ERROR:{next_version_error}"
            else:
                # 升级
                # 生成下一个版本
                try:
                    next_version = self._general_next_version(self.task_id, "version_upgrade", curr_version)
                    if next_version:
                        # 放队列，成功场景，直接升级。
                        # 存放cron_task任务，subtype="online_test",
                        umeres = UmeService().version_upgrade(env_id, next_version + ".tar")
                        if not umeres.result:
                            # 发送邮件
                            self._send_email(
                                subject=f"发送版本升级失败版本为{next_version}",
                                content=f"发送版本升级失败\n最后测试版本: {curr_version}"
                            )
                            return f"单点回溯流程-发送版本升级失败,version:{next_version}"
                        else:
                            self._send_email(
                                subject=f"发送版本升级成功版本为{next_version}",
                                content=f"发送版本升级成功\n最后测试版本: {curr_version}"
                            )
                            pipelineStageId = umeres.data.pipelineStageId
                            pipelineUuid = umeres.data.pipelineUuid
                            recordId = umeres.data.recordId
                            self.save_local_test_task(self.task_id, env_id, next_version, "version_upgrade",
                                                      "online_test", jenkins_job_name, jenkins_build_number,
                                                      pipelineUuid, pipelineStageId, recordId)
                        logger.info(f"Generated next version for version_upgrade testing: {next_version}")
                        return next_version
                    else:
                        logger.info("No more versions to test")
                        self._send_email(
                            subject="线上复测完成",
                            content=f"版本回溯已完成\n最后测试版本: {curr_version}"
                        )
                        return "No more versions to test"
                except Exception as next_version_error:
                    logger.error(f"Error generating next version: {str(next_version_error)}")
                    self._send_email(
                        subject="线上复测失败 - 版本生成异常",
                        content=f"生成下一个测试版本时发生错误: {str(next_version_error)}\n当前版本: {curr_version}"
                    )
                    return f"ERROR:{next_version_error}"


if __name__ == "__main__":
    # ind = IndependenceRollbackService("RAN3-上海高频CI团队-VAT1016", "smoke-1016-udp-tcp",
    #                                   1656)
    # print(ind.task_id)
    # # ind.get_build_params()
    # ind.execute()
    ind = TaskIndependenceRollbackService("2bca1618cd944d9894b152895170be61")
    ind.execute()
