"""
@File: Zip.py
@Author: 许王禾子10333721
@Time: 2023/9/1 下午5:35
@License: Copyright 2022-2030
@Desc: None
"""
import os
import zipfile
from infrastructure.logger.logger import logger


class Zip(object):

    @staticmethod
    def unzip_file(zipFilePath: str, extractedDir: str = None) -> list:
        logger.debug(f"zip file path: {zipFilePath}")
        if not zipFilePath.endswith(".zip"):
            logger.debug(f"unzip file {zipFilePath} failed, file should be zip type")
            return []
        # 默认解压到与压缩文件同名的解压文件夹下
        if not extractedDir:
            extractedDir = zipFilePath.split(".zip", 1)[0]
        if not os.path.exists(extractedDir):
            os.mkdir(extractedDir)
        with zipfile.ZipFile(zipFilePath, 'r') as zip_ref:
            # 解压ZIP文件中的所有内容到目标目录
            zip_ref.extractall(extractedDir)
        logger.debug(f"unzip file success, extracted dir: {extractedDir}")
        files = []
        for file in os.listdir(extractedDir):
            files.append(os.path.join(extractedDir, file))
        return files
