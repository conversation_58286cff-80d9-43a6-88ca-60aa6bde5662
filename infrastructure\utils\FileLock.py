"""
@File: FileLock.py
@Author: 许王禾子10333721
@Time: 2024/3/6 上午9:10
@License: Copyright 2022-2030
@Desc: None
"""
import threading
import typing

import portalocker
from portalocker.constants import LOCK_EX, LOCK_NB

KEEP = -1
KEEP_TRYING = 9999


class FileLock:
    def __init__(self, filename: str, timeout=0.1, flags=LOCK_NB | LOCK_EX, mode='a+', **file_open_kwargs):
        """
        filename: file文件地址;

        timeout: 锁持有的超时释放时间(s), 若为 KEEP(-1) 则一直持有;

        flags: 锁的属性，LOCK_EX：排它; LOCK_SH：共享; LOCK_NB: 非阻塞

        mode: 文件打开模式

        file_open_kwargs: 其他的文件打开属性, 如 encoding
        """
        self.fh: typing.Optional[typing.IO] = None
        self._filename = filename
        self._timeout = timeout
        self._is_locked = False
        self._lock = portalocker.Lock(self._filename, flags=flags, mode=mode, **file_open_kwargs)
        self._timer = None

    def acquire(self):
        self.try_acquire(KEEP_TRYING)

    def try_acquire(self, timeout=1):
        """
         timeout: 获取锁的超时时间
        """
        try:
            # print("Attempting to acquire the FileLock.")
            self._lock.acquire(timeout, 0.01)
            self._is_locked = True
            self.fh = self._lock.fh
            # print("FileLock acquired.")
            if self._timeout != KEEP:
                self._timer = threading.Timer(self._timeout, self.release)
                self._timer.start()
                # print(f"FileLock timeout set for {self._timeout} seconds.")
            return True
        except portalocker.exceptions.LockException:
            # print("Failed to acquire FileLock.")
            return False

    def release(self):
        if self._is_locked:
            self._lock.release()
            self._is_locked = False
            # print("FileLock released.")
            if self._timer is not None:
                self._timer.cancel()

    def __enter__(self):
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()


if __name__ == '__main__':
    filename = "test.txt"
    with FileLock(filename, timeout=KEEP) as fl:
        fl.fh.seek(0)
        lines = fl.fh.readlines()
    print(lines)
