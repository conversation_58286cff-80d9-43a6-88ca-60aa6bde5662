# -*&- coding: utf-8 -*-
from pydantic import BaseModel, Field
from fastapi import Query
from enum import Enum

class ActionExecuteInfo(BaseModel):
    pipelineStageId: str = Field(Query(description="流水线阶段ID"))
    pipelineUuid: str = Field(Query(description="流水线唯一ID"))
    recordId: int = Field(Query(description="流水线记录ID"))


class ActionTypeEnum(str, Enum):
    BASIC = "non_nested"
    COMPPSITE = "nested"

class ActionExecuteLogs(BaseModel):
    pipelineStageId: str = Query(description="流水线阶段ID")
    type: ActionTypeEnum = Query(description="日志类型，可选值：['non_nested', 'nested']")
    recordId: int = Query(description="流水线记录ID")
 
    class Config:
        use_enum_values = True  # 添加此配置项   
