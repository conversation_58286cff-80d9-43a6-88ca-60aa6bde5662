from service.run_service.online_retest_service import RemoteTestService
from service.run_service.version_update_service import VersionUpdateService
from service.run_service.version_rollback_service import VersionRollbackService


class ServiceFactory:
    @staticmethod
    def create_service(service_type, subtype, state, needed, task_id, env_id, es, cron_task_id):
        if service_type == "online_test":
            return RemoteTestService(service_type, subtype, state, needed, task_id, env_id, es, cron_task_id)
        elif service_type == "version_rollback":
            return VersionRollbackService(service_type, subtype, state, needed, task_id, env_id, es, cron_task_id)
        elif service_type == "version_upgrade":
            return VersionUpdateService(service_type, subtype, state, needed, task_id, env_id, es, cron_task_id)
        else:
            raise ValueError(f"Unknown service type: {service_type}")
