# coding=utf-8
import logging
from infrastructure.<PERSON>ton import Singleton
from service.jenkins_job_service import JenkinsJobService
from service.testcase_log_analysis import LogAnalysisService
from service.test_job_service import LocalTestJobService, OnlineTestJobService
from service.check_env_service import EnvMonitorService
from service.ume_action_service import UmeV<PERSON><PERSON>Upgrade, UmeVersionRollback
from service.logging_service import LoggingService
from infrastructure.logger.logger import logger

# Service registry mapping service types to their implementation classes
AGENTS_SERVICE_OBJECT = {
    'jenkins_job_save': JenkinsJobService,
    'log_analysis': LogAnalysisService,
    'env_check': EnvMonitorService,
    'local_test': LocalTestJobService,
    'online_test': OnlineTestJobService,
    'version_upgrade': UmeVersionUpgrade,
    'version_rollback': UmeVersionRollback,
    'email': None
}


@Singleton
class AgentsServiceFactory(object):
    """
    Factory class for creating agent service instances based on service type.
    Implements the Factory Method pattern to create appropriate service objects.
    """

    def create_agents_service(self, service_msg_dict):
        """
        Create and return a service instance based on the service_type in the message dictionary.

        Args:
            service_msg_dict (dict): Dictionary containing service information including service_type

        Returns:
            object: An instance of the appropriate service class, or None if service_type is not supported
        """
        service_type = service_msg_dict.get('service_type')

        if not service_type:
            logger.error("Service type not provided in message dictionary")
            LoggingService.log_execution(
                service_type="AgentsServiceFactory",
                operation="create_agents_service",
                status="failure",
                details={"error": "Service type not provided", "message": service_msg_dict},
                task_id=service_msg_dict.get('task_id'),
                env_id=service_msg_dict.get('env_id')
            )
            return None

        if service_type in AGENTS_SERVICE_OBJECT and AGENTS_SERVICE_OBJECT[service_type] is not None:
            # Log the service creation
            LoggingService.log_execution(
                service_type="AgentsServiceFactory",
                operation="create_agents_service",
                status="success",
                details={
                    "created_service": service_type,
                    "task_id": service_msg_dict.get('task_id'),
                    "env_id": service_msg_dict.get('env_id')
                },
                task_id=service_msg_dict.get('task_id'),
                env_id=service_msg_dict.get('env_id'),
                service_key=service_type
            )

            # Create and return the service instance
            return AGENTS_SERVICE_OBJECT[service_type](service_msg_dict)
        else:
            logger.error(f"Unsupported service type: {service_type}")
            LoggingService.log_execution(
                service_type="AgentsServiceFactory",
                operation="create_agents_service",
                status="failure",
                details={"error": f"Unsupported service type: {service_type}"},
                task_id=service_msg_dict.get('task_id'),
                env_id=service_msg_dict.get('env_id'),
                service_key=service_type
            )
            return None
