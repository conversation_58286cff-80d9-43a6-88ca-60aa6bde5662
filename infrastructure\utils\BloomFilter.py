"""
@File: BloomFilter.py
@Author: 许王禾子10333721
@Time: 2024/4/28 17:15
@License: Copyright 2022-2030
@Desc: None
"""
import threading
from pybloom_live import BloomFilter


class Bloom:
    def __init__(self, capacity, error_rate=0.001):
        """
        capacity: 过滤器容量
        error_rate: 允许的误差率
        """
        self.capacity = capacity
        self.error_rate = error_rate
        self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)

    def add(self, item):
        """添加元素到过滤器中"""
        self.bloom_filter.add(item)

    def check(self, item):
        """检查元素是否在过滤器中"""
        return item in self.bloom_filter

    def reset_filter(self):
        """重置过滤器"""
        self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)


class TimedBloom:
    def __init__(self, capacity, reset_time, error_rate=0.001):
        """
        capacity: 过滤器容量
        error_rate: 允许的误差率
        reset_time: 过滤器重置时间间隔（秒）
        """
        self.capacity = capacity
        self.error_rate = error_rate
        self.reset_time = reset_time
        self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)
        self.lock = threading.Lock()
        self.start_timer()

    def add(self, item):
        """添加元素到过滤器中"""
        with self.lock:
            self.bloom_filter.add(item)

    def check(self, item):
        """检查元素是否在过滤器中"""
        with self.lock:
            return item in self.bloom_filter

    def reset_filter(self):
        """重置过滤器"""
        with self.lock:
            self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)

    def start_timer(self):
        """启动一个定时器，定时重置过滤器"""
        self.timer = threading.Timer(self.reset_time, self.reset_and_restart_timer)
        self.timer.start()

    def reset_and_restart_timer(self):
        """重置过滤器并重新启动定时器"""
        self.reset_filter()
        self.start_timer()

    def stop_timer(self):
        """停止定时器"""
        self.timer.cancel()