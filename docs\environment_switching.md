# Environment Switching Feature

## Overview

This document describes the implementation of the environment switching feature, which allows users to select from predefined environments or configure a custom environment with a specific IP address and port.

## Features

1. **Predefined Environments**
   - Debug Environment (***********:3303)
   - Test Environment (*************:3303)
   - Development Environment (************:3303)

2. **Custom Environment Configuration**
   - Users can enter a custom IP address and port
   - Settings are saved to localStorage for persistence

3. **Environment Indicator**
   - Current environment is displayed in the header
   - Settings button provides quick access to environment configuration

## Implementation

### Components

1. **EnvSettingsDialog.vue**
   - Dialog component for selecting and configuring environments
   - Provides radio buttons for predefined environments
   - Includes input fields for custom environment configuration
   - Displays the current API URL for confirmation

2. **apiUrlService.js**
   - Service for managing the API URL
   - Provides functions for getting and setting the API URL
   - Handles loading saved settings from localStorage

3. **envInfoApi.js**
   - Uses the API URL from apiUrlService
   - Exports an updateApiUrl function to update the API URL at runtime
   - Used for environment information management API calls

4. **api.js**
   - Uses the base URL derived from apiUrlService (removing '/env_info' suffix)
   - Exports an updateBaseUrl function to update the base URL at runtime
   - Used for agent logs and pipeline API calls

5. **App.vue**
   - Includes the environment settings button in the header
   - Displays the current environment name
   - Handles updating both API URLs when environment settings change

### Data Flow

1. **Initial Load**
   - On application startup, `apiUrlService.js` loads the saved environment settings from localStorage
   - If no settings are found, it uses the default environment (Debug)
   - The current environment name is displayed in the header

2. **Environment Selection**
   - User clicks the environment settings button in the header
   - The EnvSettingsDialog component is displayed
   - User selects a predefined environment or configures a custom one
   - User clicks "Save" to apply the changes

3. **Environment Update**
   - The new API URL is saved to localStorage
   - The `updateApiUrl` function is called to update the API URL in the envInfoApi service
   - The `updateBaseUrl` function is called to update the base URL in the api service
   - The current environment name is updated in the header
   - The page is reloaded to apply the new API URLs to all components

## Technical Details

### Environment Settings Storage

Environment settings are stored in localStorage as a JSON string with the following structure:

```json
{
  "selectedEnv": "debug", // or "test", "dev", "custom"
  "customEnv": {
    "ip": "*************",
    "port": "3303"
  }
}
```

### URL Formats

#### Base API URL

The base API URL is constructed using the following format:

```
http://{ip}:{port}
```

For example:
- Debug: http://***********:3303
- Test: http://*************:3303
- Development: http://************:3303
- Custom: http://{custom_ip}:{custom_port}

#### Environment Info API URL

The environment info API URL is derived from the base URL by adding the '/env_info' suffix:

```
http://{ip}:{port}/env_info
```

For example:
- Debug: http://***********:3303/env_info
- Test: http://*************:3303/env_info
- Development: http://************:3303/env_info
- Custom: http://{custom_ip}:{custom_port}/env_info

### Page Reload

When the environment is changed, the page is reloaded to ensure that all components use the new API URL. This is done using:

```javascript
this.$router.go(0);
```

## User Guide

### Switching Environments

1. Click the environment settings button in the header (displays the current environment name)
2. Select one of the predefined environments or choose "Custom Environment"
3. If "Custom Environment" is selected, enter the IP address and port
4. Click "Save" to apply the changes
5. The page will reload with the new environment settings

### Viewing Current API URL

The current API URL is displayed in the environment settings dialog for confirmation.

## Future Enhancements

1. **Environment Health Check**
   - Add a feature to check if the selected environment is accessible
   - Display a status indicator for each environment

2. **Environment-specific Settings**
   - Allow users to configure additional settings for each environment
   - Support different authentication methods for different environments

3. **Environment Presets**
   - Allow users to save custom environments as presets
   - Provide a dropdown to select from saved presets
