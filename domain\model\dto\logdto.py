from pydantic import BaseModel, Field, model_validator
from typing import Literal, Optional
import uuid


class LogJobInfo(BaseModel):
    log_name: str
    job_name: str = ""
    build_number: str = ""
    env_id: str

    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    service_type: str
    subtype: Optional[Literal['normal', "first_time", 'rollback', 'update']] = "normal"

    @model_validator(mode='after')
    def check_subtype(cls, values):
        if values.subtype in 'normal':
            if not values.build_number or \
                    not values.job_name:
                raise ValueError("当subtype为normal时，build_number和job_name不为空!")
        return values
