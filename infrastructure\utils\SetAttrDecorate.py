def set_dict_to_obj_attr(cls):
    def inner(*args, **kwargs):
        obj = cls(*args, **kwargs)
        for d in args:
            if isinstance(d, dict):
                for key, value in d.items():
                    if not hasattr(obj, key):
                        setattr(obj, key, value)
            break
        return obj

    return inner


def set_last_dict_to_obj_attr(cls):
    def inner(*args, **kwargs):
        obj = cls(*args, **kwargs)
        lastDict = {}
        for d in args:
            if isinstance(d, dict):
                lastDict = d
        for key, value in lastDict.items():
            setattr(obj, key, value)
        return obj

    return inner
