# -*-coding:utf-8-*-
from pydantic import BaseModel, Field
from enum import Enum
from domain.model.tools.Action import TdlResponse


class ActionExecuteLogs(BaseModel):
    historyFilePath: str = Field(description="历史日志文件路径")
    hasChildren: bool = Field(description="是否包含子动作")
    pipelineStageId: str = Field(description="流水线阶段ID")
    recordId: int = Field(description="记录ID")
    actionName: str = Field(description="动作名称")


class ActionExecuteLogsResponse(TdlResponse):
    data: list[ActionExecuteLogs] = Field(description="执行结果数据")

    @property
    def execute_logs(self):
        return [log.model_dump() for log in self.data]




