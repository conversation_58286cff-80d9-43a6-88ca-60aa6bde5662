# -*-coding:utf-8-*-
from pydantic import BaseModel, Field
from enum import Enum
from domain.repository.tools.ActionRepository import ActionRepository

class ActionTypeEnum(str, Enum):
    BASIC = "basic"
    COMPOSITE = "composite"


class ActionExecutor(BaseModel):
    actionId: str = Field(description="action的唯一标识")
    para: dict = Field(default={}, description="action的执行参数，可以通过获取action的schema信息这个接口获取")
    config: dict = Field(default={}, description="配置信息，指的是测试环境的配置信息，也被叫成资源配置信息或者config文件，里面必须是json格式，可以通过查询测试环境的配置信息这个接口获取")
    actionType: ActionTypeEnum = Field(default="basic",  description="action类型，basic指的是basic action，简称BA；composite指的是composite action，简称CA")
    refs: list = Field(default=[], description="依赖的执行id列表")
    pipelineStageId: str = Field(default="", description="流水线阶段ID")
    envId: str = Field(default="", description="环境ID")
    groupNo: str = Field(default="", description="组网分组编号")
    user: str = Field(default="", description="用户标识")

    def __init__(self, env_config, **data):
        super().__init__(**data)
        self._env_config = env_config
        self.config = env_config.env_config
        self.envId = env_config.env_id

    @property
    def env_config(self):
        return self._env_config
    
    @property
    def params(self):
        return self.model_dump()

    def execute(self):
        return ActionRepository.execute(self.params)


class TdlResponse(BaseModel):
    failReason: str = Field(description="失败原因")
    result: bool = Field(description="执行结果")
    data: dict = Field(description="执行结果数据")

    def to_json(self):
        return self.model_dump()


class ActionExecuteResult(BaseModel):
    pipelineStageId: str = Field(description="流水线阶段ID")
    pipelineUuid: str = Field(description="流水线唯一ID")
    recordId: int = Field(description="流水线记录ID")
    taskId: str = Field(description="任务ID")


class ActionExecuteResultResponse(TdlResponse):
    data: ActionExecuteResult = Field(description="执行结果数据")

    @property
    def execute_result(self):
        return self.data.model_dump()


class Action:
    @staticmethod
    def execute(executor: ActionExecutor):
        return executor.execute()

    def get_execution_info(params):
        return ActionRepository.get_execution_info(params)

    def get_execution_logs(params):
        return ActionRepository.get_execution_logs(params)
