# -*- coding:utf-8 -*

from domain.models.dsp_initialization.HfDspInit import HfDspInit
from domain.models.dsp_initialization.NrDspInit import NrDspInit
from .DspMonitorBoardAgent import *
from .DspMonitorTelnet import *

WEBMNT_CONFIG = "WebMntConfig"


class DspMonitor(object):
    __singleton = None
    ToolType = "webMnt"
    pmuQuery = {}

    def __init__(self):
        self.cfg = None
        self.boards = {}
        self.client = None
        self.serverport = MONITOR_WEBMNT_CLIENT_PORT
        self.dspmonitorip = MONITOR_HOST_IP
        self.core_type_map = {}
        self.checkflag = True

    @staticmethod
    def getinstance():
        if DspMonitor.__singleton is None:
            DspMonitor.__singleton = DspMonitor()
        return DspMonitor.__singleton

    def isjavaVersion(self, res):
        return (-1 != res.find("Java"))

    def isWebMntVersion(self, res):
        return (-1 != res.find("WebMnt"))

    def resetBoard(self, boardtype, boardidx):
        key = boardtype + "_" + str(boardidx)
        if key in list(self.boards.keys()):
            self.boards.pop(key)

    async def getBoard(self, boardtype, boardidx, cfg):
        self.cfg = cfg
        if (not self.client):
            self.client = DspMonitorClient(self.dspmonitorip)
        if boardtype not in list(self.cfg.keys()):
            raise MonitorExcption(boardtype + "not in board cfg ")
        if "chips" not in list(self.cfg[boardtype].keys()):
            raise MonitorExcption("board cfg no chips cfg for " + boardtype + str(boardidx))
        if boardidx >= len(self.cfg[boardtype]["chips"]):
            raise MonitorExcption("board idx not in cfg " + boardtype + " " + str(boardidx))
        key = boardtype + "_" + str(boardidx)
        if DspMonitor.ToolType == "webMnt":
            # webmnt同时只支持一个board单板。当已有单板，由新来单板chip名时，清理掉老self.boards
            if key not in list(self.boards.keys()) and len(self.boards.keys()) != 0:
                self.boards = {}
        if key not in list(self.boards.keys()):
            chip = self.cfg[boardtype]["chips"][int(boardidx)]
            coresCfg = self.cfg[boardtype]["cores"]
            coretype_list = []
            if boardtype in self.core_type_map.keys():
                coretype_list = self.core_type_map[boardtype]
            if 'HF' in self.cfg[boardtype].keys():
                coresCfg = coresCfg[int(boardidx)]
            self.boards[key] = ChipOPAgent(boardtype, boardidx, chip, coresCfg,
                                           self.client, coretype_list, self.checkflag, self.cfg[WEBMNT_CONFIG])
        if (not self.boards[key]):
            raise MonitorExcption("no board cfg " + boardtype + str(boardidx))
        return self.boards[key]

    def WaitClientStart(self, client):
        # 前面执行了reset必定会没有立即生效，等待结束，
        while (not client.logintest()):
            time.sleep(1)

        while (not client.logintest()):
            time.sleep(1)

    def MonitorReset_webmnt(self, cfg, flag=1):
        # flag = 0, 手动配置单板；flag = 1, 自动配置单板
        bresetok = False
        self.cfg = cfg
        if MONITOR_IP in self.cfg:
            self.dspmonitorip = self.cfg[MONITOR_IP]
        self.boards = {}
        # 客户端启动后服务端没有启动多重启几次
        resetCount = 0
        while (not bresetok) and (resetCount < MONITOR_RESET_COUNT):
            try:
                self.client = DspMonitorClient(self.dspmonitorip)
                self.client.login()
                self.client.Reset()  # 复位监控工具，清空所有的配置。关闭client
                server = DspMonitorServer(self.dspmonitorip, self.serverport)  # 监控工具完成启动后,创建服务端连接通路
                bresetok = server.logintest()
                if bresetok:
                    logging.info("start end,begin to config board....")
                    server.InitServer(cfg, flag)
                    self.core_type_map = server.core_type_map
                    server.close()
            except Exception as e:
                bresetok = False
                logging.exception(e)
            resetCount += 1
        if bresetok:
            self.client = DspMonitorClient(self.dspmonitorip, MONITOR_WEBMNT_CLIENT_PORT)
        else:
            raise MonitorExcption("WebMnt monitor Init Fail, Maybe login WebMnt Error.")

    def MonitorClose(self):
        self.client = DspMonitorClient(self.dspmonitorip)
        self.client.Close()  # 关闭监控工具

    def MonitorStart(self, cfg):
        self.setMonitorCfg(cfg)
        self.client = DspMonitorClient(self.dspmonitorip)
        result = self.client.Login()
        DspMonitorServer(self.dspmonitorip, self.serverport).reset_server(cfg)
        return result

    @staticmethod
    def setCfg(cfg):
        DspMonitor.getinstance().setMonitorCfg(cfg)

    #  setMonitorCfg是工具已经存在，但是用例重新开始，并且连接断开
    def setMonitorCfg(self, cfg):
        self.cfg = cfg
        if (MONITOR_IP in list(self.cfg.keys())):
            self.dspmonitorip = self.cfg[MONITOR_IP]

    @staticmethod
    def SetMapStatusCheck(flag):
        DspMonitor.getinstance().MonitorSetMapStatusCheck(flag)

    @staticmethod
    def Reset(cfg, tool=1, flag=1):
        # tool = 0, 连接javamnt； tool = 1，连接webmnt
        # flag = 0, 手动配置单板；flag = 1, 自动配置单板
        DspMonitor.getinstance().MonitorReset_webmnt(cfg, int(cfg[WEBMNT_CONFIG].get(AUTO_CFG_BOARD_SWITCH, flag)))

    @staticmethod
    def Start(cfg):
        return DspMonitor.getinstance().MonitorStart(cfg)

    @staticmethod
    def Close():
        DspMonitor.getinstance().MonitorClose()

    @staticmethod
    def TestEnd():
        DspMonitor.getinstance().client.close()

    @staticmethod
    async def ContractCall(brdname, brdidx, coreid, cfg, funname, *params):
        brdidx = int(brdidx)
        coreid = int(coreid)
        bok = False
        res = None
        while not bok:
            try:
                board = await DspMonitor.getinstance().getBoard(brdname, brdidx, cfg)
                fun = "board.%s(%s, %s)" % (str(funname), str(coreid), ",".join(params))
                logging.info(fun)
                res = eval(fun)
                bok = True
            except MonitorReOpenExcption as e:
                logging.info(">>>>>>>>>>>cmd continue to run after Monitor restart")
                DspMonitor.getinstance().resetBoard(brdname, brdidx)
            except Exception as e:
                return e
        return res

    @staticmethod
    def ReadMem(brdname, brdidx, coreid, addr, size=4):
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "ReadMem", str(addr), str(size))

    @staticmethod
    def WriteMem(brdname, brdidx, coreid, addr, value, size=0, flag=0):
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "WriteMem", str(addr), str(size), str(value), str(flag))

    @staticmethod
    async def ReadVar(brdname, brdidx, coreid, cfg, var):
        wrapvar = '"%s"' % str(var)
        return await DspMonitor.ContractCall(brdname, brdidx, coreid, cfg, "ReadVar", wrapvar)

    @staticmethod
    async def WriteVar(brdname, brdidx, coreid, cfg, var, value, flag=0):
        wrapvar = '"%s"' % str(var)
        return await DspMonitor.ContractCall(brdname, brdidx, coreid, cfg, "WriteVar", wrapvar, str(value), str(flag))

    @staticmethod
    def ReadMem2File(brdname, brdidx, coreid, addr, len, file):
        wrapfile = '"%s"' % str(file)
        wrapfile = wrapfile.replace("\\", "/")
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "ReadMemtoFile", str(addr), str(len), wrapfile)

    @staticmethod
    def WriteFile2Mem(brdname, brdidx, coreid, addr, file):
        wrapfile = '"%s"' % str(file)
        wrapfile = wrapfile.replace("\\", "/")
        logging.info(wrapfile)
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "WriteFiletoMem", str(addr), wrapfile)

    @staticmethod
    def ReadVarInfo(brdname, brdidx, coreid, var):
        wrapvar = '"%s"' % str(var)
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "ReadVarInfo", wrapvar)

    @staticmethod
    def ReadVarChildrenNameList(brdname, brdidx, coreid, var):
        wrapvar = '"%s"' % str(var)
        return DspMonitor.ContractCall(brdname, brdidx, coreid, "ReadVarChildrenNameList", wrapvar)

    @staticmethod
    def WriteStr(brdName, brdidx, coreid, addr, mystr, maxsize):
        minlen = min(maxsize, len(mystr))
        tmpaddr = addr
        for cc in mystr[0:minlen]:
            DspMonitor.WriteMem(brdName, brdidx, coreid, tmpaddr, int(ord(cc)), 1)
            tmpaddr = tmpaddr + 1

    @staticmethod
    def LogGetModul(brdName, brdidx, coreid):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogGetModul")

    @staticmethod
    def LogLevelSet(brdName, brdidx, coreid, modul, level):
        modul = '"%s"' % str(modul)
        level = '"%s"' % str(level)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogLevelSet", str(modul), str(level))

    @staticmethod
    def LogStartSet(brdName, brdidx, coreid, start):
        start = '"%s"' % str(start)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogStartSet", str(start))

    @staticmethod
    def LogSave2File(brdName, brdidx, coreid, file):
        wrapfile = '"%s"' % str(file)
        wrapfile = wrapfile.replace("\\", "/")
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogSave2File", wrapfile)

    @staticmethod
    def LogExtBoardSet(brdName, brdidx, coreid, brdType):
        brdType = '"%s"' % str(brdType)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtBoardSet", str(brdType))

    @staticmethod
    def LogExtFilterSet(brdName, brdidx, coreid, filter):
        filter = '"%s"' % str(filter)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtFilterSet", filter)

    @staticmethod
    def LogExtConditionSet(brdName, brdidx, coreid, condition):
        condition = '"%s"' % str(condition)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtConditionSet", condition)

    @staticmethod
    def LogExtLevelSet(brdName, brdidx, coreid, module, level):
        module = '"%s"' % str(module)
        level = '"%s"' % str(level)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtLevelSet", module, level)

    @staticmethod
    def LogExtStart(brdName, brdidx, coreid, start):
        start = '"%s"' % str(start)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtStartSet", start)

    @staticmethod
    def LogExtSave2File(brdName, brdidx, coreid, file):
        wrapfile = '"%s"' % str(file)
        wrapfile = wrapfile.replace("\\", "/")
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "LogExtSave2File", wrapfile)

    @staticmethod
    def ChangeMap(brdName, brdidx, coreid, map='', out=''):
        map = map and '"{0}"'.format(map).replace("\\", "/") or '""'
        out = out and '"{0}"'.format(out).replace("\\", "/") or '""'
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "ChangeMap", map, out)

    @staticmethod
    def ParseLogFile(brdName, brdidx, coreid, logfile):
        logfile = '"%s"' % str(logfile)
        logfile = logfile.replace("\\", "/")
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "ParseLogFile", logfile)

    @staticmethod
    def PcAddrParse(brdName, brdidx, coreid, addr):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "PcAddrParse", str(addr))

    @staticmethod
    def DciInit(brdName, brdidx, coreid):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "DciInit")

    @staticmethod
    def DciTaskAdd(brdName, brdidx, coreid, triggermode, triggertype, bustype, addr, datatypeOrLen):
        triggermode = '"%s"' % str(triggermode)
        triggertype = '"%s"' % str(triggertype)
        bustype = '"%s"' % str(bustype)
        addr = '"%s"' % str(addr)
        datatypeOrLen = '"%s"' % str(datatypeOrLen)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "DciTaskAdd", triggermode, triggertype, bustype, addr,
                                       datatypeOrLen)

    @staticmethod
    def DciTaskTriggerInfo(brdName, brdidx, coreid):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "DciTaskTriggerInfo")

    @staticmethod
    def DciTaskDel(brdName, brdidx, coreid, taskIndex):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "DciTaskDel", str(taskIndex))

    @staticmethod
    def DciClose(brdName, brdidx, coreid):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "DciClose")

    def MonitorSetMapStatusCheck(self, flag):
        self.checkflag = flag

    def _GetFunCallChain(self, out_path, asm_path, fun_name, filter_rule, agent):
        cmd = 'ClientGetFuncCallChain -outfilepath %s -asmfilepath %s -interruptfunname %s -filterrule %s' \
              % (out_path, asm_path, fun_name, filter_rule)
        return agent.run(cmd)

    def GetFunListCallChain(self, out_path, asm_path, fun_name_list, filter_rule='default'):
        """
        以指定正则匹配作为函数名过滤出在fun_name_list中的调用链
        :param out_path: 核文件全路径
        :param asm_path: 汇编文件全路径
        :param fun_name_list: 检测函数列表
        :param filter_rule: 过滤正则表达式，默认为^(OBM|ceva_generic).+
        :return: FuncCallChainResult对象
        """
        if not self.client:
            self.client = DspMonitorClient(self.dspmonitorip)
        agent = OPAgent(self.client)
        res_str = ''
        for fun_name in fun_name_list:
            res_str += self._GetFunCallChain(out_path, asm_path, fun_name, filter_rule, agent)
        res = FuncCallChainResult(res_str)
        res.parse_chain()
        return res

    def GetFunStackSize(self, out_path, asm_path, fun_name):
        """
        获取指定函数的栈开销情况
        :param out_path: 核文件全路径
        :param asm_path: 汇编文件全路径
        :param fun_name: 检测函数
        :return: FuncStackSizeResult对象
        """
        if not self.client:
            self.client = DspMonitorClient(self.dspmonitorip)
        agent = OPAgent(self.client)
        cmd = 'ClientGetFuncStackSize -outfilepath %s -asmfilepath %s -funname %s' \
              % (out_path, asm_path, fun_name)
        res_str = agent.run(cmd)
        res = FuncStackSizeResult(res_str)
        res.parse_stack()
        return res

    @staticmethod
    def PmuLine(brdName, brdidx, coreid, config):
        corename = brdName + "_" + str(brdidx) + "_" + str(coreid)
        if corename not in DspMonitor.pmuQuery.keys():
            DspMonitor.pmuQuery[corename] = MonitorCore(brdName, brdidx, coreid)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "PmuLine", config)

    @staticmethod
    def PmuFunNum(brdName, brdidx, coreid, config):
        corename = brdName + "_" + str(brdidx) + "_" + str(coreid)
        if corename not in DspMonitor.pmuQuery.keys():
            DspMonitor.pmuQuery[corename] = MonitorCore(brdName, brdidx, coreid)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "PmuFunNum", config)

    @staticmethod
    def PmuFunTime(brdName, brdidx, coreid, funname, time):
        fun = '"%s"' % str(funname)
        corename = brdName + "_" + str(brdidx) + "_" + str(coreid)
        if corename not in DspMonitor.pmuQuery.keys():
            DspMonitor.pmuQuery[corename] = MonitorCore(brdName, brdidx, coreid)
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "PmuFunTime", fun, str(time))

    @staticmethod
    def PmuQuery2(brdName, brdidx, coreid):
        return DspMonitor.ContractCall(brdName, brdidx, coreid, "PmuQuery")

    @staticmethod
    def PmuQuery():
        running = True
        queryResult = {}
        for value in DspMonitor.pmuQuery.values():
            queryResult[value.coreName] = []
        while running:
            running = False
            time.sleep(5)
            logging.info("wait 5s")
            for value in DspMonitor.pmuQuery.values():
                #   当前核不存在任务列表时，不查询
                if len(queryResult[value.coreName]) > 0 and queryResult[value.coreName][-1].NoneTask:
                    continue
                #  当前核任务都完成了，未完成任务数为0时，不在查询
                if len(queryResult[value.coreName]) > 0 and queryResult[value.coreName][-1].Uncompleted == 0:
                    continue
                result = DspMonitor.PmuQuery2(value.brdName, value.brdidx, value.coreid)
                if result.Status:  # 得到数据后，保存
                    queryResult[value.coreName].append(result)
                #  当前核仍存在已完成的任务时，继续查询
                while result.Completed and result.Completed > 0:
                    result = DspMonitor.PmuQuery2(value.brdName, value.brdidx, value.coreid)
                    if result.Status:
                        queryResult[value.coreName].append(result)
                if result.Uncompleted > 0:
                    running = True  # 不是所有核都完成时，继续循环
        # 清空上一次的任务列表
        DspMonitor.pmuQuery.clear()
        return queryResult

    @staticmethod
    def init_dspmonitor(webmnt, vbpObject, productVersion="", dspConfig={}):
        DspInitTool = HfDspInit if 'HF' in productVersion else NrDspInit
        DspInitTool(webmnt, vbpObject, dspConfig).create_boards()
        # if self._get_aau_linked_dsp_alias(self._get_linked_aau_alias()) is not None:
        #     if 'HF' in productVersion:
        #         HfAauDspInit(dspAlias, DeviceRepository().find(self._get_linked_aau_alias())).create_boards()
        #     else:
        #         NrAauDspInit(dspAlias, DeviceRepository().find(self._get_linked_aau_alias())).create_boards()


class MonitorCore:
    def __init__(self, brdName, brdidx, coreid):
        self.brdName = brdName
        self.brdidx = brdidx
        self.coreid = coreid
        self.coreName = brdName + "_" + str(brdidx) + "_" + str(coreid)


if __name__ == '__main__':
    pass
