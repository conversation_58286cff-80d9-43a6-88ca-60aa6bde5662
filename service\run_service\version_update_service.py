from infrastructure.logger.logger import logger
from service.logging_service import log_execution, LoggingService
from service.run_service.run_service import Service
import sys
from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
from domain.repository.cron_task import EsCronTask
from domain.model.dto.corn_task import *
from domain.model.dto.logdto import LogJobInfo
import asyncio
from domain.repository.es_env import EsPcEnv, EsVersionEnv
from service.tools.ActionService import ActionService
import time
from service.check_env_service import EnvMonitorService


class VersionUpdateService(Service):
    def __init__(self, service_type, subtype, state, needed, task_id, env_id, es, cron_task_id):
        super().__init__(service_type, subtype, state, needed, es)
        self.task_id = task_id
        self.env_id = env_id
        self._es_version_env = EsVersionEnv()
        self.job_name = needed.get("job_name", "")
        self.build_number = needed.get("build_number", "")
        self.cron_task_id = cron_task_id


    def execute(self):
        version = self.needed.get('tar_path', '')
        upgrade_res = self.get_verison_upgrade_state()
        # 回退失败：邮件通知
        # 回退成功：翻转状态存入本地复测消息队列
        print(upgrade_res)
        if not upgrade_res.result:
            # 回退失败了邮件通知
            self._send_email(
                subject=f"{self.env_id}发送版本升级失败",
                content=f"发送版本升级失败\n最后测试版本: {version}"
            )
            return
        else:
            if upgrade_res.data.status == "成功":
                time.sleep(60)
                if self._handle_env_check():
                    # 更新数据
                    if self.subtype == "online_test":
                        trace_info = f"{self.env_id}版本升级已经完成，版本{version}"
                    elif self.subtype == "update":
                        trace_info = f"{self.env_id}升级到当天版本流程结束"
                    else:
                        trace_info = f"{self.env_id}不存在的子类型"
                    self._update_task_status(self.task_id, self.cron_task_id, "completed", self.env_id, trace_info)
                    if self.subtype == "online_test":
                        logger.info("进入升级后的线上复测流程。。。。。")
                        if self._handle_env_check():
                            self._handle_online_test()
                        else:
                            return
                    elif self.subtype == "update":
                        self._send_email(
                            subject=f"{self.env_id}版本升级成功",
                            content=f"升级到当天版本流程结束，subtype{self.subtype}"
                        )
                        return
                    else:
                        self._send_email(
                            subject=f"{self.env_id}版本升级失败",
                            content=f"版本升级定时任务失败\n未知的子类型: {self.subtype}"
                        )
                        return
                else:
                    return
            elif upgrade_res.data.status.value in ["失败", "异常"]:
                trace_info = f"{self.env_id}版本升级失败\n最后测试版本: {version},message:{upgrade_res.data.output.get('message')}"
                self._update_task_status(self.task_id, self.cron_task_id, upgrade_res.data.output.get('message'), self.env_id, trace_info)
                return
            else:
                logger.info("version_upgrade is still building, will check again later")
                return

    def get_verison_upgrade_state(self):
        pipelineUuid = self.needed.get("pipelineUuid", "")
        pipelineStageId = self.needed.get("pipelineStageId", "")
        recordId = self.needed.get("recordId", "")
        # 发送消息
        body = {
            "pipelineUuid": pipelineUuid,
            "pipelineStageId": pipelineStageId,
            "recordId": recordId
        }
        result = ActionService().get_execution_info(body)
        return result

    def _handle_env_check(self):
        ems = EnvMonitorService({"env_id": self.env_id})
        device_info_list = ems.get_env_device_info()
        for device_info in device_info_list:
            for tool in ["query_ue_com", "query_ue_ipconfig", "query_pc_net", "query_ume_alarm", "query_me_connect"]:
                try:
                    result = ems.execute_tool(device_info, tool)
                    if not result:
                        trace_info = f"环境ID: {ems.document.get('env_id')}\n工具名称: {tool}\n执行结果: 失败"
                        self.save_log("env_check", "failure", trace_info)
                        return False
                except:
                    trace_info = f"环境ID: {ems.document.get('env_id')}\n工具名称: {tool}\n执行结果: 失败"
                    self.save_log("env_check", "failure", trace_info)
                    return False
        trace_info = f"环境ID: {ems.document.get('env_id')}\n环境检查成功"
        self.save_log("env_check","success",trace_info)
        return True

    def save_log(self,service_key, status, trace_info):
        LoggingService.log_execution(
            service_type="log_save",
            operation="log_save",
            status=status,
            task_id=self.task_id,
            env_id=self.env_id,
            details={"trace_info": trace_info},
            service_key=service_key
        )

    def _handle_online_test(self):
        msg = LogJobInfo(
            log_name=self.needed.get("log_name", ""),
            job_name=self.job_name,
            build_number=self.build_number,
            env_id=self.env_id,
            task_id=self.task_id,
            service_type="online_test",
            subtype="update"
        )
        asyncio.run(CiAgentsQueue().push_info(msg))
        logger.info(f"版本升级定时任务：放入线上复测队列, task_id :{self.task_id}")


