# -*-coding:utf-8-*-
import sys, os
sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))
from service.tools.EnvConfigService import EnvConfigService
from domain.model.tools.Action import ActionExecutor, Action


class ActionService(object):
    @staticmethod
    def execute(env_id, action_params, group_no=""):
        env_config = EnvConfigService.get_env_config(env_id, group_no)
        action_executor = ActionExecutor(env_config.env_config, **action_params)
        return Action.execute(action_executor)

    @staticmethod
    def get_execution_info(params):
        return Action.get_execution_info(params)

    @staticmethod
    def get_execution_logs(params):
        return Action.get_execution_logs(params)


if __name__ == "__main__":
    execute_result = ActionService.execute("RAN3-上海高频CI团队-VAT1014", {"actionId": "d7dd77b794b947a0e1457886d7db82a8", "actionType": "basic"})
    print(execute_result.execute_result)
    import time
    time.sleep(10)
    exe_info = ActionService.get_execution_info(execute_result.execute_result)
    print(exe_info.execute_info)
    exe_log = ActionService.get_execution_logs({**execute_result.execute_result, **{"type": "nested"}})
    print(exe_log.execute_logs)

