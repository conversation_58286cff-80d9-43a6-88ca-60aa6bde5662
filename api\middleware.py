from infrastructure.logger.logger import logger
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
import time


def init_middlewire(app):
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 允许所有来源，也可以指定具体的域名列表
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(AccessMiddleware)


class AccessMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        duration = (time.time() - start_time) * 1000
        logger.info(
            f'{request.client.host} - "{request.method} {request.url.path}" '
            f'Status: {response.status_code} - {duration:.2f}ms - Agent: {request.headers.get("user-agent")}'
        )

        return response
