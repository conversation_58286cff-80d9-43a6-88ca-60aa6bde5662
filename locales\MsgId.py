# -*- coding:utf-8 -*-
# 为便于在翻译文件中查找，最好行号能和翻译文件中行号相匹配
# code码的message
SUCCESS = "SUCCESS"
SYSTEM_FAILED = "SystemFailed"
AUTH_FAILED = "AuthFailed"
BUSINESS_FAILED = "BusinessFailed"
VALIDATED_FAILED = "ValidatedFailed"
SERVER_FAILED = "ServerFailed"
INSUFFICIENT_PERMISSIONS = "InsufficientPermissions"
# FrameworkException类中的信息
ERR_UNKNOWN = "An unknown exception occurred."
ERR_PARA_UNACCEPT = "Unacceptable parameters."
# TDL
TDL_NOT_CONN = "Tdl can not be connect."
TDL_ERR_BASE = "Tdl Error!"
TDL_ERR_OCR = "Tdl exception occurred"
TDL_ERR_CODE = "%s error msg code is greater than or equal to 300, or less than 200" % TDL_ERR_BASE
# MONGO数据库相关
MONGO_ERR_BASE = "MONGO DB Error"
# ZOOKEEPER数据库相关
ZOOKEEPER_ERR_BASE = "ZOOKEEPER Error"
ZOOKEEPER_CON_TIME_OUT = "%s: Connection to ZooKeeper is time-out" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_AUTH_FAILED = "ZooKeeper Auth Failed"
ZOOKEEPER_OCC_ERR_OCC = "%s: can not occupy node because of being occupied by others" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_OCC_ERR_REENTER = "%s: can not occupy node because of being reentered by others" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_REENTER_ERR_OCC = "%s: can not reenter node because of being occupied by others" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_NODE_NOT_EXIST = "%s: this node does not exist" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_NODE_NOT_EMPTY_ERR = "%s: this node is not empty" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_NODE_VALUE_NOT_EMPTY_ERR = "%s: this node value is not empty" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_NODE_VALUE_BAD_VERSION_ERR = "%s: version of node being updated is not correct" % ZOOKEEPER_ERR_BASE
ZOOKEEPER_BRANCH_NODE_CREATE_FAILED = "%s: create branch node in zk failed" % ZOOKEEPER_ERR_BASE
# redis相关
REDIS_BASE_ERR = "redis error!"
REDIS_NOT_CONN = "redis can not be connected."
REDIS_GET_IP_FROM_CONFIG = "get redis ip from config"
# 其它
OTHER_INPUT_PARA_NO_VAR = "Variable is not found in the input params"
