import inspect
import traceback
import logging


class Reflection:
    ROBOT_LIBRARY_SCOPE = 'GLOBAL'

    @staticmethod
    def create_obj(packetName, className, *args):
        try:
            module = __import__(packetName, globals(), locals(), [className])
            print(module)
            obj = getattr(module, className)
            return obj(*args)
        except Exception as e:
            logging.warning(traceback.print_exc())
            raise (Exception("create %s exception:%s" % (className, e)))

    @staticmethod
    def invoke(obj, methodName, *args):
        method = getattr(obj, methodName)
        return method(*args)

    @staticmethod
    def constraint_invoke(obj, methodName, *args, **kwargs):
        method = getattr(obj, methodName)
        params = inspect.signature(method).parameters.keys()
        filterKwargs = {k: v for k, v in kwargs.items() if k in params}
        return method(*args, **filterKwargs)

