#!/usr/bin/env python3
"""
Jenkins任务聚合功能使用示例

本文件展示了如何使用新的Jenkins任务聚合功能，包括：
- 基本时间范围聚合
- 按环境维护负责人聚合
- 按任务ID聚合
- 多维度组合查询
"""

import requests
import json
from datetime import datetime, timedelta


class JenkinsJobAggregationExamples:
    """Jenkins任务聚合功能示例"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_endpoint = f"{base_url}/jenkins/jobs/aggregate"
    
    def basic_time_range_aggregation(self):
        """基本时间范围聚合示例"""
        print("=== 基本时间范围聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"总任务数: {data['total_jobs']}")
            print(f"总人工处理时间: {data['total_manual_hours']}小时")
            print(f"唯一任务数: {data['unique_task_count']}")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def env_filter_aggregation(self):
        """按环境过滤聚合示例"""
        print("=== 按环境过滤聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "env_id": "RAN3-T17-10466"
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"环境 {params['env_id']} 的统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def principal_filter_aggregation(self):
        """按负责人过滤聚合示例"""
        print("=== 按负责人过滤聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "principal": "张三"
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"负责人 {params['principal']} 的统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def task_id_filter_aggregation(self):
        """按任务ID过滤聚合示例"""
        print("=== 按任务ID过滤聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "task_id": "task123456"
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"任务 {params['task_id']} 的统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def env_group_aggregation(self):
        """按环境分组聚合示例"""
        print("=== 按环境分组聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "group_by_env": True
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"总体统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
            
            if data.get("env_groups"):
                print("\n各环境详细统计:")
                for env_group in data["env_groups"]:
                    print(f"  环境 {env_group['env_id']}:")
                    print(f"    任务数: {env_group['total_jobs']}")
                    print(f"    人工处理时间: {env_group['total_manual_hours']}小时")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def principal_group_aggregation(self):
        """按负责人分组聚合示例"""
        print("=== 按负责人分组聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "group_by_principal": True,
            "group_by_env": True  # 同时按环境分组以获取负责人信息
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"总体统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
            
            if data.get("principal_groups"):
                print("\n各负责人详细统计:")
                for principal_group in data["principal_groups"]:
                    print(f"  负责人 {principal_group['principal']}:")
                    print(f"    任务数: {principal_group['total_jobs']}")
                    print(f"    人工处理时间: {principal_group['total_manual_hours']}小时")
                    print(f"    负责环境: {', '.join(principal_group['env_ids'])}")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def task_id_group_aggregation(self):
        """按任务ID分组聚合示例"""
        print("=== 按任务ID分组聚合 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "group_by_task_id": True
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"总体统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
            
            if data.get("task_id_groups"):
                print("\n各任务详细统计:")
                for task_group in data["task_id_groups"][:5]:  # 只显示前5个
                    print(f"  任务 {task_group['task_id']}:")
                    print(f"    任务数: {task_group['total_jobs']}")
                    print(f"    人工处理时间: {task_group['total_manual_hours']}小时")
                    print(f"    环境: {task_group['env_id']}")
                    print(f"    负责人: {task_group['principal']}")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def combined_query_example(self):
        """组合查询示例"""
        print("=== 组合查询示例 ===")
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "principal": "张三",
            "group_by_env": True
        }
        
        response = requests.get(self.api_endpoint, params=params)
        result = response.json()
        
        if result.get("success"):
            data = result["data"]
            print(f"负责人 {params['principal']} 的环境统计:")
            print(f"  总任务数: {data['total_jobs']}")
            print(f"  总人工处理时间: {data['total_manual_hours']}小时")
            
            if data.get("env_groups"):
                print("\n各环境详细统计:")
                for env_group in data["env_groups"]:
                    print(f"  环境 {env_group['env_id']}:")
                    print(f"    任务数: {env_group['total_jobs']}")
                    print(f"    人工处理时间: {env_group['total_manual_hours']}小时")
        else:
            print(f"查询失败: {result.get('msg')}")
        
        print()
    
    def run_all_examples(self):
        """运行所有示例"""
        print("Jenkins任务聚合功能示例演示")
        print("=" * 50)
        
        try:
            self.basic_time_range_aggregation()
            self.env_filter_aggregation()
            self.principal_filter_aggregation()
            self.task_id_filter_aggregation()
            self.env_group_aggregation()
            self.principal_group_aggregation()
            self.task_id_group_aggregation()
            self.combined_query_example()
            
            print("所有示例执行完成！")
            
        except requests.exceptions.ConnectionError:
            print("错误: 无法连接到API服务器，请确保服务正在运行")
        except Exception as e:
            print(f"执行示例时发生错误: {str(e)}")


if __name__ == "__main__":
    examples = JenkinsJobAggregationExamples()
    examples.run_all_examples()
