from infrastructure.db.mysql.Env import Env
from infrastructure.db.mysql.MySql import MySql
from infrastructure.Singleton import Singleton


@Singleton
class MysqlService(object):

    def __init__(self, db={}):
        if not db:
            db = self._get_db_info()
        self._db = MySql(db)

    def _get_db_info(self):
        return Env().config

    def query(self, cond_dict, table_name, fields=[]):
        consql = ""
        if cond_dict != '':
            for k, v in cond_dict.items():
                consql = consql + k + ' = "' + v + '" and'
            consql = consql + ' 1=1 '
        return self._db.fetch(table_name, fields, consql)
