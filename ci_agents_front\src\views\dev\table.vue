<script setup lang="ts">
import { onMounted, ref, watch, computed } from "vue";
import * as echarts from "echarts";
import jobApi from "@/api/jobApi";
import { QuestionFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

// 添加数据加载状态
const loading = ref(false);
// 定义failureData为响应式变量
const failureData = ref(null);
// 添加在 script setup 部分的其他响应式变量附近
const selectedTimeRange = ref("today");
const customStartDate = ref("");
const customEndDate = ref("");
const detailTableLoading = ref(false);
const showDetailDialog = ref(false);
const detailDialogTitle = ref("");
const detailTableData = ref([]);
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);
const detailTotal = ref(0);

// Jenkins任务聚合报表相关变量
const showAggregationDialog = ref(false);
const aggregationLoading = ref(false);
const aggregationData = ref(null);
const aggregationParams = ref({
  start_date: "",
  end_date: "",
  env_id: "",
  principal: "",
  task_id: "",
  group_by_env: false,
  group_by_principal: false,
  group_by_task_id: false
});

// 初始化聚合查询的日期范围（默认为最近7天）
const initAggregationDates = () => {
  const today = new Date();
  const weekAgo = new Date();
  weekAgo.setDate(today.getDate() - 7);

  aggregationParams.value.end_date = today.toISOString().split("T")[0];
  aggregationParams.value.start_date = weekAgo.toISOString().split("T")[0];
};

// 添加表格高度计算
const tableHeight = ref(41.38 * 10 + 40); // 设置一个固定高度，根据需要调整

// 计算当前页显示的详细数据
const paginatedDetailData = computed(() => {
  const startIndex = (detailCurrentPage.value - 1) * detailPageSize.value;
  const endIndex = startIndex + detailPageSize.value;
  return detailTableData.value.slice(startIndex, endIndex);
});

// 处理详情页码改变
const handleDetailCurrentChange = val => {
  detailCurrentPage.value = val;
  // 不需要在这里调整表格高度，因为我们使用了固定高度
};

// 获取特定业务流程类型的详细数据
const fetchDetailDataByProcessType = async processType => {
  try {
    detailTableLoading.value = true;

    // 确保获取到正确的时间范围
    let startDate, endDate;

    if (
      selectedTimeRange.value === "custom" &&
      customStartDate.value &&
      customEndDate.value
    ) {
      // 使用自定义日期范围，但将startDate设置为customStartDate的后一天
      const nextStartDay = new Date(customStartDate.value);
      nextStartDay.setDate(nextStartDay.getDate() + 1);
      startDate = nextStartDay.toISOString().split("T")[0]; // 格式化为 YYYY-MM-DD
      // 格式化endDate
      const nextEndDay = new Date(customEndDate.value);
      nextEndDay.setDate(nextEndDay.getDate() + 1);
      endDate = nextEndDay.toISOString().split("T")[0]; // 格式化为 YYYY-MM-DD
    } else {
      // 使用预设时间范围
      const today = new Date();
      endDate = today.toISOString().split("T")[0]; // 格式化为 YYYY-MM-DD

      if (selectedTimeRange.value === "week") {
        const weekAgo = new Date();
        weekAgo.setDate(today.getDate() - 6);
        startDate = weekAgo.toISOString().split("T")[0];
      } else if (selectedTimeRange.value === "twoWeeks") {
        const twoWeeksAgo = new Date();
        twoWeeksAgo.setDate(today.getDate() - 13);
        startDate = twoWeeksAgo.toISOString().split("T")[0];
      } else {
        // 默认为今天
        startDate = endDate;
      }
    }

    // console.log("饼图下钻", {
    //   processType,
    //   startDate,
    //   endDate,
    //   selectedTimeRange: selectedTimeRange.value,
    //   customStartDate: customStartDate.value,
    //   customEndDate: customEndDate.value
    // });

    // 调用详细信息API，指定特定的业务流程类型
    // 对于饼图点击，我们不指定特定日期，而是查询整个时间范围内的该流程类型数据
    const result = await jobApi.getBusinessProcessDetail(
      undefined, // 不指定特定日期，查询时间范围内所有数据
      processType, // 指定业务流程类型
      startDate,
      endDate
    );

    if (result.success && result.data) {
      // 由于指定了processType，返回的数据应该只包含该类型
      // 但为了保险起见，我们再次过滤
      const filteredData = result.data.filter(
        item => item.process_type === processType
      );
      return filteredData;
    } else {
      console.error("获取特定业务流程详细数据失败:", result.message);
      return [];
    }
  } catch (error) {
    console.error("获取特定业务流程详细数据出错:", error);
    return [];
  } finally {
    detailTableLoading.value = false;
  }
};

// 处理饼图点击事件
const handlePieChartClick = params => {
  const processType = params.name; // 业务流程类型
  const value = params.value; // 数值

  if (value === 0) {
    return; // 如果数值为0，不显示详情
  }

  detailDialogTitle.value = `${processType} - 业务流程详细信息`;
  showDetailDialog.value = true;

  // 获取该业务流程类型的详细数据
  fetchDetailDataByProcessType(processType).then(detailData => {
    detailTableData.value = detailData;
    detailTotal.value = detailData.length;
    detailCurrentPage.value = 1; // 重置为第一页
  });
};

onMounted(async () => {
  loading.value = true;
  try {
    await fetchAndRenderCharts();
    initAggregationDates(); // 初始化聚合查询日期
  } finally {
    loading.value = false;
  }
});

// 获取数据并渲染所有图表
const fetchAndRenderCharts = async () => {
  // 获取失败统计数据
  failureData.value = await fetchFailureData();

  // 使用获取的数据初始化图表
  initFirstSuccessRateByDay(failureData.value);
  initFirstSuccessRateByEnv(failureData.value);
  initCiAgentStepChart(failureData.value);
};

// 获取失败统计数据
const fetchFailureData = async () => {
  try {
    const result = await jobApi.getFailureStatsByDay();
    // console.log("failureData:", result.data)
    if (result.success) {
      return result.data;
    } else {
      console.error("获取失败统计数据失败:", result.message);
      return null;
    }
  } catch (error) {
    console.error("获取失败统计数据出错:", error);
    return null;
  }
};

// 折线图 - 修改为接收数据参数
const initFirstSuccessRateByDay = failureData => {
  const chartDom = document.getElementById("firstSuccessRateByDay");
  if (!chartDom) return;

  // 检查是否已有实例并销毁
  const existingChart = echarts.getInstanceByDom(chartDom);
  if (existingChart) {
    existingChart.dispose();
  }
  const myChart = echarts.init(chartDom);

  // 默认空数据
  let days = [];
  let successRatesByDay = [];

  // 如果有数据，则处理数据
  if (failureData && failureData.by_day) {
    // 获取所有日期并按时间顺序排序，只保留最近一个月的数据
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    days = Object.keys(failureData.by_day)
      .filter(dateStr => {
        const date = new Date(dateStr);
        return date >= oneMonthAgo;
      })
      .sort();

    // 计算每天的首次成功率: (1 - (first_failures / environments)) * 100
    successRatesByDay = days.map(date => {
      // console.log(failureData.by_day[date])
      const dayData = failureData.by_day[date].total;
      // 避免除以零的情况
      if (dayData.environments === 0) return 0;

      const successRate =
        (1 - dayData.first_failures / dayData.first_executions) * 100;
      // 保留两位小数
      return parseFloat(successRate.toFixed(2));
    });
  }

  const option = {
    tooltip: {
      trigger: "axis",
      formatter: "{b}<br/>{a}: {c}%"
    },
    // legend: {
    //   data: ['首次成功率']
    // },
    grid: {
      top: "10%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: days
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 100,
      axisLabel: {
        formatter: "{value}%"
      }
    },
    series: [
      {
        name: "首次成功率",
        type: "line",
        stack: "总量",
        data: successRatesByDay,
        markLine: {
          data: [{ type: "average", name: "平均值" }],
          // 添加这行来移除箭头
          symbol: ["none", "none"],
          label: { show: false }
        }
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
};

// 柱状图
const initFirstSuccessRateByEnv = failureData => {
  const chartDom = document.getElementById("firstSuccessRateByEnv");
  if (!chartDom) return;

  // 检查是否已有实例并销毁
  const existingChart = echarts.getInstanceByDom(chartDom);
  if (existingChart) {
    existingChart.dispose();
  }
  const myChart = echarts.init(chartDom);

  // 默认空数据
  let envs = [];
  let envIds = [];
  let successRatesByEnv = [];

  // 如果有数据，则处理数据
  if (failureData && failureData.by_env) {
    // 提取环境名称中的 VAT 编号并排序
    envs = Object.keys(failureData.by_env)
      // .map(env => env.match(/VAT\d+/)?.[0]) // 提取 VAT 编号
      // .filter(Boolean) // 过滤掉可能的 null 值
      .filter(env => /^[^-]+-[^-]+-VAT\d+$/.test(env)) // 只保留符合 "xxx-xxx-VAT数字" 格式的环境
      .filter(
        env =>
          env !== "RAN3-上海高频CI团队-VAT1014" &&
          env !== "RAN3-上海高频CI团队-VAT1016"
      ) // 过滤掉特定环境
      .sort((a, b) => {
        const numA = parseInt(a.match(/VAT(\d+)/)?.[1] || "0");
        const numB = parseInt(b.match(/VAT(\d+)/)?.[1] || "0");
        return numA - numB;
      });

    // console.log(envs)

    // 提取 VAT 编号到 envIds
    envIds = envs.map(env => env.match(/VAT\d+/)?.[0] || "");

    // 计算每天的首次成功率: (1 - (first_failures / environments)) * 100
    successRatesByEnv = envs.map(env => {
      // console.log(failureData.by_env[env])
      const envData = failureData.by_env[env];
      // 避免除以零的情况
      if (envData.days === 0) return 0;

      const successRate =
        (1 - envData.first_failures / envData.first_executions) * 100;
      // 保留两位小数
      return parseFloat(successRate.toFixed(2));
    });
  }

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{b}<br/>{a}: {c}%"
    },
    // legend: {
    //   data: ['首次成功率']
    // },
    grid: {
      top: "10%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
      data: envIds,
      axisLabel: {
        interval: 0, // 强制显示所有标签
        rotate: 45 // 标签旋转45度，防止重叠
      }
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 100,
      axisLabel: {
        formatter: "{value}%"
      }
    },
    series: [
      {
        name: "首次成功率",
        type: "bar",
        barWidth: "15px",
        data: successRatesByEnv,
        markLine: {
          data: [{ type: "average", name: "平均值" }],
          symbol: ["none", "none"], // 添加这行来移除箭头
          label: { show: false }
        }
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
};

// 饼图
const initCiAgentStepChart = failureData => {
  const chartDom = document.getElementById("ciAgentStep");
  if (!chartDom) return;

  // 检查是否已有实例并销毁
  const existingChart = echarts.getInstanceByDom(chartDom);
  if (existingChart) {
    existingChart.dispose();
  }
  const myChart = echarts.init(chartDom);

  // 默认空数据
  let pieData = [];

  // 如果有数据，则处理数据
  if (failureData && failureData.total) {
    // 根据选择的时间范围获取数据
    let dataToUse;

    if (
      selectedTimeRange.value === "custom" &&
      customStartDate.value &&
      customEndDate.value
    ) {
      // 自定义日期范围
      dataToUse = getAggregatedDataForDateRange(
        failureData,
        new Date(customStartDate.value),
        new Date(customEndDate.value)
      );
    } else {
      // 预设时间范围
      dataToUse = getAggregatedDataForTimeRange(
        failureData,
        selectedTimeRange.value
      );
    }

    // 从数据中提取各个步骤的停止计数
    pieData = [
      {
        value: dataToUse.stop_in_jenkins_job_save_count || 0,
        name: "日志保存"
      },
      { value: dataToUse.stop_in_log_analysis_count || 0, name: "智能分析" },
      { value: dataToUse.stop_in_env_check_count || 0, name: "环境检测" },
      { value: dataToUse.stop_in_online_test_count || 0, name: "复测" },
      { value: dataToUse.stop_in_env_backtrack_count || 0, name: "回溯" }
    ].filter(item => item.value > 0); // 过滤掉值为0的项
  }

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)"
    },
    legend: {
      orient: "horizontal",
      bottom: 20
    },
    series: [
      {
        name: "失败分布",
        type: "pie",
        radius: "50%",
        center: ["50%", "40%"], // 将垂直位置从50%改为40%，使饼图向上移动
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ]
  };

  myChart.setOption(option);

  // 添加点击事件监听
  myChart.on("click", handlePieChartClick);

  window.addEventListener("resize", () => myChart.resize());
};

// 通用的时间范围数据计算函数
const calculateProportionByTimeRange = (
  timeRange,
  dataExtractor,
  startDateStr = null,
  endDateStr = null
) => {
  if (!failureData.value || !failureData.value.by_day) return "0/0";

  let startDate = new Date();
  let endDate = new Date();

  // 如果提供了自定义日期范围，则使用它们
  if (startDateStr && endDateStr) {
    startDate = new Date(startDateStr);
    endDate = new Date(endDateStr);
  } else {
    // 否则根据预设时间范围设置起始日期
    switch (timeRange) {
      case "week":
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "twoWeeks":
        startDate.setDate(startDate.getDate() - 14);
        break;
      case "month":
        startDate.setDate(startDate.getDate() - 30);
        break;
      case "custom":
        // 自定义时间范围在上面已处理
        break;
      case "today":
      default:
        // 默认只显示当天
        break;
    }
  }

  const endDateISOStr = endDate.toISOString().split("T")[0];

  // 如果只是当天，直接返回当天数据
  if (timeRange === "today" && !startDateStr && !endDateStr) {
    if (
      failureData.value.by_day[endDateISOStr] &&
      failureData.value.by_day[endDateISOStr].total
    ) {
      return dataExtractor(failureData.value.by_day[endDateISOStr].total);
    }
    return "0/0";
  }

  // 计算时间范围内的数据
  let numerator = 0;
  let denominator = 0;

  // 遍历日期范围内的所有数据
  Object.keys(failureData.value.by_day).forEach(dateStr => {
    const date = new Date(dateStr);
    // 检查日期是否在指定范围内
    if (date >= startDate && date <= endDate) {
      const dayData = failureData.value.by_day[dateStr].total;
      if (dayData) {
        const [dayNumerator, dayDenominator] = dataExtractor(dayData, true);
        numerator += dayNumerator;
        denominator += dayDenominator;
      }
    }
  });

  // 避免除以零
  if (denominator === 0) return "0/0";

  return `${numerator}/${denominator}`;
};

// 获取首次通过比例
const getPassProportion = (
  timeRange = "today",
  startDate = null,
  endDate = null
) => {
  return calculateProportionByTimeRange(
    timeRange,
    (data, isRange = false) => {
      if (isRange) {
        return [
          data.first_executions - data.first_failures,
          data.first_executions
        ];
      }
      return `${data.first_executions - data.first_failures}/${data.first_executions}`;
    },
    startDate,
    endDate
  );
};

// 获取复测比例
const getOnlineTestProportion = (
  timeRange = "today",
  startDate = null,
  endDate = null
) => {
  return calculateProportionByTimeRange(
    timeRange,
    (data, isRange = false) => {
      if (isRange) {
        return [data.stop_in_online_test_count || 0, data.first_failures || 0];
      }
      return `${data.stop_in_online_test_count || 0}/${data.first_failures || 0}`;
    },
    startDate,
    endDate
  );
};

// 获取回溯比例
const getRollbackProportion = (
  timeRange = "today",
  startDate = null,
  endDate = null
) => {
  return calculateProportionByTimeRange(
    timeRange,
    (data, isRange = false) => {
      if (isRange) {
        return [
          data.stop_in_env_backtrack_count || 0,
          data.first_failures || 0
        ];
      }
      return `${data.stop_in_env_backtrack_count || 0}/${data.first_failures || 0}`;
    },
    startDate,
    endDate
  );
};

// 获取环境检测比例
const getEnvCheckProportion = (
  timeRange = "today",
  startDate = null,
  endDate = null
) => {
  return calculateProportionByTimeRange(
    timeRange,
    (data, isRange = false) => {
      if (isRange) {
        return [data.stop_in_env_check_count || 0, data.first_failures || 0];
      }
      return `${data.stop_in_env_check_count || 0}/${data.first_failures || 0}`;
    },
    startDate,
    endDate
  );
};

// 获取日志分析比例
const getLogAnalysisProportion = (
  timeRange = "today",
  startDate = null,
  endDate = null
) => {
  return calculateProportionByTimeRange(
    timeRange,
    (data, isRange = false) => {
      if (isRange) {
        return [data.stop_in_log_analysis_count || 0, data.first_failures || 0];
      }
      return `${data.stop_in_log_analysis_count || 0}/${data.first_failures || 0}`;
    },
    startDate,
    endDate
  );
};

// 计算比例的百分比
const calculatePercentage = proportionString => {
  const parts = proportionString.split("/");
  if (
    parts.length === 2 &&
    !isNaN(parts[0]) &&
    !isNaN(parts[1]) &&
    parseInt(parts[1]) !== 0
  ) {
    return ((parseInt(parts[0]) / parseInt(parts[1])) * 100).toFixed(2) + "%";
  }
  return "0%";
};

// 添加获取指定时间范围内聚合数据的函数
const getAggregatedDataForTimeRange = (failureData, timeRange) => {
  if (timeRange === "today") {
    // 获取今天的日期字符串，格式为 YYYY-MM-DD
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    // 检查是否有当天的数据
    if (
      failureData.by_day &&
      failureData.by_day[todayStr] &&
      failureData.by_day[todayStr].total
    ) {
      return failureData.by_day[todayStr].total;
    }

    // 如果没有当天的数据，返回空对象
    return {
      stop_in_jenkins_job_save_count: 0,
      stop_in_log_analysis_count: 0,
      stop_in_env_check_count: 0,
      stop_in_online_test_count: 0,
      stop_in_env_backtrack_count: 0,
      first_failures: 0,
      first_executions: 0,
      environments: 0
    };
  }

  const today = new Date();
  let startDate = new Date();

  // 根据时间范围设置起始日期
  switch (timeRange) {
    case "week":
      startDate.setDate(today.getDate() - 7);
      break;
    case "twoWeeks":
      startDate.setDate(today.getDate() - 14);
      break;
    case "month":
      startDate.setDate(today.getDate() - 30);
      break;
    default:
      return failureData.total; // 默认返回总数据
  }

  return getAggregatedDataForDateRange(failureData, startDate, today);
};

// 添加获取指定日期范围内聚合数据的函数
const getAggregatedDataForDateRange = (failureData, startDate, endDate) => {
  // 初始化聚合数据对象
  const aggregatedData = {
    stop_in_jenkins_job_save_count: 0,
    stop_in_log_analysis_count: 0,
    stop_in_env_check_count: 0,
    stop_in_online_test_count: 0,
    stop_in_env_backtrack_count: 0,
    first_failures: 0,
    first_executions: 0,
    environments: 0
  };

  // 如果没有按天数据，返回空聚合
  if (!failureData.by_day) return aggregatedData;

  // 遍历日期范围内的所有数据并聚合
  Object.keys(failureData.by_day).forEach(dateStr => {
    const date = new Date(dateStr);
    // 检查日期是否在指定范围内
    if (date >= startDate && date <= endDate) {
      const dayData = failureData.by_day[dateStr].total;
      if (dayData) {
        // 累加各个指标
        aggregatedData.stop_in_jenkins_job_save_count +=
          dayData.stop_in_jenkins_job_save_count || 0;
        aggregatedData.stop_in_log_analysis_count +=
          dayData.stop_in_log_analysis_count || 0;
        aggregatedData.stop_in_env_check_count +=
          dayData.stop_in_env_check_count || 0;
        aggregatedData.stop_in_online_test_count +=
          dayData.stop_in_online_test_count || 0;
        aggregatedData.stop_in_env_backtrack_count +=
          dayData.stop_in_env_backtrack_count || 0;
        aggregatedData.first_failures += dayData.first_failures || 0;
        aggregatedData.first_executions += dayData.first_executions || 0;
        aggregatedData.environments += dayData.environments || 0;
      }
    }
  });

  return aggregatedData;
};

// 监听时间范围变化，重新渲染图表
watch([selectedTimeRange, customStartDate, customEndDate], () => {
  if (failureData.value) {
    initCiAgentStepChart(failureData.value);
  }
});

// Jenkins任务聚合相关方法
const openAggregationDialog = () => {
  showAggregationDialog.value = true;
};

const closeAggregationDialog = () => {
  showAggregationDialog.value = false;
  aggregationData.value = null;
};

const executeAggregation = async () => {
  if (
    !aggregationParams.value.start_date ||
    !aggregationParams.value.end_date
  ) {
    ElMessage.warning("请选择开始日期和结束日期");
    return;
  }

  aggregationLoading.value = true;
  try {
    const result = await jobApi.getJenkinsJobAggregation(
      aggregationParams.value
    );
    if (result.success) {
      aggregationData.value = result.data;
      ElMessage.success("聚合查询成功");
    } else {
      ElMessage.error(`聚合查询失败: ${result.message}`);
    }
  } catch (error) {
    console.error("聚合查询出错:", error);
    ElMessage.error("聚合查询出错，请稍后重试");
  } finally {
    aggregationLoading.value = false;
  }
};

const resetAggregationParams = () => {
  aggregationParams.value = {
    start_date: "",
    end_date: "",
    env_id: "",
    principal: "",
    task_id: "",
    group_by_env: false,
    group_by_principal: false,
    group_by_task_id: false
  };
  initAggregationDates();
  aggregationData.value = null;
};

// 格式化人工处理时间
const formatManualHours = hours => {
  if (hours === 0) return "0小时";
  if (hours < 1) return `${(hours * 60).toFixed(0)}分钟`;
  return `${hours.toFixed(1)}小时`;
};
</script>

<template>
  <div class="page-container">
    <div class="data-cards-container">
      <div class="cards-grid">
        <!-- 数据卡片 -->
        <el-card :class="[$style.dataCard, 'data-card']">
          <template #header>
            <div class="card-header">
              <span>统计数据</span>
              <div class="header-right">
                <el-select
                  v-model="selectedTimeRange"
                  size="small"
                  style="margin-right: 10px"
                >
                  <el-option label="今日" value="today" />
                  <el-option label="近一周" value="week" />
                  <el-option label="近两周" value="twoWeeks" />
                  <el-option label="自定义" value="custom" />
                </el-select>
                <el-date-picker
                  v-if="selectedTimeRange === 'custom'"
                  v-model="customStartDate"
                  type="date"
                  placeholder="开始日期"
                  format="YYYY-MM-DD"
                  size="small"
                  style="width: 130px; margin-right: 5px"
                />
                <el-date-picker
                  v-if="selectedTimeRange === 'custom'"
                  v-model="customEndDate"
                  type="date"
                  placeholder="结束日期"
                  format="YYYY-MM-DD"
                  size="small"
                  style="width: 130px; margin-right: 10px"
                />
                <IconifyIconOffline icon="line-md:clipboard-check" />
              </div>
            </div>
          </template>
          <div class="data-list">
            <div class="data-row">
              <div class="data-item">
                <div class="data-label">
                  首次通过
                  <el-tooltip
                    content="例显示成功通过的任务数与总任务数比"
                    placement="top"
                  >
                    <el-icon class="info-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="data-value">
                  {{
                    selectedTimeRange === "custom" &&
                    customStartDate &&
                    customEndDate
                      ? getPassProportion(
                          "custom",
                          customStartDate,
                          customEndDate
                        )
                      : getPassProportion(selectedTimeRange)
                  }}
                </div>
                <div class="data-trend down">
                  <span>{{
                    calculatePercentage(
                      selectedTimeRange === "custom" &&
                        customStartDate &&
                        customEndDate
                        ? getPassProportion(
                            "custom",
                            customStartDate,
                            customEndDate
                          )
                        : getPassProportion(selectedTimeRange)
                    )
                  }}</span>
                </div>
              </div>
              <div class="data-item">
                <div class="data-label">
                  复测
                  <el-tooltip
                    content="显示所有失败任务中进入复测流程的比例"
                    placement="top"
                  >
                    <el-icon class="info-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="data-value">
                  {{
                    selectedTimeRange === "custom" &&
                    customStartDate &&
                    customEndDate
                      ? getOnlineTestProportion(
                          "custom",
                          customStartDate,
                          customEndDate
                        )
                      : getOnlineTestProportion(selectedTimeRange)
                  }}
                </div>
                <div class="data-trend down">
                  <span>{{
                    calculatePercentage(
                      selectedTimeRange === "custom" &&
                        customStartDate &&
                        customEndDate
                        ? getOnlineTestProportion(
                            "custom",
                            customStartDate,
                            customEndDate
                          )
                        : getOnlineTestProportion(selectedTimeRange)
                    )
                  }}</span>
                </div>
              </div>
              <div class="data-item">
                <div class="data-label">
                  回溯
                  <el-tooltip
                    content="显示所有失败任务中进入回溯流程的比例"
                    placement="top"
                  >
                    <el-icon class="info-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="data-value">
                  {{
                    selectedTimeRange === "custom" &&
                    customStartDate &&
                    customEndDate
                      ? getRollbackProportion(
                          "custom",
                          customStartDate,
                          customEndDate
                        )
                      : getRollbackProportion(selectedTimeRange)
                  }}
                </div>
                <div class="data-trend down">
                  <span>{{
                    calculatePercentage(
                      selectedTimeRange === "custom" &&
                        customStartDate &&
                        customEndDate
                        ? getRollbackProportion(
                            "custom",
                            customStartDate,
                            customEndDate
                          )
                        : getRollbackProportion(selectedTimeRange)
                    )
                  }}</span>
                </div>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <div class="data-label">
                  环境检测
                  <el-tooltip
                    content="显示所有失败任务中环境监测异常的比例"
                    placement="top"
                  >
                    <el-icon class="info-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="data-value">
                  {{
                    selectedTimeRange === "custom" &&
                    customStartDate &&
                    customEndDate
                      ? getEnvCheckProportion(
                          "custom",
                          customStartDate,
                          customEndDate
                        )
                      : getEnvCheckProportion(selectedTimeRange)
                  }}
                </div>
                <div class="data-trend down">
                  <span>{{
                    calculatePercentage(
                      selectedTimeRange === "custom" &&
                        customStartDate &&
                        customEndDate
                        ? getEnvCheckProportion(
                            "custom",
                            customStartDate,
                            customEndDate
                          )
                        : getEnvCheckProportion(selectedTimeRange)
                    )
                  }}</span>
                </div>
              </div>
              <div class="data-item">
                <div class="data-label">
                  日志分析
                  <el-tooltip
                    content="显示所有失败任务中日志分析失败的比例"
                    placement="top"
                  >
                    <el-icon class="info-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="data-value">
                  {{
                    selectedTimeRange === "custom" &&
                    customStartDate &&
                    customEndDate
                      ? getLogAnalysisProportion(
                          "custom",
                          customStartDate,
                          customEndDate
                        )
                      : getLogAnalysisProportion(selectedTimeRange)
                  }}
                </div>
                <div class="data-trend down">
                  <span>{{
                    calculatePercentage(
                      selectedTimeRange === "custom" &&
                        customStartDate &&
                        customEndDate
                        ? getLogAnalysisProportion(
                            "custom",
                            customStartDate,
                            customEndDate
                          )
                        : getLogAnalysisProportion(selectedTimeRange)
                    )
                  }}</span>
                </div>
              </div>
              <div class="data-item">
                <!-- <div class="data-label">指标六
                <el-tooltip content="显示当天成功通过的任务数与总任务数比例" placement="top">
                  <el-icon class="info-icon">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="data-value">0/0</div>
              <div class="data-trend down">
                <span>0/0</span>
              </div> -->
              </div>
            </div>
          </div>
        </el-card>

        <!-- 饼图卡片 -->
        <el-card :class="[$style.chartCard, 'chart-card']">
          <template #header>
            <div class="card-header">
              <span
                >流程统计数据
                <el-tooltip
                  content="所有任务执行流程的统计数据"
                  placement="top"
                >
                  <el-icon class="info-icon">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
              <div class="header-right">
                <el-select
                  v-model="selectedTimeRange"
                  size="small"
                  style="margin-right: 10px"
                >
                  <el-option label="今日" value="today" />
                  <el-option label="近一周" value="week" />
                  <el-option label="近两周" value="twoWeeks" />
                  <el-option label="自定义" value="custom" />
                </el-select>
                <el-date-picker
                  v-if="selectedTimeRange === 'custom'"
                  v-model="customStartDate"
                  type="date"
                  placeholder="开始日期"
                  format="YYYY-MM-DD"
                  size="small"
                  style="width: 130px; margin-right: 5px"
                />
                <el-date-picker
                  v-if="selectedTimeRange === 'custom'"
                  v-model="customEndDate"
                  type="date"
                  placeholder="结束日期"
                  format="YYYY-MM-DD"
                  size="small"
                  style="width: 130px; margin-right: 10px"
                />
                <IconifyIconOffline icon="line-md:clipboard-check" />
              </div>
              <IconifyIconOffline icon="line-md:pie-chart" />
            </div>
          </template>
          <div id="ciAgentStep" class="chart" />
        </el-card>

        <!-- 折线图卡片 -->
        <el-card :class="[$style.chartCard, 'chart-card']">
          <template #header>
            <div class="card-header">
              <span>一次成功率</span>
              <IconifyIconOffline icon="line-md:document-report" />
            </div>
          </template>
          <div id="firstSuccessRateByDay" class="chart" />
        </el-card>

        <!-- 柱状图卡片 -->
        <el-card :class="[$style.chartCard, 'chart-card']">
          <template #header>
            <div class="card-header">
              <span>环境稳定性</span>
              <IconifyIconOffline icon="line-md:bar-chart" />
            </div>
          </template>
          <div id="firstSuccessRateByEnv" class="chart" />
        </el-card>

        <!-- Jenkins任务聚合报表卡片 -->
        <el-card :class="[$style.chartCard, 'chart-card']">
          <template #header>
            <div class="card-header">
              <span>Jenkins任务聚合报表</span>
              <el-button
                type="primary"
                size="small"
                @click="openAggregationDialog"
              >
                查看报表
              </el-button>
            </div>
          </template>
          <div class="aggregation-summary">
            <div class="summary-item">
              <div class="summary-label">时间范围聚合</div>
              <div class="summary-desc">支持按时间范围查询任务统计</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">多维度过滤</div>
              <div class="summary-desc">支持按环境、负责人、任务ID过滤</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">人工处理时间</div>
              <div class="summary-desc">统计manual_processing_hours总和</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">分组统计</div>
              <div class="summary-desc">支持按多个维度分组查看详情</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 详细信息对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="detailDialogTitle"
      width="60%"
      destroy-on-close
      align-center
    >
      <el-table
        v-loading="detailTableLoading"
        :data="paginatedDetailData"
        style="width: 100%"
        border
        :height="tableHeight"
      >
        <el-table-column
          prop="task_id"
          label="任务ID"
          width="200"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            <el-tooltip :content="scope.row.task_id" placement="top">
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: block;
                "
              >
                {{ scope.row.task_id }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="env_id"
          label="环境ID"
          width="260"
          header-align="center"
          align="center"
        />
        <el-table-column
          prop="date"
          label="日期"
          width="140"
          header-align="center"
          align="center"
        />
        <el-table-column
          prop="current_state"
          label="人工确认状态"
          width="140"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.current_state === 'PASS'
                  ? 'success'
                  : scope.row.current_state === 'FAIL'
                    ? 'danger'
                    : 'info'
              "
            >
              {{
                scope.row.current_state === "PASS"
                  ? "通过"
                  : scope.row.current_state === "FAIL"
                    ? "失败"
                    : "待确认"
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="fail_reason"
          label="人工反馈失败结果"
          width="240"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            <el-tooltip :content="scope.row.fail_reason" placement="top">
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: block;
                "
              >
                {{ scope.row.fail_reason }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="manual_processing_hours"
          label="人工处理时间(H)"
          width="140"
          header-align="center"
          align="center"
        />
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :total="detailTotal"
          layout="total, prev, pager, next, jumper"
          @current-change="handleDetailCurrentChange"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Jenkins任务聚合报表对话框 -->
    <el-dialog
      v-model="showAggregationDialog"
      title="Jenkins任务聚合报表"
      width="90%"
      destroy-on-close
      align-center
    >
      <div class="aggregation-container">
        <!-- 查询条件区域 -->
        <el-card class="query-card">
          <template #header>
            <span>查询条件</span>
          </template>
          <el-form :model="aggregationParams" label-width="120px" inline>
            <el-form-item label="开始日期" required>
              <el-date-picker
                v-model="aggregationParams.start_date"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="结束日期" required>
              <el-date-picker
                v-model="aggregationParams.end_date"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="环境ID">
              <el-input
                v-model="aggregationParams.env_id"
                placeholder="可选，如：RAN3-T17-10466"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="负责人">
              <el-input
                v-model="aggregationParams.principal"
                placeholder="可选，环境维护负责人"
                style="width: 150px"
              />
            </el-form-item>
            <el-form-item label="任务ID">
              <el-input
                v-model="aggregationParams.task_id"
                placeholder="可选，特定任务ID"
                style="width: 200px"
              />
            </el-form-item>
          </el-form>

          <el-divider content-position="left">分组选项</el-divider>

          <el-form :model="aggregationParams" label-width="120px" inline>
            <el-form-item label="按环境分组">
              <el-switch v-model="aggregationParams.group_by_env" />
            </el-form-item>
            <el-form-item label="按负责人分组">
              <el-switch v-model="aggregationParams.group_by_principal" />
            </el-form-item>
            <el-form-item label="按任务ID分组">
              <el-switch v-model="aggregationParams.group_by_task_id" />
            </el-form-item>
          </el-form>

          <div class="query-actions">
            <el-button
              type="primary"
              :loading="aggregationLoading"
              @click="executeAggregation"
            >
              查询聚合
            </el-button>
            <el-button @click="resetAggregationParams">重置</el-button>
          </div>
        </el-card>

        <!-- 结果展示区域 -->
        <el-card v-if="aggregationData" class="result-card">
          <template #header>
            <span>聚合结果</span>
          </template>

          <!-- 总体统计 -->
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-label">总任务数</div>
              <div class="stat-value">{{ aggregationData.total_jobs }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">唯一任务数</div>
              <div class="stat-value">
                {{ aggregationData.unique_task_count }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">总人工处理时间</div>
              <div class="stat-value">
                {{ formatManualHours(aggregationData.total_manual_hours) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">查询时间范围</div>
              <div class="stat-value">
                {{ aggregationData.time_range.start_date }} 至
                {{ aggregationData.time_range.end_date }}
              </div>
            </div>
          </div>

          <!-- 环境分组结果 -->
          <div
            v-if="
              aggregationData.env_groups &&
              aggregationData.env_groups.length > 0
            "
            class="group-section"
          >
            <h4>按环境分组统计</h4>
            <el-table
              :data="aggregationData.env_groups"
              border
              style="width: 100%"
            >
              <el-table-column prop="env_id" label="环境ID" width="300" />
              <el-table-column
                prop="total_jobs"
                label="总任务数"
                width="120"
                align="center"
              />
              <el-table-column
                prop="unique_task_count"
                label="唯一任务数"
                width="120"
                align="center"
              />
              <el-table-column
                prop="total_manual_hours"
                label="人工处理时间"
                width="150"
                align="center"
              >
                <template #default="scope">
                  {{ formatManualHours(scope.row.total_manual_hours) }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 负责人分组结果 -->
          <div
            v-if="
              aggregationData.principal_groups &&
              aggregationData.principal_groups.length > 0
            "
            class="group-section"
          >
            <h4>按负责人分组统计</h4>
            <el-table
              :data="aggregationData.principal_groups"
              border
              style="width: 100%"
            >
              <el-table-column prop="principal" label="负责人" width="150" />
              <el-table-column
                prop="total_jobs"
                label="总任务数"
                width="120"
                align="center"
              />
              <el-table-column
                prop="unique_task_count"
                label="唯一任务数"
                width="120"
                align="center"
              />
              <el-table-column
                prop="total_manual_hours"
                label="人工处理时间"
                width="150"
                align="center"
              >
                <template #default="scope">
                  {{ formatManualHours(scope.row.total_manual_hours) }}
                </template>
              </el-table-column>
              <el-table-column prop="env_ids" label="负责环境" min-width="300">
                <template #default="scope">
                  <el-tag
                    v-for="envId in scope.row.env_ids"
                    :key="envId"
                    size="small"
                    style="margin-right: 5px"
                  >
                    {{ envId }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 任务ID分组结果 -->
          <div
            v-if="
              aggregationData.task_id_groups &&
              aggregationData.task_id_groups.length > 0
            "
            class="group-section"
          >
            <h4>按任务ID分组统计</h4>
            <el-table
              :data="aggregationData.task_id_groups"
              border
              style="width: 100%"
              max-height="400"
            >
              <el-table-column prop="task_id" label="任务ID" width="200">
                <template #default="scope">
                  <el-tooltip :content="scope.row.task_id" placement="top">
                    <span
                      style="
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: block;
                      "
                    >
                      {{ scope.row.task_id }}
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="total_jobs"
                label="任务数"
                width="100"
                align="center"
              />
              <el-table-column
                prop="total_manual_hours"
                label="人工处理时间"
                width="150"
                align="center"
              >
                <template #default="scope">
                  {{ formatManualHours(scope.row.total_manual_hours) }}
                </template>
              </el-table-column>
              <el-table-column prop="env_id" label="环境ID" width="250" />
              <el-table-column prop="principal" label="负责人" width="120" />
            </el-table>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeAggregationDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<!-- 基础样式 - 不使用封装 -->
<style>
/* 全局样式 - 可以被其他组件访问 */
:global(.chart-container) {
  margin-top: 20px;
}
</style>
<!-- 使用 scoped 的样式 - 只应用于当前组件 -->
<style scoped>
.data-cards-container {
  padding: 20px;
}
.page-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}
.chart-card,
.data-card {
  height: 350px;
  transition: all 0.3s;
}
.chart-card:hover,
.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 调整下拉选择框的样式 */
:deep(.el-select) {
  width: 80px;
}
.chart {
  height: 280px;
  width: 100%;
}
/* 使用深度选择器修改子组件样式 */
:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid var(--pure-border-color);
}

:deep(.el-card__body) {
  padding-top: 10px; /* 调整上边距 */
}
@media (max-width: 1200px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<!-- 使用 CSS Modules 的样式 -->
<style module>
.chartCard {
  border-radius: 8px;
  overflow: hidden;
}
.dataCard {
  border-radius: 8px;
  overflow: hidden;
}
.chartCard:hover,
.dataCard:hover {
  border-color: var(--el-color-primary-light-5);
}
</style>
<!-- 使用 scoped 的另一个样式块，专门用于数据指标样式 -->
<style scoped>
.data-list {
  height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.data-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.data-item {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  width: 33.33%;
  border-right: 1px solid var(--el-border-color-lighter);
}
.data-item:last-child {
  border-right: none;
}
.data-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}
.data-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}
.data-trend {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.data-trend.up {
  color: #67c23a;
}
.data-trend.down {
  color: #f56c6c;
}
.data-trend span {
  margin-left: 4px;
}
.info-icon {
  font-size: 12px;
  margin-left: 0px;
  color: var(--el-text-color-secondary);
  cursor: help;
}
</style>
