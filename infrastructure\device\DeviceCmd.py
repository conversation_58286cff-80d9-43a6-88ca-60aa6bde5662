
from infrastructure.device.CmdResult import CmdResult


class DeviceCmd():
    '''
    classdocs
    '''

    def __init__(self, command: str, expected: str = None, timeout: int | float = 5):
        self._command = command
        self._expected = expected
        self._timeout = timeout
        self._cmdResult = CmdResult()

    def fail_to_excute(self, fail_reason):
        self._cmdResult.fail_to_excute(fail_reason)

    def success_to_excute(self, return_string):
        self._cmdResult.success_to_excute(return_string)

    @property
    def command(self):
        return self._command

    @command.setter
    def command(self, value):
        self._command = value

    @property
    def expected(self):
        return self._expected

    @property
    def timeout(self):
        return self._timeout

    @property
    def cmd_result(self):
        return self._cmdResult
