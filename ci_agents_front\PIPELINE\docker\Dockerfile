FROM public-docker-virtual.artnj.zte.com.cn/node:alpine as build-stage

ARG SERVICE_NAME=ci-agents-front

WORKDIR /app

RUN npm config set registry https://artnj.zte.com.cn/artifactory/api/npm/public-npm-virtual/ && \
    npm install -g pnpm

ADD ./$SERVICE_NAME.tar ./
RUN ls -l ./ && pnpm install --frozen-lockfile && pnpm build

FROM public-docker-virtual.artnj.zte.com.cn/nginx:1.25.2-alpine-slim as production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/nginx.conf

CMD ["nginx", "-g", "daemon off;"]