"""
@Author: <EMAIL>
@Date: 2025/4/24 上午10:10
@File: constants.py
@Description: 
"""

ue_fail_rules = {
    "name": "UE接入",
    "execute_error": "Test execution stopped due to a fatal error.",
    "assert_error": "AssertionError"
}
fail_class = {"upgrade": "升级用例失败",
              "ue_attach": "接入用例失败",
              "ping": "ping包用例失败",
              "flooding_zero": "灌包流量为0",
              "flooding_low": "灌包流量不足",
              "other": "当前错误未收录，请联系开发人员补充"
              }
error_type_substrings = {
    "flooding": ["灌", "流量", 'UDP'],
    "ping": ["Ping", "ping"],
    "attach": ["接入"],
    "upgrade": ["升级", "版本"]
}
