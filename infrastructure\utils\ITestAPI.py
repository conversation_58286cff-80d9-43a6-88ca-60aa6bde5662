"""
@File: ITestAPI.py
@Author: 许王禾子10333721
@Time: 2024/2/20 上午11:15
@License: Copyright 2022-2030
@Desc: None
"""
import requests
from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.logger.logger import logger


class ITestAPI:

    def __init__(self):
        self._url = "http://*************:8989/zte-rdcloud-itest-api/api/v1"

    @retries_on_exception(3, everyTryDelaySecs=5)
    def query_script_paths(self, caseIds: list) -> list:
        route = "/scriptlib/tree/leafs/query"
        url = self._url + route
        body = {"gerritUrl": "http://gerrit.zte.com.cn/a/5g_nr_v3/script_v3",
                "branch": "master",
                "testCenterCaseIds": caseIds}
        response = requests.post(url, json=body, timeout=3)

        resp = ResponseFormatter(response)
        if not resp.success:
            errMsg = f"itest 服务请求失败, {resp.text}"
            logger.error(errMsg)
            raise Exception(errMsg)

        return resp.result.get("scripts")


class ResponseFormatter(requests.Response):
    def __init__(self, response: requests.Response):
        super().__init__()
        self.__dict__.update(response.__dict__)
        self._data = self.json()
        """
        返回体结构：
        {
            "code": {
                "code": "0000",
                "msg": "请求操作成功",
                "errDetailMsg": "",
                "msgId": "SUCCESS",
                "errorSubType": ""
            },
            "bo": {
                "scripts": [
                    {
                        "name": "VSWd单板下，算法库模型加载成功全流程验证__RAN-1831549",
                        "namePath": "script_v3/5GNR/test/testcases/IM-IPA/LV-IPA相关特性/M1初始上电准备/M1初始上电准备.tsv/VSWd单板下，算法库模型加载成功全流程验证__RAN-1831549",
                        "nodeId": "1aad927f-2b58-51f2-92b2-c0aadaef22a6",
                        "pNodeId": "c6281631-618a-5c20-83b0-878c76da42e4",
                        "path": "/0c032584-f002-51c3-ae72-b2ef6f772cec/991f34a7-31a2-5cb5-b7c1-3cab331d3201/3f28e46d-3339-573b-997a-f7ec1680a550/a3a12728-051a-5469-bcfe-61418866c4b1/99803ccc-3710-5902-9990-66dfabef7c47/48af49e0-915d-5bb0-9e2c-bef1d9ea42b1/038af9cb-10fa-507d-a0b5-9d89520afe18/76cc6a61-3441-575e-a90f-954fe988412f/c6281631-618a-5c20-83b0-878c76da42e4/1aad927f-2b58-51f2-92b2-c0aadaef22a6",
                        "tag": [],
                        "testCenterCaseIds": [
                            "RAN-1831549"
                        ],
                        "type": "testcase"
                    }
                ]
            }
        }
        """

    @property
    def success(self):
        if self.status_code != 200:
            return False
        if self._data["code"]["code"] != "0000":
            return False
        return True

    @property
    def result(self):
        return self._data["bo"]




