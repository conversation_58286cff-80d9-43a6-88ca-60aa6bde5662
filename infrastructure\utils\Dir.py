import fnmatch
import os
import shutil

# abstract_dir
''' os.walk(dirPath) ：走查指定的文件夹路径
          root  ：代表目录的路径
          dirs  ：一个list，包含了dirpath下所有子目录文件夹的名字
          files ：一个list，包含了所有非目录文件的名字
  '''


def get_all_file_path_in_dir(dir_path: str):
    all_files = []
    for root, dirs, files in os.walk(os.path.abspath(dir_path)):
        for file in files:
            if not is_biz_file(file):
                continue
            all_files.append(os.path.join(root, file))
    return all_files


def get_target_files_in_dir(dir_path: str, pat: str) -> list[str]:
    """ 通过入参pat 传入过滤条件，找出dir_path 文件夹中所有复合pat条件的文件，比如 pat = ‘*.txt’ """
    all_files = []
    for root, dirs, files in os.walk(os.path.abspath(dir_path)):
        for file in fnmatch.filter(files, pat):
            all_files.append(os.path.join(root, file))
        for d in dirs:
            all_files += get_target_files_in_dir(d, pat=pat)
    return all_files


def is_biz_file(file_path: str):
    return file_path.endswith(".csv") or file_path.endswith(".xlsx")


def is_file_exist(file_path):
    try:
        os.stat(file_path)
    except FileNotFoundError:
        # 👇️ this runs
        raise Exception('The specified file does NOT exist!,{0}'.format(file_path))


def get_path_sep(file_path: str) -> str:
    if ("/" in file_path):
        return "/"
    return "\\"


def del_handle_file(file_path):
    if file_path.endswith(".zip"):
        shutil.rmtree(file_path.split(".zip")[0:-1][0])
    os.remove(file_path)


def create_file_path(file_path: str):
    if not os.path.exists(file_path):
        os.makedirs(file_path)
