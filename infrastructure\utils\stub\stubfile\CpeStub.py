# coding=utf-8
OPERATIONS = {
}

STUBS = {
    "addIpExactMatchCmpForUl": [{
        'cmd': 'DxgeAddIpExactMatchCmp 105,0xc1fe0_ULSLOT_30',
        'expected': 'begin to excel fun',
        'proc': 'MGR'}],
    "addIpExactMatchCmpForDl": [{
        'cmd': 'DxgeAddIpExactMatchCmp 105,0xc1fe0_DLSLOT_50',
        'expected': 'begin to excel fun',
        'proc': 'MGR'
    }, {
        'cmd': 'L2ssWriteReg 0x00202000,0x110',
        'expected': 'begin to excel fun',
        'proc': 'MGR'
    }, {
        'cmd': 'DxgeAddIpExactMatchCmp 91,0x3C0187F3',
        'expected': 'begin to excel fun',
        'proc': 'MGR'
    }]
}

if __name__ == "__main__":
    print(STUBS["bspShowSlaveCoreInfo"])
