#!/bin/bash
set -e

SERVICE_NAME=$(awk -F '=' '/^SERVICE_NAME/ {print $2}' build_config.ini)
TAG=$(awk -F '=' '/^TAG/ {print $2}' build_config.ini)
IMAGE_NAME=$SERVICE_NAME:$TAG
CONTAINER_NAME=$SERVICE_NAME-$TAG

clean_pack_tar(){
    pwd
    echo "--before.sh-- rm begin"
    # 删除既有服务tar包
    rm -rf $SERVICE_NAME.tar || true
    rm -rf ../../$SERVICE_NAME.tar || true
    # 删除镜像及打包zip文件
    rm -rf *.tar || true
    rm -rf $CONTAINER_NAME.zip || true
    # 清空log文件夹
    rm -rf ../../log/* || true
    rm -rf ../../logs/* || true
    # 清空download文件夹
    rm -rf ../../download/* || true
    echo "--before.sh-- rm end"
    ls -l

    echo "--before.sh-- tar begin"
    # 创建服务tar包
    echo "source code"
    cd ../..
    tar czvf $SERVICE_NAME.tar * --exclude=./$SERVICE_NAME.tar || true
    mv $SERVICE_NAME.tar ./PIPELINE/docker/$SERVICE_NAME.tar

    cd ./PIPELINE/docker
    ls -l
}

build_image(){
    #停止并删除容器
    docker stop $CONTAINER_NAME || echo "ignore"
    docker rm $CONTAINER_NAME || echo "ignore"
    docker ps -a

    #删除镜像并构建镜像
    docker rmi -f $IMAGE_NAME || echo "ignore"
    docker build --no-cache --build-arg SERVICE_NAME=$SERVICE_NAME -t $IMAGE_NAME .
    docker images -a

    #打包镜像
    docker save -o $IMAGE_NAME.tar $IMAGE_NAME
    zip $CONTAINER_NAME.zip $IMAGE_NAME.tar deploy.sh
}

main(){
    clean_pack_tar
    build_image
}

main