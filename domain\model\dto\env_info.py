from pydantic import BaseModel, Field
from datetime import datetime
import uuid


class BaseEnvInfo(BaseModel):
    env_id: str = Field(..., example="RAN3-T17-10466")
    principal: str = Field("", description="环境维护负责人")
    create_time: str = Field(default_factory=lambda: datetime.now().isoformat(), frozen=True)
    update_time: str = Field(default_factory=lambda: datetime.now().isoformat())
    update_user: str = Field("")
    param: str = Field("")
    service_type: str = Field("")
    result: str = Field("")


class JenkinsServerEnvInfo(BaseEnvInfo):
    test_domain: str
    jenkins_url: str
    jenkins_username: str
    jenkins_password: str


class PcEnvInfo(BaseEnvInfo):
    pc_ip: str
    pc_username: str
    pc_password: str


class VersionEnvInfo(BaseEnvInfo):
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    full_path: str
    last_success_version: str
    curr_version: str
    jenkins_job_name: str
    jenkins_build_number: str
    version_branch: str
    version_test_result: str
    version_list: str


class DeviceEnvInfo(BaseEnvInfo):
    me_id: str
    pc_ip: str
    pc_username: str
    pc_password: str
    ue_ip: list[str]
    device_id: list[str]
    pc_net: list[str]


class BizCoreEnv(BaseEnvInfo):
    jenkins_url: str
