from infrastructure.logger.logger import logger


class CmdResult(object):
    def __init__(self):
        '''
        Constructor
        '''
        self._result = True
        self._failReason = ''
        self._returnInfo = {}
        self._returnString = ''

    def fail_to_excute(self, fail_reason: str):
        self.failed()
        self.update_fail_reason(fail_reason)
        logger.debug(f"failed buff = {repr(fail_reason)}")

    def success_to_excute(self, return_string: str):
        self.succeed()
        self.update_return_string(return_string)

    def success_to_parse(self, return_info):
        self.succeed()
        self.update_return_info(return_info)

    def update_return_string(self, return_string: str):
        self._returnString = return_string

    def update_return_info(self, return_info: str):
        self._returnInfo = return_info

    def update_fail_reason(self, fail_reason: str):
        self._failReason = fail_reason

    def decode(self, codeFormat: str):
        self.update_return_string(self._returnString.decode(codeFormat))

    def failed(self):
        self._result = False

    def succeed(self):
        self._result = True

    def put(self, key, value):
        if not isinstance(self._returnInfo, dict):
            self._returnInfo = {}
        self._returnInfo.update({key: value})

    @property
    def result(self):
        return self._result

    @property
    def fail_reason(self):
        return self._failReason

    @property
    def return_info(self):
        return self._returnInfo

    @property
    def return_string(self):
        return self._returnString.replace('\u0000', '')
