from datetime import datetime

from pydantic import BaseModel, Field
from typing import Literal, Optional
import uuid


class Needed(BaseModel):
    tar_path: str = ""
    log_path: str = ""
    log_name: str = ""
    job_name: str = ""
    build_number: str = ""

    pipelineUuid: str = ""
    pipelineStageId: str = ""
    recordId: int = ""


class CornTask(BaseModel):
    env_id: str
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    cron_task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    needed: Needed
    service_type: str
    state: str
    subtype: Optional[Literal['normal', "first_time", 'rollback', 'update', 'online_test']] = "normal"
    create_time: str = Field(default_factory=lambda: datetime.now().isoformat(), frozen=True)
    update_time: str = Field(default_factory=lambda: datetime.now().isoformat())