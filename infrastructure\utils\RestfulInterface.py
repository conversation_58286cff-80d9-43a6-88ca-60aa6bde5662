'''
Created on 2018年4月19日

@author: 10193015
'''


from infrastructure.utils.FileHandler import FileHandler
import logging


class RestfulInterface(object):

    '''
    classdocs
    '''

    def __init__(self, jsonFolder):
        '''
        Constructor
        '''
        self._interfacesDict = FileHandler.load_json_file(jsonFolder)

    def is_exist(self, interfaceKey):
        return interfaceKey in self._interfacesDict

    def update_config_json(self, jsonFolder):
        self._interfacesDict.update(FileHandler.load_json_file(jsonFolder))

    def find(self, interfaceKey):
        if interfaceKey not in self._interfacesDict.keys():
            logging.warning("Not Found Key: %s" % interfaceKey)
            return None
        else:
            return self._interfacesDict.get(interfaceKey)
