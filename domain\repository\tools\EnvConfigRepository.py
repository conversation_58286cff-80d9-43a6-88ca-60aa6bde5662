# -*-coding:utf-8 -*-
from infrastructure.tdl.TdlApi import EnvConfigApi



class EnvConfigRepository(object):
    @staticmethod
    def get_env_config(env_id, group_no=""):
        from domain.model.tools.EnvConfig import EnvConfigResponse
        data = {
            "envId": env_id,
            "groupNo": group_no
        }
        return EnvConfigResponse(env_id, group_no, **EnvConfigApi.get_historical_config(data))