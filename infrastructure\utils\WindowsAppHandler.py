# -*- encoding: utf-8 -*-
'''
@File    :   CustomList.py
@Time    :   2024/01/13 18:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''

import os
import subprocess
import psutil
import platform

from infrastructure.logger.logger import logger


def get_running_app_path(app_name):
    if platform.system() == "Linux":
        logger.warning("暂时不支持本地linux机器查看app路径")
        return None
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] == app_name:
                path = proc.exe()
                return path
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return None

def kill_running_app(app_name):
    if platform.system() == "Linux":
        logger.warning("暂时不支持本地linux机器查看app路径")
        return None
    commands = f'taskkill /F /IM {app_name}'
    subprocess.call(commands, shell=True)

def run_app_by_path(app_path):
    if platform.system() == "Linux":
        logger.warning("暂时不支持本地linux机器查看app路径")
        return None
    if app_path:
        if os.path.exists(app_path):
            commands = f'start "" "{app_path}"'
            subprocess.run(commands, shell=True, timeout=5, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        else:
            raise Exception(f"{app_path} 文件不存在")


if __name__ == '__main__':
    path = get_running_app_path("MFTPD.exe")
    kill_running_app("MFTPD.exe")
    run_app_by_path(path)

