# coding=utf-8

import json
import logging
import inspect


def object2dict(obj):
    """convert object to a dict"""
    d = {}
    # d['__class__'] = obj.__class__.__name__
    # d['__module__'] = obj.__module__
    d.update(obj.__dict__)
    return d


def dict2object(d):
    """convert dict to object"""
    if isinstance(d, dict) and '__class__' in d:
        class_name = d.pop('__class__')
        module_name = d.pop('__module__')
        module = __import__(module_name, fromlist=(class_name))
        class_ = getattr(module, class_name)
        args = dict((key.encode('ascii'), dict2object(value)) for key, value in d.items() if
                    key in inspect.getargspec(class_.__init__)[0])
        inst = class_(**args)
    else:
        inst = d
    return inst


def unmarshal(jsonStr, obj=""):
    try:
        if isinstance(jsonStr, bytes):
            jsonStr = jsonStr.decode('utf-8')
        objectsData = jsonStr
        if isinstance(objectsData, str):
            objectsData = json.loads(jsonStr)
    except ValueError as e:
        logging.error('!!unmarshal json string failed: %s' % e)
        return
    if not objectsData:
        return
    classInfo = {}
    if obj != "":
        classInfo = {'__class__': obj.__class__.__name__, '__module__': obj.__module__}
        classInfo.update(obj.__dict__)
    if isinstance(objectsData, list):
        objects = []
        for objectDict in objectsData:
            # objectDict.update(classInfo)
            classInfoTmp = {}
            classInfoTmp.update(classInfo)
            classInfoTmp.update(objectDict)
            objects.append(dict2object(classInfoTmp))
        return objects
    # objectsData.update(classInfo)
    classInfo.update(objectsData)
    return dict2object(classInfo)


def marshal(obj):
    return json.dumps(obj, default=object2dict, ensure_ascii=False)


def json2dict(raw_msg):
    return json.loads(raw_msg, encoding='utf-8')


def check_json_format(raw_msg):
    try:
        json.loads(raw_msg, encoding='utf-8')
    except ValueError:
        return False
    return True
