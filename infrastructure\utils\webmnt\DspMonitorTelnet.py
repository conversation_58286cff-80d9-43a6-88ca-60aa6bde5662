# -*- coding:utf-8 -*
import telnetlib
import time
import re
import struct
import threading
import os
import subprocess
from .BaseMonitor import *

# from ..dspmonitor import WEBMNT_CONFIG
WEBMNT_CONFIG = "WebMntConfig"


class TelentC(Runer):
    processWatcher = None

    @staticmethod
    def Monitor():
        if (TelentC.processWatcher):
            TelentC.processWatcher.Monitor()

    def __init__(self, host, port, waitfor):
        self.host = host
        self.port = port
        self.waitfor = waitfor
        self.tn = None
        self.d1 = "a"
        self.d2 = "b"
        self.CRTL_AND_C = b"\03"

    def __run(self, cmd, timeout=MONITOR_CMD_WAIT_TIME, waitfor=None):
        if (not self.tn):
            raise MonitorExcption("not login, please login...")
        # waitstr = "\n" + (waitfor if waitfor else self.waitfor)
        waitstr = (waitfor if waitfor else self.waitfor)
        logging.info("CMD:" + cmd)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(cmd.encode('ascii') + b'\n')
        result = self.tn.read_until(waitstr.encode('ascii'), timeout)
        res = res.decode('utf-8', errors='ignore')
        result = result.decode('utf-8', errors='ignore')
        logging.info(res)
        logging.info(result)
        if (not result):
            time.sleep(5)
            raise MonitorExcption("not wait ack")

        return result

    def __looprun(self, cmd, timeout=MONITOR_CMD_WAIT_TIME, waitfor=None):
        if (not self.tn):
            raise MonitorExcption("not login, please login...")
        # waitstr = "\n" + (waitfor if waitfor else self.waitfor)
        waitstr = (waitfor if waitfor else self.waitfor)
        logging.info("CMD:" + cmd)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(b'\n')
        res = self.tn.read_until(self.waitfor.encode('ascii'), timeout)
        self.tn.write(cmd.encode('ascii') + b'\n')
        res = res.decode('utf-8', errors='ignore')
        logging.info(res)
        count = 0
        while count < 30:
            result = self.tn.read_until(waitstr.encode('ascii'), timeout)
            result = result.decode('utf-8', errors='ignore')
            if result:
                logging.info(result)
            count += 1
            if -1 != result.find(waitstr):
                break

        if (not result):
            time.sleep(5)
            raise MonitorExcption("not wait ack")

        return result

    def ProcesstelnetExcp(self, e):
        excp = ["connection closed",
                "not wait ack",
                "Errno 10054",
                "Errno 10061"
                ]
        msg = str(e)
        if 0 == sum([1 for s in excp if -1 == msg.find(s)]):
            raise MonitorExcption("excption" + msg)
        self.close()
        logging.info(msg)
        logging.info("cmd run except, retry.......")
        logging.info("not login or start DspMonitor, please start and login ")

    def run(self, cmd, timeout=15, waitfor=None):
        count = 0
        while True and count < MONITOR_REPEAT_RUN_COUNT:
            try:
                return self.__run(cmd, timeout, waitfor)
            except Exception as e:
                self.ProcesstelnetExcp(e)
                self.login()
            count += 1
        if count >= MONITOR_REPEAT_RUN_COUNT:
            msg = "can not run  cmd, check DspMonitor is started!"
            logging.info(msg)
            raise MonitorExcption(msg)
        return "can not run  cmd, check DspMonitor is started!"

    def looprun(self, cmd, timeout=5, waitfor=None):
        try:
            return self.__looprun(cmd, timeout, waitfor)
        except Exception as e:
            self.ProcesstelnetExcp(e)
            self.login()

    def __Login(self):
        TelentC.Monitor()
        print(self.host)
        print(self.port)
        self.tn = telnetlib.Telnet(self.host, self.port)
        self.tn.set_debuglevel(0)
        self.tn.write(b"\n")
        self.tn.read_until(b"connectID:", 2)
        self.tn.write(self.d1.encode('ascii') + b'\n')
        self.tn.read_until(b"check:")
        self.tn.write(self.d2.encode('ascii') + b'\n')
        self.tn.read_until(self.waitfor.encode('ascii'))
        # logging.info("----->login----->" + res)

    def login(self):
        count = 0
        while True and count < MONITOR_REPEAT_RUN_COUNT:
            try:
                self.__Login()
                return True
            except MonitorReOpenExcption as e:
                raise MonitorReOpenExcption(e.value)
            except Exception as e:
                self.ProcesstelnetExcp(e)
            count += 1
        if count >= MONITOR_REPEAT_RUN_COUNT:
            msg = "can not login DspMonitor, check DspMonitor is started!"
            logging.info(msg)
            raise MonitorExcption(msg)
        return False

    def logintest(self):
        try:
            logging.info("check longin ...")
            self.__Login()
            self.close()
            return True
        except Exception as e:
            logging.info(logging.exception(e))
            logging.exception(e)
            return False

    def close(self):
        if (self.tn):
            self.tn.write(self.CRTL_AND_C)
            time.sleep(2)
            self.tn.close()
            time.sleep(1)
            self.tn = None


class DspMonitorServer(TelentC):
    def __init__(self, host, port):
        TelentC.__init__(self, host, port, SERVER_BEGINTAG)
        self.core_type_map = {}

    def __webUrlConfig(self, WebMntConfig):
        url = WebMntConfig.get('URL')
        c1 = WebMntConfig.get('c1', 0)
        c2 = WebMntConfig.get('c2', 0)
        return self.run("ClientWebUrlConfig -url {0} -c1 {1} -c2 {2}".format(url, c1, c2))

    def __dspmonitorreset(self):
        cmd = "DspMonitorReset"
        return self.run(cmd)

    def __isSuccess(self, res):
        if (not res):
            return False
        return False if -1 == str(res).find(SUCCESSFLAG) else True

    def __autoGetBoardCfg(self, ):
        cmd = "AutoGetBoardCfg"
        return self.__isSuccess(self.run(cmd))

    def __createboardtype(self, boardname, chipnum):
        cmd = "CreateBoardType -boardname %s -chipnum %s" % (str(boardname), str(chipnum))
        return self.__isSuccess(self.run(cmd))

    def __addchip(self, boardname, chiptype, endian, chipnum, arc):
        if (chiptype not in CHIP_TYPE):
            chips = ",".join(CHIP_TYPE)
            raise MonitorExcption("there is not %s  CHIP_TYPE [%s]" % (chiptype, chips))
        cmd = "AddChip -boardname %s -chiptype %s -endian %s -chipnum %s -arc %s" % (
            str(boardname), str(chiptype), str(endian), str(chipnum), str(arc))
        return self.__isSuccess(self.run(cmd))

    def __updateboardtype(self, boardname):
        cmd = "UpdateBoardType  -boardname  %s" % str(boardname)
        return self.__isSuccess(self.run(cmd))

    # def __createboard(self, boardname):
    #     cmd = "createboard - boardname  %s " % str(boardname)
    #     return self.__isSuccess(self.run(cmd))

    def __createboard(self, boardname, brdidx=None, ccip="", emsip="", ommbip="", aauip=""):
        cmd = "CreateBoard -boardname {boardname}".format(boardname=boardname)
        if ccip:
            cmd += " -ccip %s " % ccip
        if emsip:
            cmd += " -emsip %s " % emsip
        if ommbip:
            cmd += " -ommbip %s " % ommbip
        if aauip:
            cmd += " -aauip %s " % aauip
        if str(brdidx):
            cmd += " -boardid %s " % str(brdidx)
        return self.__isSuccess(self.run(cmd))

    def __setcoreip(self, boardname, boardidx, coreid, coreip, link):
        cmd = "SetCoreIP -boardname {boardname} -boardidx {boardidx} -coreid  {coreid}  -coreip {coreip} -link {link}".format(
            boardname=boardname, boardidx=boardidx, coreid=coreid, coreip=coreip, link=link)
        return self.__isSuccess(self.run(cmd))

    def __UpdateBoard(self, boardname, boardidx):
        cmd = "UpdateBoard  -boardname  %s -boardidx %s " % (str(boardname), str(boardidx))
        return self.__isSuccess(self.run(cmd))

    def __CreateBoardtype(self, boardtype, cores):
        chipnum = sum([coreitem[0] for coreitem in cores])
        result = self.__createboardtype(boardtype, chipnum)
        if (not result):
            raise MonitorExcption("create board %s type fail" % boardtype)

        for coreitem in cores:
            paraCnt = len(coreitem)
            if (paraCnt == 4):
                result = self.__addchip(boardtype, coreitem[1], coreitem[2], coreitem[0], coreitem[3])
            else:
                result = self.__addchip(boardtype, coreitem[1], coreitem[2], coreitem[0], 0)
            if (not result):
                raise MonitorExcption("add core to  board %s type fail,there is not %s " % (boardtype, boardtype))

        result = self.__updateboardtype(boardtype)
        logging.info(result)
        if (not result):
            raise MonitorExcption("update board %s type fail" % boardtype)

    def __Addboard(self, boardtype, brdidx, cores, chip):
        if (0 not in list(chip.keys())):
            raise Exception("no config core0")

        ccip = chip[CCIP] if CCIP in list(chip.keys()) else ""
        emsip = chip[EMSIP] if EMSIP in list(chip.keys()) else ""
        ommbip = chip[OMMBIP] if OMMBIP in list(chip.keys()) else ""
        aauip = chip[AAUIP] if AAUIP in list(chip.keys()) else ""

        result = self.__createboard(boardtype, brdidx, ccip, emsip, ommbip, aauip)
        logging.info(result)
        if (not result):
            raise MonitorExcption("create board %s fail" % boardtype)

        core0 = chip[0]
        if core0[1] != DIRECTSEND:
            raise Exception("core connect type error")

        chipnum = sum([coreitem[0] for coreitem in cores])
        chiptypes = []
        for item in cores:
            chiptypes += [item[1], ] * item[0]
        self.core_type_map[boardtype] = chiptypes
        print(chiptypes)

        for i in range(0, chipnum):
            coreip = chip[i][0] if i in list(chip.keys()) else core0[0]
            link = chip[i][1] if i in list(chip.keys()) else CPU_AGENT  # DEFAULT_CORES_LINKTYPE[chiptypes[i]]
            # logging.info("---" + chiptypes[i] + "  " + str(link))
            result = self.__setcoreip(boardtype, brdidx, i, coreip, link)
            if (not result):
                raise MonitorExcption("set board %s %d core info %s fail" % (boardtype, brdidx, i))

        result = self.__UpdateBoard(boardtype, brdidx)
        if (not result):
            raise MonitorExcption("update board %s %d fail" % (boardtype, brdidx))

    def reset_server(self, cfg):
        if WEBMNT_CONFIG in cfg:
            self.__webUrlConfig(cfg.get(WEBMNT_CONFIG))  # 更新webmnt IP等信息
        self.__dspmonitorreset()

    def InitServer(self, cfg, flag=0):
        self.login()
        self.reset_server(cfg)
        if flag == 1:
            # flag = 0, 手动配置单板；flag = 1, 自动配置单板
            # 自动配置成功，返回；失败就继续走手动配置
            if self.__autoGetBoardCfg():
                return
        for (boardtype, brdcfg) in list(cfg.items()):
            if not isinstance(brdcfg, dict):
                continue
            if boardtype in [MONITOR, WEBMNT_CONFIG]:
                continue

            cores = brdcfg[CORES] if (CORES in list(brdcfg.keys())) else DEFAULT_CORES_CFG
            self.__CreateBoardtype(boardtype, cores)
            chips = brdcfg[BOARDS]
            for brdidx in range(0, len(chips)):
                chip = chips[brdidx]
                self.__Addboard(boardtype, brdidx, cores, chip)
        self.close()


class DspMonitorClient(TelentC):
    def __init__(self, host, port=MONITOR_WEBMNT_CLIENT_PORT):
        TelentC.__init__(self, host, port, CLIENT_BEGINTAG)
        self.ToolType = "webMnt"

    def Login(self):
        res = self.login()
        self.close()
        return res

    def Reset(self):
        cmd = "ClientReset"
        res = self.run(cmd)
        self.close()
        return res

    def Close(self):
        cmd = "ClientClose"
        res = self.run(cmd)
        self.close()
        return res
