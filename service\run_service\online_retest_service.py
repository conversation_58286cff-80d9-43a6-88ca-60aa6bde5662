from infrastructure.logger.logger import logger
from service.logging_service import log_execution
from service.run_service.run_service import Service
import datetime
import asyncio
from domain.repository.es_env import EsJenkinsServerEnv, EsBizCoreEnv, EsPcEnv, EsVersionEnv
from domain.service.jenkins_client import JenkinsClient
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch

from service.tools.UmeService import UmeService


class RemoteTestService(Service):
    def __init__(self, service_type, subtype, state, needed, task_id, env_id, es, cron_task_id):
        super().__init__(service_type, subtype, state, needed, es)
        self.task_id = task_id
        self._es_jenkins_server_info = EsJenkinsServerEnv()
        self._es_biz_core_env = EsBizCoreEnv()
        self.env_id = env_id
        self.job_name = needed.get("job_name", "")
        self.build_number = needed.get("build_number", "")
        self.cron_task_id = cron_task_id
        self._es_version_env = EsVersionEnv()
    def get_jenkins_loginfo(self):
        result = self._es_jenkins_server_info.query_all_without_time()
        print(result)
        return result

    def match_login_info(self):
        result = self._es_biz_core_env.query_by_filter_without_sort({"env_id": self.env_id})
        print(result)
        return result

    def get_jenkins_client(self):
        all_loginfo = self.get_jenkins_loginfo()
        env_biz_info = self.match_login_info()
        try:
            jenkins_url = env_biz_info[1][0].get("jenkins_url", "")
        except:
            self._send_email("线上复测问题失败", f"没有维护{self.env_id}对应的biz_core表信息")
            return
        username = ""
        password = ""
        for loginfo in all_loginfo[1]:
            if loginfo.get("jenkins_url", "") == jenkins_url:
                username = loginfo.get("jenkins_username")
                password = loginfo.get("jenkins_password")
        return JenkinsClient(jenkins_url=jenkins_url, username=username, password=password)


    def execute(self):
        """
        执行线上复测任务并处理结果
        """
        try:
            job_name = self.needed.get("job_name")
            build_number = self.needed.get("build_number")
            if not job_name:
                logger.error("Job name not provided in needed parameters")
                return

            jenkins_client = self.get_jenkins_client()
            job_status = self.check_jenkins_job_status(jenkins_client, job_name, int(build_number))
            logger.info(f"Jenkins job '{job_name}' status: {job_status}")

            # 处理不同的任务状态
            status_handlers = {
                "BUILDING": self._handle_building_status,
                "SUCCESS": self._handle_success_status_new,
                "FAILURE": self._handle_failure_status_new
            }

            # 获取对应的处理函数，如果没有则使用默认处理函数
            handler = status_handlers.get(job_status, self._handle_unknown_status)

            try:
                # 尝试调用处理函数
                handler(job_status)
            except Exception as handler_error:
                # 处理函数执行出错时的异常处理
                logger.error(f"Error handling job status '{job_status}': {str(handler_error)}")
                # 更新任务状态为错误
                trace_info = f"环境{self.env_id}处理任务状态 '{job_status}' 时发生错误: {str(handler_error)}"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
        except Exception as e:
            # 整个执行过程中的异常处理
            logger.error(f"Error executing online retest: {str(e)}")
            try:
                self._send_email(
                    subject="线上复测执行异常",
                    content=f"执行线上复测任务时发生错误: {str(e)}"
                )
            except Exception as email_error:
                logger.error(f"Failed to send error email: {str(email_error)}")

            # 尝试更新任务状态
            try:
                trace_info = f"环境{self.env_id}更新当前错误状态"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
            except Exception as update_error:
                logger.error(f"Failed to update task status: {str(update_error)}")

    def _handle_building_status(self, status):
        """处理构建中状态"""
        logger.info("Job is still building, will check again later")
        return
    def query_fullpath(self,version):
        result = self._es_version_env.query_version_list(self.task_id)
        if result:
            version_list = eval(result[0].get("version_list",[]))
        else:
            self._send_email(
                subject=f"{self.env_id}没有对应的taskid任务",
                content=f"查询version_list失败{self.task_id}"
            )
            return ""
        fulluri = ""
        for version_dict in version_list:
            for versionstr, full_path in version_dict.items():
                if versionstr == version:
                    fulluri+=full_path
        return fulluri

    def _handle_success_status_new(self, status):
        """处理成功状态"""
        logger.info("Job completed successfully")
        if self.subtype == "normal":
            trace_info = f"{self.env_id}线上复测成功"
            self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
            return
        else:
            try:
                version = self.get_bs_version(self.env_id)
                print("_handle_success_status_newversssss", version)
                if not version:
                    raise ValueError("Failed to get base station version - empty response")
                full_path = self.query_fullpath(version)
                if "release" in full_path:
                    #release分支不走此流程
                    return
            except Exception as version_error:
                logger.error(f"Error getting base station version: {str(version_error)}")
                trace_info = f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {self.env_id}"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
                return

            # 更新任务状态和版本执行记录
            try:
                trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status}"
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                self._update_version_execute(
                    task_id=self.task_id,
                    version=version,
                    env_id=self.env_id,
                    status="已完成",
                    result="success"
                )
            except Exception as update_error:
                logger.error(f"Error updating task status or version execute: {str(update_error)}")
                self._send_email(
                    subject="线上复测成功 - 状态更新异常",
                    content=f"更新任务状态或版本执行记录时发生错误: {str(update_error)}\n任务ID: {self.task_id}\n版本: {version}"
                )
                return
            #非首次线上复测回退或者升级到上次成功版本的复测，成功结果，应当升级到当日首次失败的版本
            result = self._es_version_env.query_version_list(self.task_id)
            if result:
                version_list = eval(result[0].get("version_list", []))
            else:
                self._send_email(
                    subject=f"{self.env_id}没有对应的taskid任务",
                    content=f"准备升级到当天版本的时候查询version_list失败{self.task_id}"
                )
                return
            today_version = list(version_list[-1].keys())[0]
            # 获取值
            today_version_full_path = list(version_list[-1].values())[0]
            today_version_full_path = today_version_full_path + "/01-gNB/litepass/PKG/" + today_version + ".tar"
            logger.info(
                f"today_version_full_path:{today_version_full_path}")
            umeres = UmeService().version_upgrade(self.env_id, today_version_full_path)
            print("umeres22222", umeres, umeres.result)
            if not umeres.result:
                # 发送邮件
                self._send_email(
                    subject=f"回溯中发送版本升级失败版本为{today_version}",
                    content=f"发送版本升级失败\n最后测试版本: {version}"
                )
                return
            else:
                self._send_email(
                    subject=f"回溯中发送版本升级成功版本为{today_version}",
                    content=f"发送版本升级成功\n最后测试版本: {version}"
                )
                pipelineStageId = umeres.data.pipelineStageId
                pipelineUuid = umeres.data.pipelineUuid
                recordId = umeres.data.recordId
                self.save_local_test_task(self.task_id, self.env_id, today_version, "version_upgrade",
                                          "update", self.job_name, self.build_number,
                                          pipelineUuid, pipelineStageId, recordId)
                return

    def _handle_success_status(self, status):
        """处理成功状态"""
        logger.info("Job completed successfully")
        if self.subtype == "normal":
            trace_info = f"{self.env_id}线上复测成功"
            self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
            return
        try:
            # 获取当前版本
            try:
                version = self.get_bs_version(self.env_id)
                print("versssss", version)
                if not version:
                    raise ValueError("Failed to get base station version - empty response")
            except Exception as version_error:
                logger.error(f"Error getting base station version: {str(version_error)}")
                trace_info = f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {self.env_id}"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
                return

            # 更新任务状态和版本执行记录
            try:
                trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status}"
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                self._update_version_execute(
                    task_id=self.task_id,
                    version=version,
                    env_id=self.env_id,
                    status="已完成",
                    result="success"
                )
            except Exception as update_error:
                logger.error(f"Error updating task status or version execute: {str(update_error)}")
                self._send_email(
                    subject="线上复测成功 - 状态更新异常",
                    content=f"更新任务状态或版本执行记录时发生错误: {str(update_error)}\n任务ID: {self.task_id}\n版本: {version}"
                )
                return

            # 生成下一个版本
            try:
                next_version = self._general_next_version(self.task_id, "version_upgrade", version)
                print("nextereeeee", next_version)
                if next_version:
                    # 放队列，成功场景，直接升级。
                    # 存放cron_task任务，subtype="online_test",
                    fulluri = self.query_fullpath(next_version)
                    print("fulluri", fulluri)
                    if not fulluri:
                        self._send_email(
                            subject=f"{self.env_id}没有对应fullpath",
                            content=f"发送版本升级失败\n最后测试版本: {version}"
                        )
                        return
                    else:
                        fulluri = fulluri + "/01-gNB/litepass/PKG/" + next_version + ".tar"
                        logger.info(
                            f"fulluri:{fulluri}")
                    umeres = UmeService().version_upgrade(self.env_id, fulluri)
                    print("umeres1111111111", umeres, umeres.result)
                    if not umeres.result:
                        # 发送邮件
                        self._send_email(
                            subject=f"发送版本升级失败版本为{next_version}",
                            content=f"发送版本升级失败\n最后测试版本: {version}"
                        )
                        return
                    else:
                        self._send_email(
                            subject=f"发送版本升级成功版本为{next_version}",
                            content=f"发送版本升级成功\n最后测试版本: {version}"
                        )
                        pipelineStageId = umeres.data.pipelineStageId
                        pipelineUuid = umeres.data.pipelineUuid
                        recordId = umeres.data.recordId
                        self.save_local_test_task(self.task_id, self.env_id, next_version, "version_upgrade",
                                                  "online_test", self.job_name, self.build_number,
                                                  pipelineUuid, pipelineStageId, recordId)
                    logger.info(f"Generated next version for version_upgrade testing: {next_version}")
                else:
                    logger.info("No more versions to test")
                    self._send_email(
                        subject="线上复测完成",
                        content=f"版本回溯已完成\n最后测试版本: {version}"
                    )
            except Exception as next_version_error:
                logger.error(f"Error generating next version: {str(next_version_error)}")
                self._send_email(
                    subject="线上复测失败 - 版本生成异常",
                    content=f"生成下一个测试版本时发生错误: {str(next_version_error)}\n当前版本: {version}"
                )

        except Exception as e:
            logger.error(f"Error in handle_failure_status: {str(e)}")
            trace_info = f"处理失败状态时发生未预期的错误: {str(e)}"
            self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)

    def _handle_failure_status_new(self, status):
        try:
            version = self.get_bs_version(self.env_id)
            print("_handle_failure_status_newveresssssss", version)
            if not version:
                raise ValueError("Failed to get base station version - empty response")
        except Exception as version_error:
            logger.error(f"Error getting base station version: {str(version_error)}")
            trace_info = f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {self.env_id}"
            self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
            return
        # 更新任务状态和版本执行记录
        if self.subtype == "rollback":
            #回退里的线上复测，失败直接结束保留环境
            trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status}，这里是回退任务的onlinetest，失败了，流程结束"
            try:
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                return
            except Exception as update_error:
                logger.error(f"Error updating task status or version execute: {str(update_error)}")
                self._send_email(
                    subject="线上复测失败 - 状态更新异常",
                    content=f"更新任务状态或版本执行记录时发生错误: {str(update_error)}\n任务ID: {self.task_id}\n版本: {version}"
                )
                return
        else:
            full_path = self.query_fullpath(version)
            if "release" in full_path:
                # release分支不走此流程
                trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status},release分支不走回退测试"
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                return
            try:
                trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status},接下来判断备用版本和最近成功版本的关系"
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                self._update_version_execute(
                    task_id=self.task_id,
                    version=version,
                    env_id=self.env_id,
                    status="已完成",
                    result="fail"
                )
            except Exception as update_error:
                logger.error(f"Error updating task status or version execute: {str(update_error)}")
                self._send_email(
                    subject="线上复测失败 - 状态更新异常",
                    content=f"更新任务状态或版本执行记录时发生错误: {str(update_error)}\n任务ID: {self.task_id}\n版本: {version}"
                )
                return
            try:
                backup_version = self.get_bs_backup_version(self.env_id)
                print("_handle_failure_status_new_backupversion", backup_version)
                if not backup_version:
                    raise ValueError("Failed to get base station backup_version - empty response")
            except Exception as backup_version_error:
                logger.error(f"Error getting base station backup_version: {str(backup_version_error)}")
                trace_info = f"获取基站版本时发生错误: {str(backup_version_error)}\n环境ID: {self.env_id}"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
                return
            result = self._es_version_env.query_version_list(self.task_id)
            if result:
                version_list = eval(result[0].get("version_list", []))
            else:
                self._send_email(
                    subject=f"{self.env_id}没有对应的taskid任务",
                    content=f"准备升级到当天版本的时候查询version_list失败{self.task_id}"
                )
                return
            last_success_version = list(version_list[0].keys())[0]
            # 获取值
            compare_result = self.compare_versions(backup_version,last_success_version)
            if compare_result == 0:
                #相等直接回退
                umeres = UmeService().version_rollback(self.env_id, last_success_version + ".tar")
                if not umeres.result:
                    # 发送邮件
                    self._send_email(
                        subject=f"{self.env_id}发送版本回退失败",
                        content=f"发送版本回退失败\n最后测试版本: {version}"
                    )
                    return
                else:
                    self._send_email(
                        subject=f"{self.env_id}发送版本回退成功，回退版本为last_success_version版本为{last_success_version}",
                        content=f"发送版本回退成功\n最后测试版本: {version}"
                    )
                    pipelineStageId = umeres.data.pipelineStageId
                    pipelineUuid = umeres.data.pipelineUuid
                    recordId = umeres.data.recordId
                    self.save_local_test_task(self.task_id, self.env_id, last_success_version, "version_rollback",
                    "online_test", self.job_name, self.build_number,
                    pipelineUuid, pipelineStageId, recordId)
            if compare_result == -1:
                #备份的版本小需要先回退再升级，升级完走线上复测
                umeres = UmeService().version_rollback(self.env_id, version + ".tar")
                if not umeres.result:
                    # 发送邮件
                    self._send_email(
                        subject=f"{self.env_id}发送版本回退失败",
                        content=f"备份版本比最近成功版本要小，发送版本回退失败\n最后测试版本: {version},最近成功版本：{last_success_version},备用版本{backup_version}"
                    )
                    return
                else:
                    self._send_email(
                        subject=f"{self.env_id}发送版本回退成功，next_version版本为{last_success_version}",
                        content=f"备份版本比最近成功版本要小，发送版本回退成功\n最后测试版本: {version},最近成功版本：{last_success_version},备用版本{backup_version}"
                    )
                    pipelineStageId = umeres.data.pipelineStageId
                    pipelineUuid = umeres.data.pipelineUuid
                    recordId = umeres.data.recordId
                    self.save_local_test_task(self.task_id, self.env_id, last_success_version, "version_rollback",
                                              "update", self.job_name, self.build_number,
                                              pipelineUuid, pipelineStageId, recordId)
            if compare_result == 1:
                #备份的版本大直接返回
                self._send_email(
                    subject=f"{self.env_id}回溯流程结束，因为备用版本大于最后成功版本",
                    content=f"发送版本升级成功\n最后测试版本: {version},最近成功版本：{last_success_version},备用版本{backup_version}"
                )
    def _handle_failure_status(self, status):
        """处理失败状态"""
        try:
            logger.error("Job failed")

            # 获取当前版本
            try:
                version = self.get_bs_version(self.env_id)
                print("veresssssss", version)
                if not version:
                    raise ValueError("Failed to get base station version - empty response")
            except Exception as version_error:
                logger.error(f"Error getting base station version: {str(version_error)}")
                trace_info = f"获取基站版本时发生错误: {str(version_error)}\n环境ID: {self.env_id}"
                self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)
                return

            # 更新任务状态和版本执行记录
            try:
                trace_info = f"环境ID: {self.env_id}当前版本：{version},执行状态：{status}"
                self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)
                self._update_version_execute(
                    task_id=self.task_id,
                    version=version,
                    env_id=self.env_id,
                    status="已完成",
                    result="fail"
                )
            except Exception as update_error:
                logger.error(f"Error updating task status or version execute: {str(update_error)}")
                self._send_email(
                    subject="线上复测失败 - 状态更新异常",
                    content=f"更新任务状态或版本执行记录时发生错误: {str(update_error)}\n任务ID: {self.task_id}\n版本: {version}"
                )
                return

            # 生成下一个版本
            try:
                next_version = self._general_next_version(self.task_id, "version_rollback", version)
                if next_version:
                    # 放队列，失败场景，应该先回退再升级。
                    # 触发回退任务
                    # 存放cron_task，subtype类型为"version_upgrade"
                    umeres = UmeService().version_rollback(self.env_id, version + ".tar")
                    if not umeres.result:
                        # 发送邮件
                        self._send_email(
                            subject=f"{self.env_id}发送版本回退失败",
                            content=f"发送版本回退失败\n最后测试版本: {version}"
                        )
                        return
                    else:
                        self._send_email(
                            subject=f"{self.env_id}发送版本回退成功，next_version版本为{next_version}",
                            content=f"发送版本回退成功\n最后测试版本: {version}"
                        )
                        pipelineStageId = umeres.data.pipelineStageId
                        pipelineUuid = umeres.data.pipelineUuid
                        recordId = umeres.data.recordId
                        if self.subtype != "normal":
                            self.save_local_test_task(self.task_id, self.env_id, next_version, "version_rollback",
                                                      "update", self.job_name, self.build_number,
                                                      pipelineUuid, pipelineStageId, recordId)
                        else:
                            self.save_local_test_task(self.task_id, self.env_id, next_version, "version_rollback",
                                                      "online_test", self.job_name, self.build_number,
                                                      pipelineUuid, pipelineStageId, recordId)
                    logger.info(f"Generated next version for version_rollback testing: {next_version}")
                else:
                    logger.info("No more versions to test")
                    self._send_email(
                        subject=f"{self.env_id}线上复测完成",
                        content=f"版本回溯已完成\n最后测试版本: {version}"
                    )
            except Exception as next_version_error:
                logger.error(f"Error generating next version: {str(next_version_error)}")
                self._send_email(
                    subject=f"{self.env_id}线上复测失败 - 版本生成异常",
                    content=f"生成下一个测试版本时发生错误: {str(next_version_error)}\n当前版本: {version}"
                )

        except Exception as e:
            logger.error(f"Error in handle_failure_status: {str(e)}")
            trace_info = f"处理失败状态时发生未预期的错误: {str(e)}"
            self._update_task_status(self.task_id, self.cron_task_id, "ERROR", self.env_id, trace_info)

    def _handle_unknown_status(self, status):
        """处理未知状态"""
        logger.warning(f"Unknown job status: {status}")
        trace_info = f"请分析，当前状态: {status}"
        self._update_task_status(self.task_id, self.cron_task_id, status, self.env_id, trace_info)

    def check_jenkins_job_status(self, jenkins_client: JenkinsClient, job_name: str, build_number):
        # 模拟查询 Jenkins 任务状态
        result = jenkins_client.get_job_info(job_name, build_number)
        return result  # 假设返回的状态
