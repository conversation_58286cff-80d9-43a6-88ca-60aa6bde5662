from _ast import Dict
from collections import defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional, Tuple

from domain.model.dto.env_info import JenkinsServerEnvInfo, PcEnvInfo, VersionEnvInfo
from domain.repository.es_env import EsJenkinsServerEnv, EsPcEnv, EsVersionEnv
from domain.repository.version_execute import EsVersionExecute
from infrastructure.logger.logger import logger
from domain.repository.agents_log import EsAgentsLog
from service.agent_log_aggregation_service import AgentLogAggregationService


class MeasurementReportService:
    def __init__(self):
        self._es = EsVersionEnv()
        self._agents_log = EsAgentsLog()
        self._log_aggregation_service = AgentLogAggregationService()


    def query_all(self):
        result = self._es.query_all_without_time(size=1000)
        print(result)
        for elem in result[1]:
            print(elem)
        print(len(result[1]))

    def get_by_time_range(self, start_time: Optional[str] = None, end_time: Optional[str] = None,
                          from_id: int = 0, size: int = 1000) -> List[Dict]:
        """
        Get environment information within a specific time range

        Args:
            start_time (str, optional): Start time in ISO format (YYYY-MM-DDTHH:MM:SS)
            end_time (str, optional): End time in ISO format (YYYY-MM-DDTHH:MM:SS)
            from_id (int): Starting index for pagination
            size (int): Maximum number of results to return

        Returns:
            List[Dict]: List of environment records within the time range
        """
        # If no time range is specified, default to the last 14 days
        if not start_time:
            now = datetime.now()
            start_time = (now - timedelta(days=14)).isoformat()

        if not end_time:
            end_time = datetime.now().isoformat()

        # Create a range query for create_time
        range_query = {
            "gte": start_time
        }

        if end_time:
            range_query["lte"] = end_time

        query = {
            "query": {
                "range": {
                    "create_time": range_query
                }
            },
            "sort": [
                {"create_time": {"order": "desc"}}
            ],
            "from": from_id,
            "size": size
        }

        # Execute the query
        _, results = self._es.search(body=query)
        print(results)
        for elem in results:
            print(elem)
        print(len(results))
        return results

    def get_failure_counts(self, start_time: Optional[str] = None, end_time: Optional[str] = None) -> Dict[str, Any]:
        """
        计算指定时间范围内的失败统计，按环境分组

        Args:
            start_time (str, optional): 开始时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
            end_time (str, optional): 结束时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)

        Returns:
            Dict[str, Any]: 包含按环境分组的首次失败和累计失败统计
        """
        # 获取时间范围内的所有记录
        records = self.get_by_time_range(start_time, end_time, size=10000)

        if not records:
            return {
                "total": {
                    "total_tests": 0,
                    "first_failures": 0,
                    "total_failures": 0
                },
                "by_env": []
            }

        # 按环境ID分组
        env_records = defaultdict(list)
        for record in records:
            env_id = record.get("env_id")
            if env_id:
                env_records[env_id].append(record)

        # 按版本统计首次失败
        env_stats = []
        total_tests = 0
        total_first_failures = 0
        total_failures = 0

        for env_id, env_data in env_records.items():
            # 统计该环境的测试总数和失败总数
            env_total_tests = len(env_data)
            env_total_failures = sum(1 for record in env_data if record.get("version_test_result") == "False")

            # 按版本分组，找出每个版本的首次测试
            version_first_tests = {}
            for record in env_data:
                version = record.get("curr_version")
                if not version:
                    continue

                timestamp = record.get("create_time", "")
                result = record.get("version_test_result")

                # 如果版本不在字典中，或者当前记录的时间戳更早，则更新
                if version not in version_first_tests or timestamp < version_first_tests[version][0]:
                    version_first_tests[version] = (timestamp, result)

            # 统计首次失败的版本数
            env_first_failures = sum(1 for _, result in version_first_tests.values() if result == "False")

            # 添加到环境统计列表
            env_stats.append({
                "env_id": env_id,
                "total_tests": env_total_tests,
                "first_failures": env_first_failures,
                "total_failures": env_total_failures,
                "versions": len(version_first_tests)
            })

            # 累加到总计
            total_tests += env_total_tests
            total_first_failures += env_first_failures
            total_failures += env_total_failures

        # 按首次失败数降序排序环境统计
        env_stats.sort(key=lambda x: x["first_failures"], reverse=True)

        return {
            "total": {
                "total_tests": total_tests,
                "first_failures": total_first_failures,
                "total_failures": total_failures,
                "environments": len(env_records),
                "versions": sum(env["versions"] for env in env_stats)
            },
            "by_env": env_stats
        }

    def get_failure_counts_new(self, start_time: Optional[str] = None, end_time: Optional[str] = None) -> Dict[str, Any]:
        """
        计算指定时间范围内的失败统计，按环境分组

        Args:
            start_time (str, optional): 开始时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
            end_time (str, optional): 结束时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)

        Returns:
            Dict[str, Any]: 包含按环境分组的首次失败和累计失败统计
        """
        # 获取时间范围内的所有记录
        records = self.get_by_time_range(start_time, end_time, size=10000)
        logger.info(f"Retrieved {len(records)} version_env records")

        # 获取version_execute表中的执行结果
        es_version_execute = EsVersionExecute()
        version_execute_records = self._get_version_execute_records(es_version_execute, start_time, end_time)
        logger.info(f"Retrieved {len(version_execute_records)} version_execute records")
        
        # 合并所有环境ID
        all_env_ids = set()

        # 按环境ID分组version_env记录
        env_records = defaultdict(list)
        for record in records:
            env_id = record.get("env_id")
            if env_id:
                all_env_ids.add(env_id)
                env_records[env_id].append(record)

        # 按环境ID分组version_execute记录
        version_execute_by_env = defaultdict(list)
        for record in version_execute_records:
            env_id = record.get("env_id")
            if env_id:
                all_env_ids.add(env_id)
                version_execute_by_env[env_id].append(record)

        logger.info(f"Found {len(all_env_ids)} unique environments")

        # 按环境和版本统计
        env_stats = []
        total_tests = 0
        total_first_failures = 0
        total_failures = 0
        total_version_tests = 0
        total_version_failures = 0
        total_combined_tests = 0
        total_combined_failures = 0
        total_unique_versions = 0

        for env_id in all_env_ids:
            env_data = env_records[env_id]
            version_execute_data = version_execute_by_env[env_id]
            
            logger.debug(f"Processing env_id: {env_id} with {len(env_data)} version_env records and {len(version_execute_data)} version_execute records")

            # 按版本分组统计version_env表数据
            version_env_stats = {}
            for record in env_data:
                version = record.get("curr_version")
                if not version:
                    continue
                
                if version not in version_env_stats:
                    version_env_stats[version] = {
                        "tests": 0,
                        "failures": 0,
                        "first_test_time": None,
                        "first_test_result": None
                    }
                
                # 记录测试次数和失败次数
                version_env_stats[version]["tests"] += 1
                if record.get("version_test_result") == "False":
                    version_env_stats[version]["failures"] += 1
                
                # 记录首次测试时间和结果
                timestamp = record.get("create_time", "")
                if not version_env_stats[version]["first_test_time"] or timestamp < version_env_stats[version]["first_test_time"]:
                    version_env_stats[version]["first_test_time"] = timestamp
                    version_env_stats[version]["first_test_result"] = record.get("version_test_result")

            # 按版本分组统计version_execute表数据
            version_execute_stats = {}
            for record in version_execute_data:
                version = record.get("version")
                if not version:
                    continue
                
                if version not in version_execute_stats:
                    version_execute_stats[version] = {
                        "tests": 0,
                        "failures": 0
                    }
                
                # 记录测试次数和失败次数
                version_execute_stats[version]["tests"] += 1
                if record.get("test_result") == "fail":
                    version_execute_stats[version]["failures"] += 1

            # 合并两个表中的版本信息
            all_versions = set(version_env_stats.keys()) | set(version_execute_stats.keys())
            
            # 计算环境级别的统计数据
            env_total_tests = sum(stats["tests"] for stats in version_env_stats.values())
            env_total_failures = sum(stats["failures"] for stats in version_env_stats.values())
            env_first_failures = sum(1 for stats in version_env_stats.values() 
                                   if stats["first_test_result"] == "False")
            
            env_version_tests = sum(stats["tests"] for stats in version_execute_stats.values())
            env_version_failures = sum(stats["failures"] for stats in version_execute_stats.values())
            
            env_combined_tests = env_total_tests + env_version_tests
            env_combined_failures = env_total_failures + env_version_failures
            
            logger.debug(f"Env {env_id}: version_env tests={env_total_tests}, failures={env_total_failures}, " +
                        f"first_failures={env_first_failures}, " +
                        f"version_execute tests={env_version_tests}, failures={env_version_failures}, " +
                        f"unique versions={len(all_versions)}")
            
            # 添加到环境统计列表
            env_stats.append({
                "env_id": env_id,
                "total_tests": env_total_tests,
                "first_failures": env_first_failures,
                "total_failures": env_total_failures,
                "versions": len(all_versions),
                "version_tests": env_version_tests,
                "version_failures": env_version_failures,
                "combined_tests": env_combined_tests,
                "combined_failures": env_combined_failures
            })

            # 累加到总计
            total_tests += env_total_tests
            total_first_failures += env_first_failures
            total_failures += env_total_failures
            total_version_tests += env_version_tests
            total_version_failures += env_version_failures
            total_combined_tests += env_combined_tests
            total_combined_failures += env_combined_failures
            total_unique_versions += len(all_versions)

        # 按合并失败数降序排序环境统计
        env_stats.sort(key=lambda x: x["combined_failures"], reverse=True)
        
        logger.info(f"Final stats: {len(env_stats)} environments, {total_combined_tests} combined tests, " +
                   f"{total_combined_failures} combined failures, {total_unique_versions} total versions")

        return {
            "total": {
                "total_tests": total_tests,
                "first_failures": total_first_failures,
                "total_failures": total_failures,
                "environments": len(all_env_ids),
                "versions": total_unique_versions,
                "version_tests": total_version_tests,
                "version_failures": total_version_failures,
                "combined_tests": total_combined_tests,
                "combined_failures": total_combined_failures
            },
            "by_env": env_stats
        }

    def _get_task_stage_stats(self, task_ids: List[str]) -> Dict[str, Dict[str, int]]:
        """
        获取任务停留阶段的统计信息
        
        Args:
            task_ids: 任务ID列表
            
        Returns:
            Dict[str, Dict[str, int]]: 每个任务ID对应的阶段统计
        """
        # 初始化结果字典
        result = {}
        
        # 定义所有可能的阶段
        stages = ["jenkins_job_save", "log_analysis", "env_check", "online_test", "env_backtrack"]
        
        # 获取每个任务的阶段信息
        for task_id in task_ids:
            # 获取任务的日志聚合结果
            aggregated_logs = self._log_aggregation_service.aggregate_by_task_id(task_id)
            
            # 初始化该任务的阶段统计
            task_stages = {f"stop_in_{stage}_count": 0 for stage in stages}
            
            # 确定最后阶段
            last_stage = None
            for stage in stages:
                if stage in aggregated_logs and aggregated_logs[stage]:
                    last_stage = stage
            
            # 如果找到最后阶段，增加相应计数
            if last_stage:
                task_stages[f"stop_in_{last_stage}_count"] = 1
            
            # 保存该任务的阶段统计
            result[task_id] = task_stages
        
        return result

    def get_failure_counts_by_day(self, start_time: Optional[str] = None, end_time: Optional[str] = None) -> Dict[str, Any]:
        """
        计算指定时间范围内每天每个环境的失败统计
        
        Args:
            start_time (str, optional): 开始时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
            end_time (str, optional): 结束时间，ISO格式 (YYYY-MM-DDTHH:MM:SS)
        
        Returns:
            Dict[str, Any]: 包含按日期和环境分组的首次失败和累计失败统计，以及按环境汇总的统计
        """
        # 获取时间范围内的所有记录
        records = self.get_by_time_range(start_time, end_time, size=10000)
        logger.info(f"Retrieved {len(records)} version_env records")
        
        # 获取version_execute表中的执行结果
        es_version_execute = EsVersionExecute()
        version_execute_records = self._get_version_execute_records(es_version_execute, start_time, end_time)
        logger.info(f"Retrieved {len(version_execute_records)} version_execute records")
        
        # 如果没有记录，返回空结果
        if not records and not version_execute_records:
            return {
                "total": {
                    "total_tests": 0,
                    "first_failures": 0,
                    "total_failures": 0,
                    "first_executions": 0,
                    "stop_in_jenkins_job_save_count": 0,
                    "stop_in_log_analysis_count": 0,
                    "stop_in_env_check_count": 0,
                    "stop_in_online_test_count": 0,
                    "stop_in_env_backtrack_count": 0
                },
                "by_day": {},
                "by_env": {}
            }
        
        # 按日期和环境ID分组
        daily_env_records = defaultdict(lambda: defaultdict(list))
        
        # 处理version_env记录
        for record in records:
            env_id = record.get("env_id")
            if not env_id:
                continue
            
            # 提取日期部分 (YYYY-MM-DD)
            create_time = record.get("create_time", "")
            if not create_time:
                continue
            
            date_str = create_time.split("T")[0] if "T" in create_time else create_time.split(" ")[0]
            daily_env_records[date_str][env_id].append(record)
        
        # 处理version_execute记录，去除重复数据
        # 使用(task_id, env_id, version)作为唯一键，只保留每个键的最新记录
        task_env_version_latest = {}
        
        for record in version_execute_records:
            task_id = record.get("task_id")
            env_id = record.get("env_id")
            version = record.get("version")
            create_time = record.get("create_time", "")
            
            if not all([task_id, env_id, version, create_time]):
                continue
            
            key = (task_id, env_id, version)
            
            # 如果是新的键或者比现有记录更新，则更新
            if key not in task_env_version_latest or create_time > task_env_version_latest[key]["create_time"]:
                task_env_version_latest[key] = record
        
        # 将去重后的version_execute记录按日期和环境分组
        daily_version_execute = defaultdict(lambda: defaultdict(list))
        
        for record in task_env_version_latest.values():
            env_id = record.get("env_id")
            create_time = record.get("create_time", "")
            
            if not env_id or not create_time:
                continue
            
            date_str = create_time.split("T")[0] if "T" in create_time else create_time.split(" ")[0]
            daily_version_execute[date_str][env_id].append(record)
        
        # 合并所有日期
        all_dates = sorted(set(daily_env_records.keys()) | set(daily_version_execute.keys()))
        logger.info(f"Found data for {len(all_dates)} days")
        
        # 跟踪全局唯一任务ID
        all_task_ids = set()
        daily_task_ids = defaultdict(set)
        env_task_ids = defaultdict(lambda: defaultdict(set))
        
        # 收集所有任务ID
        for date_str in all_dates:
            for env_id in set(daily_env_records[date_str].keys()) | set(daily_version_execute[date_str].keys()):
                # 从version_env收集任务ID
                for record in daily_env_records[date_str][env_id]:
                    task_id = record.get("task_id")
                    if task_id:
                        all_task_ids.add(task_id)
                        daily_task_ids[date_str].add(task_id)
                        env_task_ids[date_str][env_id].add(task_id)
                
                # 从version_execute收集任务ID
                for record in daily_version_execute[date_str][env_id]:
                    task_id = record.get("task_id")
                    if task_id:
                        all_task_ids.add(task_id)
                        daily_task_ids[date_str].add(task_id)
                        env_task_ids[date_str][env_id].add(task_id)
        
        # 获取所有任务的阶段统计
        logger.info(f"Analyzing stages for {len(all_task_ids)} tasks")
        task_stage_stats = self._get_task_stage_stats(list(all_task_ids))
        
        # 初始化阶段统计计数器
        stages = ["jenkins_job_save", "log_analysis", "env_check", "online_test", "env_backtrack"]
        total_stage_counts = {f"stop_in_{stage}_count": 0 for stage in stages}
        daily_stage_counts = defaultdict(lambda: {f"stop_in_{stage}_count": 0 for stage in stages})
        env_stage_counts = defaultdict(lambda: {f"stop_in_{stage}_count": 0 for stage in stages})
        
        # 按日期统计
        daily_stats = {}
        total_tests = 0
        total_first_failures = 0
        total_failures = 0
        
        for date_str in all_dates:
            # 获取当天的环境记录
            env_records = daily_env_records[date_str]
            version_execute_by_env = daily_version_execute[date_str]
            
            # 合并所有环境ID
            all_env_ids = set(env_records.keys()) | set(version_execute_by_env.keys())
            
            # 按环境统计
            env_stats = []
            day_version_env_tests = 0
            day_first_failures = 0
            day_version_env_failures = 0
            day_version_execute_tests = 0
            day_version_execute_failures = 0
            day_total_tests = 0
            day_total_failures = 0
            
            # 当天的阶段统计
            day_stage_counts = {f"stop_in_{stage}_count": 0 for stage in stages}
            
            # 统计当天任务的阶段
            for task_id in daily_task_ids[date_str]:
                if task_id in task_stage_stats:
                    for stage in stages:
                        stage_key = f"stop_in_{stage}_count"
                        day_stage_counts[stage_key] += task_stage_stats[task_id][stage_key]
                        total_stage_counts[stage_key] += task_stage_stats[task_id][stage_key]
            
            # 更新每日阶段统计
            daily_stage_counts[date_str] = day_stage_counts
            
            for env_id in all_env_ids:
                env_data = env_records[env_id]
                version_execute_data = version_execute_by_env[env_id]
                
                # 按版本分组统计version_env表数据
                version_env_stats = {}
                for record in env_data:
                    version = record.get("curr_version")
                    if not version:
                        continue
                    
                    if version not in version_env_stats:
                        version_env_stats[version] = {
                            "tests": 0,
                            "failures": 0,
                            "first_test_time": None,
                            "first_test_result": None
                        }
                    
                    # 记录测试次数和失败次数
                    version_env_stats[version]["tests"] += 1
                    if record.get("version_test_result") == "False":
                        version_env_stats[version]["failures"] += 1
                    
                    # 记录首次测试时间和结果
                    timestamp = record.get("create_time", "")
                    if not version_env_stats[version]["first_test_time"] or timestamp < version_env_stats[version]["first_test_time"]:
                        version_env_stats[version]["first_test_time"] = timestamp
                        version_env_stats[version]["first_test_result"] = record.get("version_test_result")
                
                # 按版本分组统计version_execute表数据
                version_execute_stats = {}
                for record in version_execute_data:
                    version = record.get("version")
                    if not version:
                        continue
                    
                    if version not in version_execute_stats:
                        version_execute_stats[version] = {
                            "tests": 0,
                            "failures": 0
                        }
                    
                    # 记录测试次数和失败次数
                    version_execute_stats[version]["tests"] += 1
                    if record.get("test_result") == "fail":
                        version_execute_stats[version]["failures"] += 1
                
                # 合并两个表中的版本信息
                all_versions = set(version_env_stats.keys()) | set(version_execute_stats.keys())
                
                # 计算环境级别的统计数据
                version_env_tests = sum(stats["tests"] for stats in version_env_stats.values())
                version_env_failures = sum(stats["failures"] for stats in version_env_stats.values())
                
                # 修改这里：按task_id统计首次失败
                # 创建一个字典来跟踪每个task_id的首次测试结果
                task_first_tests = {}
                
                # 从version_env记录中获取每个task_id的首次测试
                for record in env_data:
                    task_id = record.get("task_id")
                    if not task_id:
                        continue
                    
                    timestamp = record.get("create_time", "")
                    result = record.get("version_test_result")
                    
                    # 如果task_id不在字典中，或者当前记录的时间戳更早，则更新
                    if task_id not in task_first_tests or timestamp < task_first_tests[task_id][0]:
                        task_first_tests[task_id] = (timestamp, result)
                
                # 从version_execute记录中获取每个task_id的首次测试
                for record in version_execute_data:
                    task_id = record.get("task_id")
                    if not task_id:
                        continue
                    
                    timestamp = record.get("create_time", "")
                    result = "False" if record.get("test_result") == "fail" else "True"
                    
                    # 如果task_id不在字典中，或者当前记录的时间戳更早，则更新
                    if task_id not in task_first_tests or timestamp < task_first_tests[task_id][0]:
                        task_first_tests[task_id] = (timestamp, result)

                # 统计首次失败的task_id数
                first_failures = sum(1 for _, result in task_first_tests.values() if result == "False")

                version_execute_tests = sum(stats["tests"] for stats in version_execute_stats.values())
                version_execute_failures = sum(stats["failures"] for stats in version_execute_stats.values())
                
                total_tests_for_env = version_env_tests + version_execute_tests
                total_failures_for_env = version_env_failures + version_execute_failures
                
                # 获取该环境的唯一任务数
                first_executions_for_env = len(env_task_ids[date_str][env_id])
                
                # 环境的阶段统计
                env_day_stage_counts = {f"stop_in_{stage}_count": 0 for stage in stages}
                
                # 统计该环境当天任务的阶段
                for task_id in env_task_ids[date_str][env_id]:
                    if task_id in task_stage_stats:
                        for stage in stages:
                            stage_key = f"stop_in_{stage}_count"
                            env_day_stage_counts[stage_key] += task_stage_stats[task_id][stage_key]
                            env_stage_counts[env_id][stage_key] += task_stage_stats[task_id][stage_key]
                
                # 只有当有测试数据时才添加到环境统计列表
                if total_tests_for_env > 0:
                    env_stats.append({
                        "env_id": env_id,
                        "version_env_tests": version_env_tests,  # version_env表的测试数
                        "first_failures": first_failures,  # 修改为按task_id统计的首次失败数
                        "version_env_failures": version_env_failures,  # version_env表的失败数
                        "versions": len(all_versions),
                        "version_execute_tests": version_execute_tests,
                        "version_execute_failures": version_execute_failures,
                        "total_tests": total_tests_for_env,  # 合并后的总测试数
                        "total_failures": total_failures_for_env,  # 合并后的总失败数
                        "first_executions": first_executions_for_env,  # 该环境的首次执行数
                        **env_day_stage_counts  # 添加阶段统计
                    })
                    
                    # 累加到当天总计
                    day_version_env_tests += version_env_tests
                    day_first_failures += first_failures  # 修改为按task_id统计的首次失败数
                    day_version_env_failures += version_env_failures
                    day_version_execute_tests += version_execute_tests
                    day_version_execute_failures += version_execute_failures
                    day_total_tests += total_tests_for_env
                    day_total_failures += total_failures_for_env
        
            # 按总失败数降序排序环境统计
            env_stats.sort(key=lambda x: x["total_failures"], reverse=True)
            
            # 只有当有测试数据时才添加到日期统计
            if day_total_tests > 0:
                daily_stats[date_str] = {
                    "total": {
                        "version_env_tests": day_version_env_tests,  # version_env表的测试数
                        "first_failures": day_first_failures,
                        "version_env_failures": day_version_env_failures,  # version_env表的失败数
                        "environments": len(all_env_ids),
                        "version_execute_tests": day_version_execute_tests,
                        "version_execute_failures": day_version_execute_failures,
                        "total_tests": day_total_tests,  # 合并后的总测试数
                        "total_failures": day_total_failures,  # 合并后的总失败数
                        "first_executions": len(daily_task_ids[date_str]),  # 新增字段：当天的首次执行数
                        **day_stage_counts  # 添加阶段统计
                    },
                    "by_env": env_stats
                }
                
                # 累加到总计
                total_tests += day_total_tests
                total_first_failures += day_first_failures
                total_failures += day_total_failures
        
        # 按环境ID汇总统计
        env_stats = defaultdict(lambda: {
            "version_env_tests": 0,
            "first_failures": 0,
            "version_env_failures": 0,
            "versions": set(),
            "version_execute_tests": 0,
            "version_execute_failures": 0,
            "total_tests": 0,
            "total_failures": 0,
            "first_executions": 0,
            "days": set()
        })
        
        # 从daily_stats中提取每个环境的统计数据
        for date_str, day_data in daily_stats.items():
            for env_data in day_data.get("by_env", []):
                env_id = env_data.get("env_id")
                if not env_id:
                    continue
                
                # 累加统计数据
                env_stats[env_id]["version_env_tests"] += env_data.get("version_env_tests", 0)
                env_stats[env_id]["first_failures"] += env_data.get("first_failures", 0)
                env_stats[env_id]["version_env_failures"] += env_data.get("version_env_failures", 0)
                env_stats[env_id]["version_execute_tests"] += env_data.get("version_execute_tests", 0)
                env_stats[env_id]["version_execute_failures"] += env_data.get("version_execute_failures", 0)
                env_stats[env_id]["total_tests"] += env_data.get("total_tests", 0)
                env_stats[env_id]["total_failures"] += env_data.get("total_failures", 0)
                env_stats[env_id]["first_executions"] += env_data.get("first_executions", 0)
                env_stats[env_id]["days"].add(date_str)
                
                # 收集版本信息
                if "versions" in env_data and isinstance(env_data["versions"], int):
                    # 如果versions是数量而不是集合，我们只能累加
                    if isinstance(env_stats[env_id]["versions"], set):
                        # 如果当前是集合，转换为整数
                        env_stats[env_id]["versions"] = len(env_stats[env_id]["versions"]) + env_data["versions"]
                    else:
                        # 如果当前已经是整数，直接累加
                        env_stats[env_id]["versions"] += env_data["versions"]
        
        # 转换为列表并按总失败数排序
        env_summary = []
        for env_id, stats in env_stats.items():
            env_summary.append({
                "env_id": env_id,
                "version_env_tests": stats["version_env_tests"],
                "first_failures": stats["first_failures"],
                "version_env_failures": stats["version_env_failures"],
                "versions": len(stats["versions"]) if isinstance(stats["versions"], set) else stats["versions"],
                "version_execute_tests": stats["version_execute_tests"],
                "version_execute_failures": stats["version_execute_failures"],
                "total_tests": stats["total_tests"],
                "total_failures": stats["total_failures"],
                "first_executions": stats["first_executions"],
                "days": len(stats["days"]),
                **env_stage_counts[env_id],  # 添加阶段统计
                "by_day": {}  # 将为每个环境添加按天的数据
            })
        
        # 按总失败数降序排序
        env_summary.sort(key=lambda x: x["total_failures"], reverse=True)
        
        # 为每个环境添加按天的数据
        for date_str, day_data in daily_stats.items():
            for env_data in day_data.get("by_env", []):
                env_id = env_data.get("env_id")
                if not env_id:
                    continue
                
                # 找到对应的环境摘要
                for env_summary_item in env_summary:
                    if env_summary_item["env_id"] == env_id:
                        if date_str not in env_summary_item["by_day"]:
                            env_summary_item["by_day"][date_str] = {}
                        
                        # 复制当天该环境的数据
                        env_summary_item["by_day"][date_str] = env_data
                        break
        
        # 构建最终返回结果
        result = {
            "total": {
                "total_tests": total_tests,
                "first_failures": total_first_failures,
                "total_failures": total_failures,
                "days": len(daily_stats),
                "version_env_tests": sum(daily_stats[date]["total"]["version_env_tests"] for date in daily_stats),
                "version_env_failures": sum(daily_stats[date]["total"]["version_env_failures"] for date in daily_stats),
                "version_execute_tests": sum(daily_stats[date]["total"]["version_execute_tests"] for date in daily_stats),
                "version_execute_failures": sum(daily_stats[date]["total"]["version_execute_failures"] for date in daily_stats),
                "first_executions": len(all_task_ids),
                **total_stage_counts  # 添加阶段统计
            },
            "by_day": daily_stats,
            "by_env": {env_item["env_id"]: env_item for env_item in env_summary}
        }
        
        logger.info(f"Final stats: {len(result['by_env'])} environments, {total_tests} total tests, " +
                   f"{total_failures} total failures across {len(daily_stats)} days")
        
        return result

    def _get_version_execute_records(self, es_version_execute, start_time=None, end_time=None):
        """
        从version_execute表获取版本执行记录

        Args:
            es_version_execute: EsVersionExecute实例
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            List: 版本执行记录列表
        """
        try:
            # 构建查询条件
            query = {}
            if start_time or end_time:
                time_filter = {}
                if start_time:
                    time_filter["gte"] = start_time
                if end_time:
                    time_filter["lte"] = end_time
                if time_filter:
                    query["create_time"] = time_filter

            # 查询version_execute表
            result = es_version_execute.query_by_filter_with_sort(query, "create_time", size=10000)
            if not result or len(result) < 2 or not result[1]:
                logger.warning("No version_execute records found")
                return []

            records = result[1]
            logger.info(f"Retrieved {len(records)} version_execute records")
            return records

        except Exception as e:
            logger.error(f"Error getting version execute records: {str(e)}")
            return []
if __name__  == "__main__":
    ms = MeasurementReportService()
    # ms.query_all()
    # ms.get_by_time_range()
    # results = ms.get_failure_counts()
    # print(results)
    # result = ms.get_failure_counts_new()
    result = ms.get_failure_counts_by_day()
    print(result)
