#!/bin/bash
#更新apt源为公司源
main(){
    echo original parameter=[$1]
    echo $#
    if [ $# -eq 0 ]
    then
        echo Terminating...
        exit 1
    fi
    ARGS=`getopt -o xa --long arm64,x86-64 -n "$0" -- "$1"`
    if [ $? -ne 0]
    then
        echo "Terminating..."
        exit 1
    fi

    echo ARGS=[$ARGS]
    eval set -- "$ARGS"
    echo formatted parameters=[$@]

    while true
    do
        case $1 in
        -x|--x86-64)
            echo "update x86-64 debian apt source"
            update_x86_64_apt_source
            break
            ;;
        -a|--arm64)
            echo "update arm64 ubuntu apt source"
            update_arm64_apt_source
            break
            ;;
        *)
            echo "Internal error"
            exit 1
            ;;
        esac
    done
}

update_x86_64_apt_source(){
    echo "[Info] 正在备份默认apt源..."
    cp /etc/apt/sources.list /etc/apt/sources.list.bak
    echo "[Info] 正在替换apt源为中兴apt源..."
    echo deb http://mirrors.zte.com.cn/debian buster main contrib non-free > /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/debian buster-updates main contrib non-free  >> /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/debian-security/ buster/updates main contrib non-free  >> /etc/apt/sources.list
    echo "[Info] 正在更新源..."
    apt update
    echo "[Info] 正在更新软件..."
    apt upgrade -y
}

update_arm64_apt_source(){
    echo "[Info] 正在备份默认apt源..."
    cp /etc/apt/sources.list /etc/apt/sources.list.bak
    echo "[Info] 正在替换apt源为中兴apt源..."
    echo deb http://mirrors.zte.com.cn/ubuntu-ports/ focal main multiverse restricted universe > /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/ubuntu-ports/ focal-backports main multiverse restricted universe  >> /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/ubuntu-ports/ focal-proposed main multiverse restricted universe  >> /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/ubuntu-ports/ focal-security main multiverse restricted universe  >> /etc/apt/sources.list
    echo deb http://mirrors.zte.com.cn/ubuntu-ports/ focal-updates main multiverse restricted universe  >> /etc/apt/sources.list
    echo "[Info] 正在更新源..."
    apt update
    echo "[Info] 正在更新软件..."
    apt upgrade -y
}

main $@
