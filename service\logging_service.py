import logging
from domain.repository.agents_log import EsAgentsLog
import traceback
from functools import wraps
import inspect
from infrastructure.utils.json2 import marshal


class LoggingService:
    """
    Service for logging execution results to both standard logging and Elasticsearch
    """

    @staticmethod
    def log_execution(service_type, operation, status, details=None, task_id=None, env_id=None, service_key=None):
        """
        Log an execution event to both standard logging and Elasticsearch

        Args:
            service_type (str): Type of service (e.g., 'jen<PERSON>_job_save', 'log_analysis')
            operation (str): Operation being performed
            status (str): Status of the operation ('success', 'failure', 'in_progress')
            details (dict, optional): Additional details about the operation
            task_id (str, optional): Associated task ID
            env_id (str, optional): Associated environment ID
            service_key (str, optional): The key from AGENTS_SERVICE_OBJECT
        """
        # Log to standard logging
        log_message = f"Service: {service_type}, Operation: {operation}, Status: {status}"
        if task_id:
            log_message += f", Task ID: {task_id}"
        if env_id:
            log_message += f", Env ID: {env_id}"
        if service_key:
            log_message += f", Service Key: {service_key}"

        if status == "success":
            logging.info(log_message)
        elif status == "failure":
            logging.error(log_message)
            if details and "error" in details:
                logging.error(f"Error details: {details['error']}")
        else:
            logging.info(log_message)

        # Log to Elasticsearch
        try:
            EsAgentsLog().log_execution(
                service_type=service_type,
                operation=operation,
                status=status,
                details=details,
                task_id=task_id,
                env_id=env_id,
                service_key=service_key
            )
        except Exception as e:
            logging.error(f"Failed to log to Elasticsearch: {str(e)}")
            logging.error(traceback.format_exc())


def log_execution(operation=None):
    """
    Decorator to log execution of a method

    Args:
        operation (str, optional): Name of the operation. If not provided, the method name will be used.
    """

    def decorator(func):
        @wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            details = eval(marshal(getattr(self, "jenkins_job", {})))
            # Get service type from the instance
            service_type = getattr(self, "service_type", self.__class__.__name__)

            # Get task_id and env_id if available
            task_id = getattr(self, "task_id", None)
            env_id = getattr(self, "env_id", None)

            # Get operation name
            op_name = operation or func.__name__

            # Get service_key if available
            service_key = getattr(self, "service_key", op_name)

            # Log start of execution
            LoggingService.log_execution(
                service_type=service_type,
                operation=op_name,
                status="in_progress",
                task_id=task_id,
                env_id=env_id,
                details={"message": details},
                service_key=service_key
            )

            try:
                # Execute the function
                result = await func(self, *args, **kwargs)

                # Log successful execution
                LoggingService.log_execution(
                    service_type="log_save",
                    operation="log_save",
                    status="success",
                    details={"message": details},
                    task_id=task_id,
                    env_id=env_id,
                    service_key=service_key
                )

                return result
            except Exception as e:
                # Log failed execution
                LoggingService.log_execution(
                    service_type="log_save",
                    operation="log_save",
                    status="failure",
                    details={"error": str(e), "traceback": traceback.format_exc(), "message": details},
                    task_id=task_id,
                    env_id=env_id,
                    service_key=service_key
                )

                # Re-raise the exception
                raise

        @wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            details = eval(marshal(getattr(self, "jenkins_job", {})))
            # Get service type from the instance
            service_type = getattr(self, "service_type", self.__class__.__name__)

            # Get task_id and env_id if available
            task_id = getattr(self, "task_id", None)
            env_id = getattr(self, "env_id", None)

            # Get operation name
            op_name = operation or func.__name__

            # Get service_key if available
            service_key = getattr(self, "service_key", op_name)

            # Log start of execution
            LoggingService.log_execution(
                service_type=service_type,
                operation=op_name,
                status="in_progress",
                task_id=task_id,
                env_id=env_id,
                details={"message": details},
                service_key=service_key
            )

            try:
                # Execute the function
                result = func(self, *args, **kwargs)

                # Log successful execution
                LoggingService.log_execution(
                    service_type="log_save",
                    operation="log_save",
                    status="success",
                    details={"message": details},
                    task_id=task_id,
                    env_id=env_id,
                    service_key=service_key
                )

                return result
            except Exception as e:
                # Log failed execution
                LoggingService.log_execution(
                    service_type="log_save",
                    operation="log_save",
                    status="failure",
                    details={"error": str(e), "traceback": traceback.format_exc(), "message": details},
                    task_id=task_id,
                    env_id=env_id,
                    service_key=service_key
                )

                # Re-raise the exception
                raise

        # Check if the function is async or not
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
