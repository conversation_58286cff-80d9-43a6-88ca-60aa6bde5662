import asyncio
import time
import threading
import pytz
import sys
from infrastructure.logger.logger import logger
from datetime import date, datetime, timedelta

from zoneinfo import ZoneInfo

beijing_tz = ZoneInfo('Asia/Shanghai')


# 只能对异步函数使用
def timeout(seconds):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            async def target():
                return await func(*args, **kwargs)

            try:
                # asyncio.wait_for 设置超时时间
                res = await asyncio.wait_for(target(), timeout=seconds)
                return res
            except asyncio.TimeoutError:
                # 超时异常
                raise TimeoutError(f"function timed out: {seconds} s")

        return wrapper

    return decorator


def convert_to_unix_time(timeInput):
    temp = time.strptime(timeInput, '%Y-%m-%d %H:%M:%S')
    s = time.mktime(temp)
    t = int(s) * 1000
    return str(t)


def bj_time_now():
    return datetime.now(beijing_tz)


def get_current_date():
    return time.strftime("%Y-%m-%d", time.localtime())


def get_last_day_date():
    return (date.today() - timedelta(days=1)).strftime('%Y-%m-%d')


def format_time(timestamp):
    timeArray = time.localtime(int(timestamp) / 1000)
    return time.strftime("%Y-%m-%d %H:%M:%S", timeArray)


def calc_run_time():
    def calc_time_decorator(func):
        def _(*args, **kwargs):
            startTime = time.clock()
            result = func(*args, **kwargs)
            currentTime = time.clock()
            costTime = currentTime - startTime
            return result, costTime

        _.__name__ = func.__name__
        _.__doc__ = func.__doc__
        return _

    return calc_time_decorator


def get_timestamp():
    return str(int(time.time()))


def get_datetime_stamp():
    return bj_time_now().strftime("%Y%m%d%H%M%S%f")[:-3]


def get_format_datetime_stamp():
    return bj_time_now().strftime("%Y-%m-%d %H:%M:%S")


def format_bj_datetime_to_utc_str(bjTime: datetime) -> str:
    utcTime = bjTime.astimezone(pytz.utc)
    return utcTime.strftime("%Y-%m-%dT%H:%M:%S.000Z")


def format_to_zero_time(startTime=None, endTime=None, collectionTime=60, timedelay=8):
    if startTime is None or endTime is None or startTime > endTime:
        endTime = datetime.now() - timedelta(hours=timedelay)
        startTime = endTime - timedelta(minutes=collectionTime)
    return startTime.strftime("%Y-%m-%dT%H:%M:%S.000Z"), endTime.strftime("%Y-%m-%dT%H:%M:%S.000Z")

def format_startEndTime(startTime=None, endTime=None, collectionTime=60, timedelay=8):
    if startTime is None or endTime is None or startTime > endTime:
        startTime = datetime.now() - timedelta(hours=timedelay)
        endTime = startTime + timedelta(minutes=collectionTime)
    return startTime.strftime("%Y-%m-%dT%H:%M:%S.000Z"), endTime.strftime("%Y-%m-%dT%H:%M:%S.000Z")

def time_limit(seconds):
    def timeout_decorator(func):
        def _new_func(oldfunc, result, oldfunc_args, oldfunc_kwargs):
            result.append(oldfunc(*oldfunc_args, **oldfunc_kwargs))


        def _(*args, **kwargs):
            result = []
            # create new args for _new_func, because we want to get the func return val to result list
            new_kwargs = {'oldfunc': func, 'result': result,
                          'oldfunc_args': args, 'oldfunc_kwargs': kwargs}

            thd = KThread(target=_new_func, args=(), kwargs=new_kwargs)

            thd.setDaemon(True)
            thd.start()
            thd.join(seconds)
            alive = thd.is_alive()

            if alive:
                m = u'function run too long, timeout %d seconds.' % seconds
                logger.error(m)
                thd.kill()
                raise Exception(m)
            else:
                thd.kill()
                return result[0]
        _.__name__ = func.__name__
        _.__doc__ = func.__doc__
        return _

    return timeout_decorator

class KThread(threading.Thread):

    """A subclass of threading.Thread, with a kill()
    method.
    Come from:
    Kill a thread in Python:
    http://mail.python.org/pipermail/python-list/2004-May/260937.html
    """

    def __init__(self, *args, **kwargs):
        threading.Thread.__init__(self, *args, **kwargs)
        self.killed = False

    def start(self):
        """Start the thread."""
        self.__run_backup = self.run
        self.run = self.__run  # Force the Thread to install our trace.
        threading.Thread.start(self)

    def __run(self):
        """Hacked run function, which installs the
        trace."""
        sys.settrace(self.globaltrace)
        self.__run_backup()
        self.run = self.__run_backup

    def globaltrace(self, frame, why, arg):
        if why == 'call':
            return self.localtrace
        else:
            return None

    def localtrace(self, frame, why, arg):
        if self.killed:
            if why == 'line':
                raise SystemExit()
        return self.localtrace

    def kill(self):
        self.killed = True
