# Queue Logging Refactor

## Overview

This document describes the refactoring of the logging code in the `CiAgentsQueue` class to improve code organization, maintainability, and to capture the result of service execution in the logs.

## Changes Made

1. **Created a `QueueLogger` Helper Class**:
   - Extracted all logging logic into a dedicated helper class
   - Organized logging methods by operation type (listen, process, push)
   - Added type hints for better code readability and IDE support
   - Improved documentation with detailed docstrings

2. **Enhanced the `process_message` Operation**:
   - Modified the code to capture and log the result of `service_obj.run()`
   - Added the result to the log details as `details.result`
   - This ensures that any return value from a service's run method is properly recorded

3. **Improved Error Handling**:
   - Added better initialization of variables to prevent reference errors
   - Enhanced error logging with more context
   - Made error handling more consistent across different operations

4. **Reduced Code Duplication**:
   - Eliminated repeated logging code patterns
   - Centralized logging logic in the helper class
   - Made the code more DRY (Don't Repeat Yourself)

## Benefits

1. **Improved Maintainability**:
   - Easier to update logging logic in one place
   - Clearer separation of concerns
   - More consistent logging across different operations

2. **Better Debugging**:
   - Service execution results are now captured in logs
   - More detailed error information
   - Consistent logging format

3. **Enhanced Code Quality**:
   - Reduced code duplication
   - Better type hints and documentation
   - More robust error handling

## Example Usage

Before:
```python
# Log successful processing
LoggingService.log_execution(
    service_type="CiAgentsQueue",
    operation="process_message",
    status="success",
    details={
        "service_type": service_msg_dict.get('service_type'),
        "task_id": service_msg_dict.get('task_id'),
        "env_id": service_msg_dict.get('env_id')
    },
    task_id=service_msg_dict.get('task_id'),
    env_id=service_msg_dict.get('env_id'),
    service_key=service_msg_dict.get('service_type')
)
```

After:
```python
# Run the service and capture the result
result = await service_obj.run()

# Log successful processing with the result
self.logger.log_process_success(service_msg_dict, result)
```

## Implementation Details

### QueueLogger Class

The `QueueLogger` class provides static methods for different logging scenarios:

- `log_listen_start`: Logs the start of the listening process
- `log_process_start`: Logs the start of message processing
- `log_process_failure`: Logs a failure in message processing
- `log_process_success`: Logs successful message processing, including the result
- `log_exception`: Logs an exception that occurred during processing
- `log_push_start`: Logs the start of pushing a message to the queue
- `log_push_success`: Logs successful pushing of a message
- `log_push_failure`: Logs a failure in pushing a message

### CiAgentsQueue Class

The `CiAgentsQueue` class now uses the `QueueLogger` helper:

```python
def __init__(self):
    RedisDB.__init__(self)
    self.queue = Env.get_config().REDIS.ci_agent_queue
    self.logger = QueueLogger
```

This allows for cleaner code in the methods:

```python
# Log the start of the listening process
self.logger.log_listen_start(self.queue)

# Log the received message
self.logger.log_process_start(service_msg_dict)

# Run the service and capture the result
result = await service_obj.run()

# Log successful processing with the result
self.logger.log_process_success(service_msg_dict, result)
```
