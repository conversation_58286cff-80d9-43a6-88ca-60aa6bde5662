#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
版本二分查找服务集成测试

该模块测试版本二分查找服务的实际功能，与真实数据库交互。
"""

import unittest
import sys
import os
import time
from datetime import datetime
import uuid

from service.run_service.online_retest_service import RemoteTestService

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from service.version_binary_search_service import VersionBinarySearchService
from service.env_service import VersionEnvService
from domain.repository.es_env import EsVersionEnv
from domain.repository.version_execute import EsVersionExecute
from domain.model.dto.env_info import VersionEnvInfo
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from domain.model.dto.test_record import VersionExecuteInfo
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from infrastructure.logger.logger import logger
from infrastructure.utils.Env import Env
from domain.repository.cron_task import EsCronTask
from typing import Dict


class TestVersionBinarySearchServiceIntegration(unittest.TestCase):
    """测试版本二分查找服务的集成功能"""

    @classmethod
    def setUpClass(cls):
        """测试类初始化，设置ES连接"""
        try:
            # 初始化ES连接
            Document.esConn = Elasticsearch(
                hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                maxsize=1000,
                http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                sniff_on_start=False, 
                sniff_on_connection_fail=False, 
                retry_on_timeout=True,
                max_retries=2, 
                timeout=30, 
                sniff_timeout=60
            )
            
            # 创建测试数据
            cls.task_id = f"test-task-{int(time.time())}"
            cls.env_id = "RAN3-上海高频CI团队-VAT1014"
            cls.create_test_data(cls.task_id, cls.env_id)
            
            # 创建服务实例
            cls.service = VersionBinarySearchService(task_id=cls.task_id, env_id=cls.env_id)
            
            # 创建其他需要的服务实例
            cls.version_env_service = VersionEnvService()
            cls.es_version_execute = EsVersionExecute()
            
        except Exception as e:
            logger.error(f"测试初始化失败: {str(e)}")
            raise

    @classmethod
    def tearDownClass(cls):
        """测试类清理，删除测试数据"""
        try:
            # 清理测试数据
            cls.cleanup_test_data(cls.task_id)
        except Exception as e:
            logger.error(f"测试清理失败: {str(e)}")

    def setUp(self):
        """测试前准备工作"""
        try:
            # 生成唯一的task_id
            self.base_task_id = f"test_task_{uuid.uuid4().hex[:8]}"
            self.env_id = "test_env_001"
            logger.info(f"初始化测试: task_id={self.base_task_id}, env_id={self.env_id}")
            
        except Exception as e:
            logger.error(f"测试初始化失败: {str(e)}")
            raise

    def create_test_data_for_case(self, case_name, version_list, version_task_dict):
        """为每个测试用例创建独立的测试数据
        
        Args:
            case_name: 测试用例名称，用于生成唯一task_id
            version_list: 版本列表
            version_task_dict: 版本任务字典
        """
        task_id = f"{self.base_task_id}_{case_name}"
        
        # 创建版本环境信息
        version_env_info = VersionEnvInfo(
            task_id=task_id,
            env_id=self.env_id,
            full_path="https://artsz.zte.com.cn/artifactory/...",
            last_success_version=version_list[0] if version_list else "",
            curr_version=version_list[-1] if version_list else "",
            jenkins_job_name="test-jenkins-job",
            jenkins_build_number="123",
            version_branch="V4.20.20.20_Main",
            version_test_result="False",
            version_list=str(version_list)
        )
        
        # 保存版本环境信息到ES
        es_version_env = EsVersionEnv()
        env_result = es_version_env.index(
            id=datetime.now().isoformat(), 
            body=eval(str(version_env_info.model_dump()))
        )
        
        # 创建版本执行信息
        version_execute_info = VersionExecuteInfo(
            task_id=task_id,
            env_id=self.env_id,
            version=version_list[-1] if version_list else "",
            test_result="fail",
            version_task_dict=str(version_task_dict)
        )
        
        # 保存版本执行记录到ES
        es_version_execute = EsVersionExecute()
        execute_result = es_version_execute.save_version_execute_task(version_execute_info)
        
        logger.info(f"测试数据创建结果: task_id={task_id}, 环境信息={env_result}, 执行记录={execute_result}")
        return task_id

    @classmethod
    def create_test_data(cls, task_id, env_id):
        """创建测试数据"""
        logger.info(f"创建测试数据: task_id={task_id}, env_id={env_id}")
        
        # 创建测试版本列表
        version_list = [
            "NR-V4.20.20.20MainR119_2504201842",
            "NR-V4.20.20.20MainR120_2504211530",
            "NR-V4.20.20.20MainR121_2504220945",
            "NR-V4.20.20.20MainR122_2504231210",
            "NR-V4.20.20.20MainR122_2504232254"
        ]
        
        # 创建版本环境信息
        version_env_info = VersionEnvInfo(
            task_id=task_id,
            env_id=env_id,
            full_path="https://artsz.zte.com.cn/artifactory/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Main/NR-V4.20.20.20MainR122/NR-V4.20.20.20MainR122_2504232254",
            last_success_version="NR-V4.20.20.20MainR119_2504201842",
            curr_version="NR-V4.20.20.20MainR122_2504232254",
            jenkins_job_name="test-jenkins-job",
            jenkins_build_number="123",
            version_branch="V4.20.20.20_Main",
            version_test_result="False",
            version_list=str(version_list)
        )
        
        # 保存版本环境信息到ES
        es_version_env = EsVersionEnv()
        env_result = es_version_env.index(id=datetime.now().isoformat(), body=eval(str(version_env_info.model_dump())))
        
        # 创建初始版本执行记录
        version_task_dict = {
            version_list[0]: {"status": "已完成", "result": "success"},
            version_list[1]: {"status": "未执行", "result": None},
            version_list[2]: {"status": "未执行", "result": None},
            version_list[3]: {"status": "未执行", "result": None},
            version_list[4]: {"status": "已完成", "result": "fail"}
        }
        
        # 创建版本执行信息
        version_execute_info = VersionExecuteInfo(
            task_id=task_id,
            env_id=env_id,
            version=version_list[4],  # 使用最新版本
            test_result="fail",
            version_task_dict=str(version_task_dict)
        )
        
        # 保存版本执行记录到ES
        es_version_execute = EsVersionExecute()
        execute_result = es_version_execute.save_version_execute_task(version_execute_info)
        
        logger.info(f"测试数据创建结果: 环境信息={env_result}, 执行记录={execute_result}")
        return env_result

    @classmethod
    def cleanup_test_data(cls, task_id):
        """清理测试数据"""
        logger.info(f"清理测试数据: task_id={task_id}")
        
        # 删除版本环境信息
        es_version_env = EsVersionEnv()
        env_result = es_version_env.delete_by_query(
            body={"query": {"match": {"task_id": task_id}}},
            params={'request_timeout': 60}
        )
        
        # 删除版本执行记录
        es_version_execute = EsVersionExecute()
        execute_result = es_version_execute.delete_by_query(
            body={"query": {"match": {"task_id": task_id}}},
            params={'request_timeout': 60}
        )
        
        logger.info(f"测试数据清理结果: 环境信息={env_result}, 执行记录={execute_result}")
        return env_result

    def test_get_version_list_by_task_id(self):
        """测试根据task_id获取版本列表"""
        logger.info(f"测试 get_version_list_by_task_id: task_id={self.task_id}")
        
        # 调用方法
        result = self.service.get_version_list_by_task_id(self.task_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 5)
        
        # 验证版本内容
        expected_versions = [
            "NR-V4.20.20.20MainR119_2504201842",
            "NR-V4.20.20.20MainR120_2504211530",
            "NR-V4.20.20.20MainR121_2504220945",
            "NR-V4.20.20.20MainR122_2504231210",
            "NR-V4.30.10.00B207-2_2504232209"
        ]
        
        for version in expected_versions:
            self.assertIn(version, result)
        
        logger.info(f"测试 get_version_list_by_task_id 成功: {result}")

    def test_get_bs_version_with_real_env(self):
        """测试使用真实环境ID获取基站版本"""
        logger.info("开始测试获取基站版本")
        
        try:
            # 使用特定的环境ID
            env_id = "RAN3-上海高频CI团队-VAT1014"
            
            # 从run_service导入Service
            from service.run_service.run_service import Service
            from domain.repository.cron_task import EsCronTask
            
            # 创建Service实例，设置必要的参数
            service = Service(
                service_type="version_rollback",  # 服务类型
                subtype="binary_search",          # 子类型
                state="running",                  # 状态
                needed={                          # 所需参数
                    "env_id": env_id,
                    "max_tries": 3,
                    "delay_secs": 2,
                    "duration_secs": 10
                },
                es=EsCronTask()                   # ES实例
            )
            
            # 调用get_bs_version方法
            version = service.get_bs_version(
                env_id=env_id,
                max_tries=3,      # 减少重试次数
                delay_secs=2,     # 减少重试间隔
                duration_secs=10  # 减少总超时时间
            )
            
            # 验证返回的版本
            self.assertIsNotNone(version, "版本不应为None")
            self.assertIsInstance(version, str, "版本应该是字符串类型")
            self.assertNotEqual(version.strip(), "", "版本不应为空字符串")
            
            # 验证版本格式（根据实际版本格式调整）
            self.assertTrue(
                version.startswith("NR-") or version.startswith("V"),
                f"版本号 {version} 格式不正确，应以 'NR-' 或 'V' 开头"
            )
            
            logger.info(f"成功获取基站版本: {version}")
            
            # 可选：打印更多版本相关信息供分析
            logger.info(f"版本长度: {len(version)}")
            logger.info(f"版本格式: {version[:20]}...") # 只显示前20个字符
            
        except Exception as e:
            logger.error(f"测试获取基站版本失败: {str(e)}")
            raise

    def test_handle_failure_status_odd_versions(self):
        """测试处理失败状态 - 奇数个版本的场景"""
        logger.info("测试奇数版本数量的失败状态处理")
        
        try:
            # 创建奇数个版本的测试数据，使用实际环境中的版本号
            versions = [
                "NR-V4.20.20.20MainR119_2504201842",  # 成功
                "NR-V4.20.20.20MainR120_2504211530",  # 未执行
                "NR-V4.20.20.20MainR121_2504220945",  # 未执行
                "NR-V4.20.20.20MainR122_2504231210",  # 未执行
                "NR-V4.20.20.20MainR122_2504232254"   # 失败 - 使用实际环境中的版本号
            ]
            
            # 创建版本执行字典
            version_task_dict = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "未执行", "result": None},
                versions[2]: {"status": "未执行", "result": None},
                versions[3]: {"status": "未执行", "result": None},
                versions[4]: {"status": "已完成", "result": "fail"}
            }
            
            task_id_1 = self.create_test_data_for_case(
                "odd_versions_no_adjacent",
                versions,
                version_task_dict
            )

            service = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_1,
                    "version_task_dict": version_task_dict
                },
                task_id=task_id_1,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 执行失败状态处理
            service._handle_failure_status("FAILURE")
            
            # 验证二分查找结果
            current_version = "NR-V4.20.20.20MainR122_2504232254"  # 使用实际环境中的版本号
            next_version = service._general_next_version(self.task_id, "version_rollback", current_version)
            self.assertEqual(next_version, versions[2], 
                           f"奇数版本场景下应该选择中间版本 {versions[2]}")
            
        except Exception as e:
            logger.error(f"奇数版本测试失败: {str(e)}")
            raise

    def test_handle_failure_status_even_versions(self):
        """测试处理失败状态 - 偶数个版本的场景"""
        logger.info("测试偶数版本数量的失败状态处理")
        
        try:
            # 创建偶数个版本的测试数据，使用实际环境中的版本号
            versions = [
                "NR-V4.20.20.20MainR119_2504201842",  # 成功
                "NR-V4.20.20.20MainR120_2504211530",  # 未执行
                "NR-V4.20.20.20MainR121_2504220945",  # 未执行
                "NR-V4.20.20.20MainR122_2504232254"   # 失败 - 使用实际环境中的版本号
            ]
            
            # 场景1：当没有相邻的成功失败版本时
            version_task_dict_1 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "未执行", "result": None},
                versions[2]: {"status": "未执行", "result": None},
                versions[3]: {"status": "已完成", "result": "fail"}
            }

            # 为场景1创建独立的测试数据
            task_id_1 = self.create_test_data_for_case(
                "odd_versions_no_adjacent",
                versions,
                version_task_dict_1
            )

            service_1 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_1,
                    "version_task_dict": version_task_dict_1
                },
                task_id=task_id_1,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果 - 应该选择左侧中间版本
            current_version = "NR-V4.20.20.20MainR122_2504232254"  # 使用实际环境中的版本号
            next_version = service_1._general_next_version(task_id_1, "version_rollback", current_version)
            self.assertEqual(next_version, versions[1], 
                           f"偶数版本场景下应该选择左侧中间版本 {versions[1]}")

            # 场景2：当有相邻的成功失败版本时
            version_task_dict_2 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "已完成", "result": "success"},
                versions[2]: {"status": "已完成", "result": "fail"},
                versions[3]: {"status": "已完成", "result": "fail"}
            }

            # 为场景2创建独立的测试数据
            task_id_2 = self.create_test_data_for_case(
                "odd_versions_no_adjacent",
                versions,
                version_task_dict_2
            )

            service_2 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_2,
                    "version_task_dict": version_task_dict_2
                },
                task_id=task_id_2,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果 - 应该返回None表示已找到故障版本
            current_version = "NR-V4.20.20.20MainR122_2504232254"  # 使用实际环境中的版本号
            next_version = service_2._general_next_version(task_id_2, "version_rollback", current_version)
            self.assertIsNone(next_version,
                           "当找到相邻的成功失败版本时应该返回None")
            
        except Exception as e:
            logger.error(f"偶数版本测试失败: {str(e)}")
            raise

    def test_handle_success_status_odd_versions(self):
        """测试处理成功状态 - 奇数个版本的升级场景"""
        logger.info("测试奇数版本数量的成功状态处理")
        
        try:
            versions = [
                "NR-V4.20.20.20MainR119_2504201842",  # 成功
                "NR-V4.20.20.20MainR120_2504211530",  # 未执行
                "NR-V4.20.20.20MainR121_2504220945",  # 未执行
                "NR-V4.20.20.20MainR122_2504231210",  # 未执行
                "NR-V4.20.20.20MainR122_2504232254"   # 失败
            ]
            
            # 场景1：当没有相邻的成功失败版本时
            version_task_dict_1 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "未执行", "result": None},
                versions[2]: {"status": "未执行", "result": None},
                versions[3]: {"status": "未执行", "result": None},
                versions[4]: {"status": "已完成", "result": "fail"}
            }
            
            # 为场景1创建独立的测试数据
            task_id_1 = self.create_test_data_for_case(
                "odd_versions_no_adjacent",
                versions,
                version_task_dict_1
            )
            
            service_1 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_1,
                    "version_task_dict": version_task_dict_1
                },
                task_id=task_id_1,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果
            current_version = versions[0]
            next_version = service_1._general_next_version(task_id_1, "version_upgrade", current_version)
            self.assertEqual(next_version, versions[2],
                            f"奇数版本升级场景下应该选择中间版本 {versions[2]}")

            # 场景2：当有相邻的成功失败版本时
            version_task_dict_2 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "已完成", "result": "success"},
                versions[2]: {"status": "已完成", "result": "fail"},
                versions[3]: {"status": "已完成", "result": "fail"},
                versions[4]: {"status": "已完成", "result": "fail"}
            }
            
            # 为场景2创建独立的测试数据
            task_id_2 = self.create_test_data_for_case(
                "odd_versions_adjacent",
                versions,
                version_task_dict_2
            )
            
            service_2 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_2,
                    "version_task_dict": version_task_dict_2
                },
                task_id=task_id_2,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果
            current_version = versions[1]
            next_version = service_2._general_next_version(task_id_2, "version_upgrade", current_version)
            self.assertIsNone(next_version,
                             "当找到相邻的成功失败版本时应该返回None")

        except Exception as e:
            logger.error(f"奇数版本升级测试失败: {str(e)}")
            raise

    def test_handle_success_status_even_versions(self):
        """测试处理成功状态 - 偶数个版本的升级场景"""
        logger.info("测试偶数版本数量的成功状态处理")

        try:
            # 场景1：当没有相邻的成功失败版本时
            versions = [
                "NR-V4.20.20.20MainR119_2504201842",  # 成功
                "NR-V4.20.20.20MainR120_2504211530",  # 未执行
                "NR-V4.20.20.20MainR121_2504220945",  # 未执行
                "NR-V4.20.20.20MainR122_2504232254"   # 失败
            ]
            
            version_task_dict_1 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "未执行", "result": None},
                versions[2]: {"status": "未执行", "result": None},
                versions[3]: {"status": "已完成", "result": "fail"}
            }
            
            # 为场景1创建独立的测试数据
            task_id_1 = self.create_test_data_for_case(
                "even_versions_no_adjacent",
                versions,
                version_task_dict_1
            )
            
            service_1 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_1,
                    "version_task_dict": version_task_dict_1
                },
                task_id=task_id_1,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果
            current_version = versions[0]
            next_version = service_1._general_next_version(task_id_1, "version_upgrade", current_version)
            self.assertEqual(next_version, versions[1],
                            f"偶数版本升级场景下应该选择左侧中间版本 {versions[1]}")

            # 场景2：当有相邻的成功失败版本时
            version_task_dict_2 = {
                versions[0]: {"status": "已完成", "result": "success"},
                versions[1]: {"status": "已完成", "result": "success"},
                versions[2]: {"status": "已完成", "result": "fail"},
                versions[3]: {"status": "已完成", "result": "fail"}
            }
            
            # 为场景2创建独立的测试数据
            task_id_2 = self.create_test_data_for_case(
                "even_versions_adjacent",
                versions,
                version_task_dict_2
            )
            
            service_2 = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_2,
                    "version_task_dict": version_task_dict_2
                },
                task_id=task_id_2,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 验证二分查找结果
            current_version = versions[1]
            next_version = service_2._general_next_version(task_id_2, "version_upgrade", current_version)
            self.assertIsNone(next_version,
                             "当找到相邻的成功失败版本时应该返回None")

        except Exception as e:
            logger.error(f"偶数版本升级测试失败: {str(e)}")
            raise

    def test_handle_failure_status_boundary_cases(self):
        """测试处理失败状态的边界情况"""
        logger.info("测试边界情况的失败状态处理")
        
        try:
            # 测试用例1: 相邻版本（已找到故障版本）
            versions_two = [
                "NR-V4.20.20.20MainR119_2504201842",  # 成功
                "NR-V4.20.20.20MainR120_2504211530"   # 失败
            ]
            
            version_task_dict_two = {
                versions_two[0]: {"status": "已完成", "result": "success"},
                versions_two[1]: {"status": "已完成", "result": "fail"}
            }
            
            # 为相邻版本场景创建独立的测试数据
            task_id_two = self.create_test_data_for_case(
                "adjacent_versions",
                versions_two,
                version_task_dict_two
            )
            
            service_two = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_two,
                    "version_task_dict": version_task_dict_two
                },
                task_id=task_id_two,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 执行失败状态处理
            service_two._handle_failure_status("FAILURE")
            
            # 验证二分查找结果
            current_version = versions_two[1]
            next_version = service_two._general_next_version(task_id_two, "version_rollback", current_version)
            self.assertIsNone(next_version,
                             "相邻版本场景下应该返回None，因为已找到故障版本")

            # 测试用例2: 只有一个版本且失败
            versions_one = ["NR-V4.20.20.20MainR119_2504201842"]  # 失败
            
            version_task_dict_one = {
                versions_one[0]: {"status": "已完成", "result": "fail"}
            }
            
            # 为单版本场景创建独立的测试数据
            task_id_one = self.create_test_data_for_case(
                "single_version",
                versions_one,
                version_task_dict_one
            )
            
            service_one = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_one,
                    "version_task_dict": version_task_dict_one
                },
                task_id=task_id_one,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 执行失败状态处理
            service_one._handle_failure_status("FAILURE")
            
            # 验证二分查找结果
            current_version = versions_one[0]
            next_version = service_one._general_next_version(task_id_one, "version_rollback", current_version)
            self.assertIsNone(next_version,
                             "单个失败版本场景下应该返回None，表示问题在初始版本")

            # 测试用例3: 空版本列表
            versions_empty = []
            version_task_dict_empty = {}
            
            # 为空版本列表场景创建独立的测试数据
            task_id_empty = self.create_test_data_for_case(
                "empty_versions",
                versions_empty,
                version_task_dict_empty
            )
            
            service_empty = RemoteTestService(
                service_type="version_upgrade",
                subtype="binary_search",
                state="running",
                needed={
                    "env_id": self.env_id,
                    "task_id": task_id_empty,
                    "version_task_dict": version_task_dict_empty
                },
                task_id=task_id_empty,
                env_id=self.env_id,
                es=EsCronTask()
            )
            
            # 执行失败状态处理
            service_empty._handle_failure_status("FAILURE")
            
            # 验证二分查找结果
            next_version = service_empty._general_next_version(task_id_empty, "version_rollback", None)
            self.assertIsNone(next_version,
                             "空版本列表场景下应该返回None，表示没有版本可搜索")

        except Exception as e:
            logger.error(f"边界测试失败: {str(e)}")
            raise

    def tearDown(self):
        """测试后清理工作"""
        try:
            # 清理测试过程中创建的数据
            es_version_env = EsVersionEnv()
            es_version_execute = EsVersionExecute()
            
            # 删除包含基础task_id的所有测试数据
            query = {
                "query": {
                    "wildcard": {
                        "task_id": f"{self.base_task_id}*"
                    }
                }
            }
            
            es_version_env.delete_by_query(body=query)
            es_version_execute.delete_by_query(body=query)
            
            logger.info(f"清理测试数据完成: base_task_id={self.base_task_id}")
            
        except Exception as e:
            logger.error(f"测试清理失败: {str(e)}")
            raise


if __name__ == '__main__':
    unittest.main()
