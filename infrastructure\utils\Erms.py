'''
Created on 2023年3月16日

@author: 10111815
'''
import json

import requests
import os
from infrastructure.logger.logger import logger
from infrastructure.utils.DatetimeFormater import now
from infrastructure.utils.SftpClient import SftpClient
from domain.platform.artifact import Artifact

UPGRADE = "基站升级"
SETUP = "基站开站"
SETUP_WITH_DATA = "本地规划数据开站"
ROLLBACK = "回退"

OPTYPE_MAP = {UPGRADE: "5G_PNF_Auto",
              SETUP: "ITBBU_SetupWithData",
              SETUP_WITH_DATA: "ITBBU_SetupWithData",
              ROLLBACK: "5G_PNF_Rollback"}

OP_URL_MAP = {UPGRADE: "/zte-rd-aegis-envresctrl/envResCtrl/batchScheduledUpgrade",
           ROLLBACK: "/zte-rd-aegis-envresctrl/envResCtrl/batchScheduledUpgrade",
           SETUP: "/zte-rd-aegis-envresctrl/envResCtrl/batchScheduledSetup",
           SETUP_WITH_DATA: "/zte-rd-aegis-envresctrl/envResCtrl/setupByLocalPlanData"}


class ErmsApi(object):

    def __init__(self, name="10111815"):
        self._name = name
        self.erms_url = "https://erms.zte.com.cn/service"
        # self.erms_url = "https://***********:28014"


    def upgrade(self, task_id, task_name, env_id, op_type, version, meids, file=None):
        bodyTemp = {"sourceTaskId": task_id,
                "sourceTaskName": task_name,
                "creator": self._name,
                "depart": "RAN开发三部",
                "envUniqueId": env_id,
                "opDesc": op_type,
                "upgradeType": self._get_upgrade_type(op_type),
                "versionAddress": version,
                "upgradeTime": now(),
                "bbuType": "V9200"
                }
        if op_type == SETUP_WITH_DATA:
            bodyTemp.update({"neInfo": json.dumps({"meId": ";".join(list(map(str, meids)))})})
            body = bodyTemp
            if file:
                filePath = Artifact.download_file_form_url(file[0])
                with open(filePath, 'rb') as fp:
                    content = fp.read()
                file = {"files": (os.path.basename(file[0]), content)}
            else:raise Exception(f"本地规划数据开站时，必须上传本地规划数据")
        else:
            bodyTemp.update({"isNetMgrAllNe": False})
            body =[{**bodyTemp, "neInfo": {"meId": str(meid)}} for meid in meids]
            body = json.dumps(body)
        return self._send_rdc_cmd("POST", self._get_upgrade_url(op_type), body, file)

    def _get_upgrade_url(self, op_type):
        return OP_URL_MAP.get(op_type)

    def _get_upgrade_type(self, op_type):
        return OPTYPE_MAP.get(op_type)

    def brush_para(self, task_id, task_name, env_id, op_desc, task_type, path, group_no):
        url = "/zte-rd-aegis-envresctrl/envResCtrl/flashParams"
        body = {"envUniqueId": env_id,
                "branchVerName": "",
                "opDesc": op_desc,
                "taskType": task_type,
                "depart": 'RAN开发三部',
                "creator": self._name,
                "sourceTaskId": task_id,
                "sourceTaskName": task_name,
                "group": group_no
                }
        content = SftpClient().get_file_stream(path)
        file_name = os.path.basename(path)
        return self._send_rdc_cmd("POST", url, body, {"files": (file_name, content)}, timeout=300)

    def get_version(self, branch_name):
        url = f"/zte-rd-aegis-envresctrl/envResCtrl/queryMatchedVer?branchVerName={branch_name}"
        reponse, matchdVer = self._send_rdc_cmd("GET", url, {})
        if reponse:return matchdVer
        raise Exception(f"erms获取分支{branch_name}最新版本异常，请确认版本是否正确")

    def _send_rdc_cmd(self, method, url, body, files=None, timeout=60):
        url = self._format_url(url)
        try:
            logger.info(f"erms request body: ", body)
            response = requests.request(method, url, data=body, files=files, timeout=timeout, verify=False)
        except Exception as e:
            logger.error(str(e))
            return False, str(e)
        return self._parse_response(response)

    def _format_url(self, url):
        return self.erms_url + url

    def _composite_header(self):
        header = {'Content-Type': "application/json"}
        return header

    def _parse_response(self, response):
        if response.status_code != 200:
            logger.error(response.text)
            return {}, response.text
        logger.info("erms response:", response.text)
        reponse_txt = json.loads(response.text)
        if "code" in reponse_txt:
            code = reponse_txt.get("code")
            if "0000" != code.get("code"):
                return False, code.get("msg")
        return self._parse_result(reponse_txt)

    def _parse_result(self, response):
        result = True
        msg = ""
        if "bo" in response:
            msg = response.get("bo")
            if isinstance(msg, dict):
                if "result" in msg and not msg.get("result"):
                    result = False
        if "result" in response:
            result_infos = response.get("result")
            if isinstance(result_infos, list):
                for result_info in result_infos:
                    if not result_info.get("result"):
                        result = False
                    if result_info.get("msg"):
                        msg += result_info.get("msg") + " "
        return result, msg

    def exist_on_going_task(self, envUniqueId):
        url = f"/zte-rd-aegis-envresctrl/envResCtrl/existOngoingTask?envId={envUniqueId}"
        _, result = self._send_rdc_cmd("GET", url, {})
        return result

    def query_spu_task(self, infos: dict):
        url = "/zte-rd-aegis-envresctrl/envResCtrl/queryEnodebUpgradeRecord"
        body = {"limit": 10,
                "page": 1
                }
        body.update(infos)
        body = json.dumps(body)
        return self._send_rdc_cmd("POST", url, body)

    def cancel_spu_task(self, env_ids: str|list):
        env_ids = env_ids if isinstance(env_ids, list) else [env_ids]
        for env_id in env_ids:
            url = f"/zte-rd-aegis-envresctrl/envResCtrl/cancelUpgradeTask?taskId={env_id}"
            self._send_rdc_cmd("POST", url, {})

if __name__ == "__main__":
    pass
    ErmsApi().cancel_spu_task("451c98151a254c8ab25d87182224c316")