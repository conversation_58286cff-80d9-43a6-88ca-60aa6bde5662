import json
import os
import re
import shutil
import stat
import zipfile
from pathlib import Path

from infrastructure.logger.logger import logger
from infrastructure.utils.CatchException import catchExceptionReturnTrueFalse
from infrastructure.utils.Repeat import retries_on_flag


class FileHandler(object):

    @staticmethod
    def get_specified_file_paths(specifiedDir, fuzzyFileNames, resultList=None):
        resultList = [] if resultList is None else resultList
        abspath = os.path.abspath(specifiedDir)
        for f in os.listdir(abspath):
            childPath = os.path.abspath(abspath + '/' + f)
            if os.path.isdir(childPath):
                resultList = FileHandler.get_specified_file_paths(childPath, fuzzyFileNames, resultList)
            else:
                for fuzzyFileName in fuzzyFileNames:
                    if re.search(fuzzyFileName, childPath):
                        resultList.append(childPath)
        return resultList

    @staticmethod
    def copy_file_and_rename(srcFilePath, destFilePath):
        destDir, filename = os.path.split(destFilePath)
        if not os.path.exists(destDir):
            os.makedirs(destDir)
        shutil.copyfile(srcFilePath, destFilePath)

    @staticmethod
    def copy_dir(srcDir, destDir):
        if not os.path.exists(destDir):
            os.makedirs(destDir)
        srcFiles = FileHandler.get_files(srcDir, False)
        for fileName in srcFiles:
            shutil.copy(fileName, destDir)

    @staticmethod
    def get_files(dirPath, justRoot=False):
        retFiles = []
        for root, _, files in os.walk(dirPath):
            for f in files:
                retFiles.append(os.path.join(root, f).replace("\\", "/"))
            if justRoot:
                break
        return retFiles

    @staticmethod
    def copy_file(srcFilePath, destDir):
        if not os.path.exists(destDir):
            os.makedirs(destDir)
        _, filename = os.path.split(srcFilePath)
        shutil.copyfile(srcFilePath, os.path.abspath(destDir + '/' + filename))

    @staticmethod
    @catchExceptionReturnTrueFalse
    def clear_path(path):
        shutil.rmtree(path)
        os.system('mkdir "{}"'.format(path))

    @staticmethod
    @catchExceptionReturnTrueFalse
    def modify(valueBeforeSub, valueAfterSub, filePath, count=0):
        f = open(filePath, "r+")
        data = f.read()
        f.close()
        open(filePath, 'w').write(re.sub(valueBeforeSub, valueAfterSub, data, count))

    @staticmethod
    def file_filter(path, files):
        for _, _, filenames in os.walk(path):
            return [path + os.sep + filename for filename in filenames if
                    files.replace('_', '') <= os.path.splitext(filename)[0]]

    @staticmethod
    def is_content_in_file(content, filePath):
        f = open(filePath, "r")
        data = f.read()
        f.close()
        index = data.find(content)
        if index < 0:
            return False
        return True

    @staticmethod
    def remove_file(targetFile):
        if os.path.exists(targetFile):
            os.chmod(targetFile, stat.S_IREAD | stat.S_IWRITE)
            os.remove(targetFile)
            logger.debug("Success for remove file!")

    @staticmethod
    def rename_file(sourceFile, targetFile):
        FileHandler.remove_file(targetFile)
        if os.path.exists(sourceFile):
            os.chmod(sourceFile, stat.S_IREAD | stat.S_IWRITE)
            os.rename(sourceFile, targetFile)
            logger.debug("Success for rename file!")

    @staticmethod
    def get_file_hash(filePath, hashValue):
        rfp = os.popen("certutil -hashfile " + filePath + ' ' + hashValue)
        return re.findall('\n(.*)\n', rfp.read().decode('GBK'))[0].replace(' ', '')

    @staticmethod
    def compare_files(file1, file2):
        try:
            f1 = open(file1, 'r')
            f2 = open(file2, 'r')
            file1 = f1.readlines()
            file2 = f2.readlines()
        except IOError:
            return False
        else:
            f1.close()
            f2.close()
            if file1 == file2:
                return True
            else:
                return False

    @staticmethod
    def compare_206files(file1, file2):
        try:
            f1 = open(file1, 'r')
            f2 = open(file2, 'r')
            file1 = f1.readlines()
            file2 = f2.readlines()
        except IOError:
            return False
        else:
            f1.close()
            f2.close()
            for i in range(3, 20):
                if file1[i] != file2[i]:
                    return False
            return True

    @staticmethod
    def unzip_file(zipFilepath, targetPath):
        z = zipfile.ZipFile(zipFilepath, "r")
        if os.path.exists(targetPath):
            shutil.rmtree(targetPath)
        z.extractall(targetPath)

    @staticmethod
    def load_json_file(jsonFolderPath):
        resultDict = {}
        for root, _, filenames in os.walk(jsonFolderPath):
            for filename in filenames:
                if re.search("json$", filename):
                    file = os.path.splitext(filename)[0]
                    jsonData = json.load(open(os.path.join(root, filename)))
                    resultDict.update({("%s.%s" % (file, key)): jsonData[key] for key in jsonData})
        return resultDict

    @staticmethod
    def remove_folder(folderPath):
        logger.info('remove folder "{}"...'.format(folderPath))
        os.system("rmdir /s/q " + folderPath)

    @staticmethod
    def handle_path(path):
        try:
            dir, filename = os.path.split(re.findall(r'.*([C-Zc-z]:.*)', path)[0])
        except:
            dir = ""
            filename = ""
            logger.info("match path failed")
        return dir, filename

    @staticmethod
    def download_file_stream(response, filePath):
        with open(filePath, "wb") as file:
            for chunk in response.iter_bytes():
                file.write(chunk)


def format_size(size_bytes):
    for unit in ["B", "K", "M", "G"]:
        if size_bytes < 1024:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024


def format_file_size(file_path):
    if not os.path.isfile(file_path):
        return "文件不存在"
    size_bytes = os.path.getsize(file_path)
    return format_size(size_bytes)


def delete_file(filePath):
    try:
        os.remove(filePath)
        logger.info(f"文件已删除：{filePath}")
    except Exception as _:
        logger.info(f"文件不存在")


def get_file_info(filePath):
    filename = filePath.split(os.sep)[-1]
    fileSize = format_file_size(filePath)
    return filename, fileSize


@retries_on_flag(3, isRaiseException=True, everyTryDelaySecs=3)
def rename_path(oldpath, newpath):
    try:
        shutil.move(oldpath, newpath)
        return True
    except Exception as e:
        logger.error('error:', e)
        return False


def mk_dir(folder_name):
    if not os.path.exists(folder_name):
        os.mkdir(folder_name)


def check_file_exist(file_path):
    if not os.path.exists(file_path):
        logger.error(f"文件 {file_path} 不存在，请检查路径。")
        raise Exception(f"文件 {file_path} 不存在，请检查路径。")


def get_files(path: str):
    files = []
    folder_path = Path(path)
    for item in folder_path.iterdir():
        if item.is_file():
            files.append(item.name)
        elif item.is_dir():
            print(f"目录: {item.name}")
    return files
