import paramiko


class SftpClient:
    def __init__(self, cfg):
        self.host = cfg.get("host")
        self.port = cfg.get("port", 22)
        self.username = cfg.get("username")
        self.password = cfg.get("password")
        self.transport = None
        self.sftp = None

    def __del__(self):
        """在对象被销毁时关闭SFTP连接"""
        self.close()

    def connect(self):
        """建立SFTP连接"""
        self.transport = paramiko.Transport((self.host, self.port))
        self.transport.connect(username=self.username, password=self.password)
        self.transport.set_keepalive(60)
        self.sftp = paramiko.SFTPClient.from_transport(self.transport)

    def close(self):
        """关闭SFTP连接"""
        if self.sftp:
            self.sftp.close()
        if self.transport:
            self.transport.close()

    def list_directory(self, path):
        """列出指定目录下的文件"""
        if not self.sftp:
            self.connect()
        file_list = self.sftp.listdir(path)
        return file_list

    def get_file_stream(self, path):
        """获取指定路径文件的数据流"""
        if not self.sftp:
            self.connect()
        with self.sftp.open(path, 'rb') as f:
            data = f.read()
        return data

    def get_tree_structure(self, root_dir):
        if not self.sftp:
            self.connect()
        return self._get_tree_structure(root_dir, None)

    def _get_tree_structure(self, path, tree):
        if tree is None:
            tree = {'id': path, 'name': path.split('/')[-1], 'children': []}
        files = self.sftp.listdir_attr(path)
        for file in files:
            if file.st_mode & 0o040000:
                subpath = '{}/{}'.format(path, file.filename)
                child = {'id': subpath, 'name': file.filename, 'children': []}
                self._get_tree_structure(subpath, child)
                if child['children']:
                    tree['children'].append(child)
            else:
                tree['children'].append({'id': path+'/'+file.filename, 'name': file.filename})
        return tree

