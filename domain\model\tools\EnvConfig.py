# -*-coding:utf-8-*-
from pydantic import BaseModel, Field
from domain.model.tools.Action import TdlResponse
from domain.repository.tools.EnvConfigRepository import EnvConfigRepository


class EnvConfigData(BaseModel):
    env_id:  str = Field(description="环境编号")
    group_no: str = Field(description="动态组网分组编号")
    data: dict = Field(description="环境配置数据")

    @property
    def env_config(self):
        return self.data


class EnvConfigResponse(TdlResponse):
    def __init__(self, env_id, group_no="", **data):
        super().__init__(**data)
        self.data = EnvConfigData(env_id=env_id, group_no=group_no, data=self.data)

    @property
    def env_config(self):
        return self.data


class EnvConfig:
    @staticmethod
    def get_env_config(env_id, group_no=""):
        return EnvConfigRepository.get_env_config(env_id, group_no)
