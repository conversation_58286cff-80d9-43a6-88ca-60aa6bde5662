class Role:

    @staticmethod
    def convert(aggregateRootObj, roleClassName):
        for objName in dir(aggregateRootObj):
            obj = getattr(aggregateRootObj, objName)
            if obj.__class__.__name__ == roleClassName:
                return obj

    @staticmethod
    def add_role(root, role):
        for attr in dir(role):
            if hasattr(root, attr):
                continue
            setattr(root, attr, getattr(role, attr))

    @staticmethod
    def add_roles(root, roles):
        if not isinstance(roles, list):
            roles = [roles]
        for role in roles:
            Role.add_role(root, role)
