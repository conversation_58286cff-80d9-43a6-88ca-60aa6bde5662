from datetime import datetime
from typing import List, Dict, Any, Optional, Union

from infrastructure.logger.logger import logger
from domain.model.dto.env_maintain_log import EnvMaintainLog
from domain.repository.es_env import (
    EsJenkinsServerEnv, EsPcEnv, EsBizCoreEnv, EsVersionEnv, EsDeviceEnv
)
from domain.repository.es_env_maintain_log import EsEnvMaintainLog


class EnvInfoService:
    """Base class for environment information services"""

    def __init__(self, es_repo, env_type):
        self._es = es_repo()
        self._env_type = env_type
        self._log_es = EsEnvMaintainLog()

    def _log_operation(self, operation: str, env_id: str, operator: str,
                      before: Optional[Dict] = None, after: Optional[Dict] = None):
        """Log an operation to the maintenance log"""
        # Create safe copies of the data to prevent Elasticsearch mapping issues
        safe_before = None
        safe_after = None

        if before:
            safe_before = before.copy()
            # Remove problematic fields from before data
            if isinstance(safe_before, dict) and "id" in safe_before:
                del safe_before["id"]

        if after:
            safe_after = after.copy()
            # Remove problematic fields from after data
            if isinstance(safe_after, dict) and "id" in safe_after:
                del safe_after["id"]

        # Create the log entry
        log = EnvMaintainLog(
            env_id=env_id,
            env_type=self._env_type,
            operation=operation,
            operator=operator,
            details={
                "before": safe_before,
                "after": safe_after
            }
        )

        # Convert to dictionary and log
        log_dict = log.model_dump()

        # Additional safety check for nested id fields
        if "details" in log_dict and isinstance(log_dict["details"], dict):
            details = log_dict["details"]

            # Check and clean before data
            if "before" in details and isinstance(details["before"], dict):
                if "id" in details["before"]:
                    del details["before"]["id"]

            # Check and clean after data
            if "after" in details and isinstance(details["after"], dict):
                if "id" in details["after"]:
                    del details["after"]["id"]

        # Log the operation
        try:
            self._log_es.index(body=log_dict)
        except Exception as e:
            logger.error(f"Failed to log operation: {e}")
            # Continue execution even if logging fails

    def get_all(self, from_id: int = 0, size: int = 50) -> List[Dict]:
        """Get all environment information"""
        _, results = self._es.query_all_without_time(fromId=from_id, size=size, flag=False)
        return results

    def get_by_id(self, env_id: str) -> List[Dict]:
        """Get environment information by ID"""
        _, results = self._es.query_by_filter_without_sort({"env_id": env_id}, flag=False)
        return results

    def create(self, env_info: Dict, operator: str) -> Dict:
        """Create new environment information"""
        # Add timestamp
        env_info["create_time"] = datetime.now().isoformat()
        env_info["update_time"] = datetime.now().isoformat()
        env_info["update_user"] = operator

        # Remove problematic fields that might cause Elasticsearch mapping issues
        if "id" in env_info:
            logger.warning(f"Removing 'id' field from create data to prevent Elasticsearch mapping issues")
            del env_info["id"]

        # Save to Elasticsearch
        result = self._es.index(body=env_info)

        # Create a safe copy of the data for logging
        safe_env_info = env_info.copy()
        # Remove any fields that might cause issues in the log
        if "id" in safe_env_info:
            del safe_env_info["id"]

        # Log the operation
        self._log_operation("create", env_info["env_id"], operator, None, safe_env_info)

        return {"id": result["_id"], "result": result["result"]}

    def update(self, env_id: str, doc_id: str, env_info: Dict, operator: str) -> Dict:
        """Update environment information"""
        # Get the current document
        before = self._es.get(id=doc_id)

        # Update timestamp and user
        env_info["update_time"] = datetime.now().isoformat()
        env_info["update_user"] = operator

        # Remove problematic fields that might cause Elasticsearch mapping issues
        if "id" in env_info:
            logger.warning(f"Removing 'id' field from update data to prevent Elasticsearch mapping issues")
            del env_info["id"]

        # Update in Elasticsearch
        result = self._es.update(id=doc_id, body={"doc": env_info})

        # Create a safe copy of the data for logging
        safe_env_info = env_info.copy()
        # Remove any fields that might cause issues in the log
        if "id" in safe_env_info:
            del safe_env_info["id"]

        # Log the operation
        self._log_operation("update", env_id, operator, before, safe_env_info)

        return {"id": doc_id, "result": result}

    def delete(self, env_id: str, doc_id: str, operator: str) -> Dict:
        """Delete environment information"""
        # Get the current document
        before = self._es.get(id=doc_id)

        # Delete from Elasticsearch
        result = self._es.delete(id=doc_id)

        # Log the operation
        self._log_operation("delete", env_id, operator, before, None)

        return {"id": doc_id, "result": result}


class JenkinsServerEnvInfoService(EnvInfoService):
    """Service for Jenkins server environment information"""

    def __init__(self):
        super().__init__(EsJenkinsServerEnv, "jenkins_server_env")


class PcEnvInfoService(EnvInfoService):
    """Service for PC environment information"""

    def __init__(self):
        super().__init__(EsPcEnv, "pc_env")


class BizCoreEnvInfoService(EnvInfoService):
    """Service for BizCore environment information"""

    def __init__(self):
        super().__init__(EsBizCoreEnv, "biz_core_env")


class VersionEnvInfoService(EnvInfoService):
    """Service for Version environment information"""

    def __init__(self):
        super().__init__(EsVersionEnv, "version_env")


class DeviceEnvInfoService(EnvInfoService):
    """Service for Device environment information"""

    def __init__(self):
        super().__init__(EsDeviceEnv, "device_env")


class EnvMaintainLogService:
    """Service for environment maintenance logs"""

    def __init__(self):
        self._es = EsEnvMaintainLog()

    def get_logs(self, filters: Dict = None, from_id: int = 0, size: int = 50) -> List[Dict]:
        """Get maintenance logs with optional filters"""
        if filters:
            _, results = self._es.query_by_filter_with_sort(filters, order_key="operation_time", fromId=from_id, size=size, flag=False)
        else:
            _, results = self._es.query_all(fromId=from_id, size=size, flag=False, sortFlag="operation_time")
        return results
