# coding=utf-8
'''
Created on 2014�?0�?0�?

@author: wa<PERSON><PERSON><PERSON><PERSON><PERSON>
'''

import atexit
import sys

from infrastructure.channels.ChannelData import ChannelData
from infrastructure.utils.Reflection import Reflection
from infrastructure.utils.Singleton import Singleton
from infrastructure.logger.logger import logger


@Singleton
class ChannelFactory(object):

    '''
    通道的工厂类
    '''

    def __init__(self):
        '''
        Constructor
        '''
        #         log_config()
        self._channels = {}
        self._channelData = ChannelData()
        atexit.register(self.close_channels)

    def _find_channel(self, uniId):
        if uniId in self._channels:
            return self._channels[uniId]
        else:
            return None

    def _delete_channel(self, uniId):
        if self._find_channel(uniId) is not None:
            self._channels.pop(uniId)

    def _add_channel(self, uniId, ob):
        self._channels[uniId] = ob

    def __del__(self):
        logger.info("CLOSE ALL CHANNEL")
        self.close_channels()

#     工厂类对外暴露的接口

    def create(self, channelType, host, port, timeout=5, mode=None):
        reflection = Reflection()
        packetName = self._channelData.get_package(channelType)
        if "linux" in sys.platform:
            packetName = self.replace_dir(packetName)
        tn = reflection.create_obj(packetName, channelType, host, port, timeout)
        uniId = tn.get_id()
        ob = self._find_channel(uniId)
        if ob is None:
            if mode != "NOT_CONN":
                tn.connect()
            ob = tn
        else:
            try:
                ob.disconnect()
            except Exception:
                logger.error("disconnect {0} fail".format(uniId))
            ob = tn
            ob.connect()
        self._add_channel(uniId, ob)
        return ob

    def replace_dir(self, packetName):
        packetName = packetName.replace('\\', '.')
        packetName = packetName.replace('/', '.')
        return packetName

    def get_channels(self):
        return self._channels

    def close_channels(self):
        for i in self._channels:
            try:
                self._channels[i].disconnect()
            except Exception:
                logger.warn(" close channel {0} is exception.".format(i))
        self._channels.clear()


if __name__ == '__main__':
    fac = ChannelFactory()
    fac2 = ChannelFactory()
    while (1):
        tn = fac.create('Telnet', '10.92.232.180', '64116')
        tn.read('username:', 10)
        logger.info(tn.excute_cmd('admin', 'password:', 10))
        logger.info(tn.excute_cmd('', '$>', 10))
        tn2 = fac2.create('Telnet', '10.92.232.180', '64116')
        tn2.read('username:', 10)
        logger.info(tn2.excute_cmd('admin', 'password:', 10))
        logger.info(tn2.excute_cmd('', '$>', 10))
        tn.excute_cmd('sdfsdf', '$>', 10)
        tn3 = fac.create('Telnet', '10.92.232.180', '64116')
        tn3.read('username:', 10)
        logger.info(tn3.excute_cmd('admin', 'password:', 10))
        logger.info(tn3.excute_cmd('', '$>', 10))
        tn.excute_cmd('sdfsdfsdf', '$>', 10)
        tn2.excute_cmd('sdfsdfsdf', '$>', 10)
        tn3.excute_cmd('sdfsdfsdf', '$>', 10)
