import json
import os
import time

import requests

from domain.platform.workspace.Workspace import get_local_path
from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_exception

TIMEOUT = 120
SYSTEM_CODE = "ACUYJV-Q1V2QU-1YWRCN-NBYCYK-ACTSFQ-BDUNYG-964ECW-DBHCAY"
TASK_ID = "20240613094502255"  # "20240617162955521"
TASK_NAME = "MTSLOG在wxbee解码任务创建"  # "测试任务1"
UPLOAD_URL = "https://wxbee.zte.com.cn/log/uploadData"
SERVER_IP = "http://*************:7999"  # 中转到mia系统的请求，该服务部署在*************/root/program/mia-decoder/main.py
STATUS_CODE_0 = "新建"
STATUS_CODE_1 = "排队中"
STATUS_CODE_2 = "数据文件传输中"
STATUS_CODE_3 = "数据文件传输完成"
STATUS_CODE_4 = "解码中"
STATUS_CODE_5 = "解码完成"
STATUS_CODE_6 = "分析中"
STATUS_CODE_7 = "完成分析"
STATUS_CODE_8 = "传输失败"
STATUS_CODE_9 = "解码失败"
STATUS_CODE_10 = "分析失败"


class MiaDecoder:

    @staticmethod
    async def upload(filepath, user="10288354"):
        filename = filepath.split(os.sep)[-1]
        files = {"files": (filename, open(filepath, 'rb'), "application/x-zip-compressed")}
        body = {"taskName": TASK_NAME, "taskId": TASK_ID, "userID": user, "system": "auto", "systemCode": SYSTEM_CODE}
        response = post_request(UPLOAD_URL, data=body, files=files)
        logger.info(f"上传文件响应：{response}")
        if response.get("status") == "success":
            return True, None
        return False, response.get("message")

    async def decode(self, filename: str, fileSize: str, user: str) -> int:
        flag = False
        taskId = self.start_decode(filename, fileSize, user)
        logger.info(
            f"文件 {filename} 开始解码，任务id：{taskId}，任务地址：https://uda.test.zte.com.cn/#/apps/mia/task-detial/{taskId}")
        while not flag:
            status = self.check_status(taskId, user)
            logger.info(
                f"MIA解码任务：{taskId}的实时状态: {status}, 任务地址：https://uda.test.zte.com.cn/#/apps/mia/task-detial/{taskId}")
            if 5 == status:
                logger.info(f"MIA任务id：{taskId}解码完成！")
                flag = True
            elif status in [8, 9, 10]:
                raise Exception(
                    f"MIA解码任务：{taskId} 解码文件异常, 异常码 {status}, 请前往UDACloud确认！任务地址：https://uda.test.zte.com.cn/#/apps/mia/task-detial/{taskId}")
            else:
                time.sleep(3)
        return taskId

    def start_decode(self, filename: str, fileSize: str, user: str) -> int:
        url = SERVER_IP + "/mia/task/create"
        body = {"filename": filename, "fileSize": fileSize, "user": user}
        response = post_request(url=url, body=body)
        logger.info(f"response：{response}")
        if taskId := response.get("data"):
            return int(taskId)
        raise Exception(f"创建{filename}MIA解码任务失败！")

    def check_status(self, taskId: int, user: str):
        url = SERVER_IP + "/mia/task/status"
        body = {"taskId": str(taskId), "user": user}
        response = post_request(url=url, body=body)
        logger.info(f"response：{response}")
        if resp_data := response.get("data", {}):
            return resp_data.get("status")
        raise Exception(
            f"task: {taskId} 不存在，请前往UDA CLOUD检查确认! 任务地址：https://uda.test.zte.com.cn/#/apps/mia/task-detial/{taskId}")

    async def download_decode_file(self, taskId: int, user: str) -> str:
        filename = "uda_cloud_decode_mtslog_result_" + str(taskId) + ".zip"
        url = SERVER_IP + "/mia/task/download"
        body = {"taskId": str(taskId), "user": user}
        filepath = download_file_request(url=url, body=body, filename=filename)
        return filepath


@retries_on_exception(12, everyTryDelaySecs=30, exceptions=requests.exceptions.ConnectionError)
def post_request(url: str, data: dict = None, body: dict = None, headers: dict = None, files=None):
    logger.info(f"post请求url：{url}，headers：{headers}，data：{data}，json：{body}")
    response = requests.post(url, headers=headers, data=data, json=body, files=files, timeout=60)
    if response.status_code >= 400:
        raise Exception(f"发生错误，响应状态码: {response.status_code}, 响应内容: {response.text}")
    return json.loads(response.text)


@retries_on_exception(12, everyTryDelaySecs=30, exceptions=requests.exceptions.ConnectionError)
def download_file_request(url: str, body: dict = None, headers: dict = None, filename: str = None):
    logger.info(f"post请求url：{url}，headers：{headers}，json：{body}, filepath: {filename}")
    response = requests.post(url, headers=headers, json=body, timeout=60)

    try:
        filepath = get_local_path(filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
            logger.info(f"文件下载成功，存放地址：{filepath}")
    except Exception as e:
        raise Exception(f"文件下载失败，状态码：{response.status_code}, error: {e}")
    if response.status_code >= 400:
        raise Exception(f"接口发生错误，响应状态码: {response.status_code}, 响应内容: {response.text}")
    return filepath


if __name__ == '__main__':
    # ret = asyncio.run(MiaDecoder().download_decode_file(3356, "10288354"))
    print(SERVER_IP)
