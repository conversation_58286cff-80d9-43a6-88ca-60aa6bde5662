# -*- encoding: utf-8 -*-
"""
@Time    :   2023-8-28
<AUTHOR>   wen zhang 10234064
@Desc    :   None
"""
import base64
import json

import requests
from Crypto import Random
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

from infrastructure.logger.logger import logger


BASE_URL = "https://erms.zte.com.cn/service/zte-rd-aegis-modelmgr"
NET_PRIVATE_KEY_URL = BASE_URL + "/pwd/queryNetMgrPrivateKeyById?keyId={0}&sourceId=5"


def get_ume_password(umeId, encrypted_text):
    private_key = _get_private_key(umeId)
    password = decryption_password(encrypted_text, private_key)
    return password


def _get_private_key(umeId):
    resp = requests.get(NET_PRIVATE_KEY_URL.format(umeId), timeout=60)
    if resp.status_code != 200:
        logger.error(resp.text)
        raise Exception(resp.text)
    response_text = json.loads(resp.text)
    # logger.info(response_text)
    if "code" in response_text:
        code = response_text.get("code")
        if "0000" != code.get("code"):
            logger.error(code)
            raise Exception(f'code err {code}')
    private_key = response_text.get("bo")
    logger.debug(f'private_key info: {private_key}')
    if not private_key:
        raise ValueError(f'从ERMS获取5G网管[umeId:{umeId}]私钥失败!请检查网元在ERMS中的网管配置或者网管的公共配置！')
    return "-----BEGIN RSA PRIVATE KEY-----\n" + private_key + "\n-----END RSA PRIVATE KEY-----"


def decryption_password(encryptedText, privateKey):
    encryptedText = base64.b64decode(encryptedText.encode("utf-8"))
    cipherPrivate = PKCS1_v1_5.new(RSA.importKey(privateKey))
    textDecrypted = cipherPrivate.decrypt(encryptedText, Random.new().read)
    return textDecrypted.decode()
