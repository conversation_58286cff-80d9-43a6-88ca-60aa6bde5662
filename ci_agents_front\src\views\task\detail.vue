<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import jobApi from "@/api/jobApi";

defineOptions({
  name: "TaskDetail"
});

const route = useRoute();
const router = useRouter();
const taskId = ref(route.params.taskId as string);
const taskData = ref(null);
const loading = ref(true);
const error = ref("");
const activeTab = ref('pipeline');
const taskDetail = ref(null);
const jobState = ref("");
const pipelineSteps = ref([]);
const logAnalysisData = ref(null);
const logAnalysisLoading = ref(false);
const logAnalysisError = ref(null);
const showStatusHistoryDialog = ref(false);
const currentServiceKey = ref('');
const statusHistory = ref([]);

// 计算属性：按时间排序的状态历史
const sortedStatusHistory = computed(() => {
  return [...statusHistory.value].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()  // 早的在前
  );
});

// 确保所有生命周期钩子都在组件setup的顶层同步调用
onMounted(async () => {
  try {
    // 从localStorage获取任务数据
    const data = localStorage.getItem('taskLogData');
    if (data) {
      taskData.value = JSON.parse(data);
      initializePipelineSteps();
    } else {
      error.value = "未找到任务数据";
    }
    
    // 获取任务详情
    await fetchTaskById(taskId.value);
  } catch (err) {
    error.value = '解析任务数据失败';
  } finally {
    loading.value = false;
  }
});

// 移除 onUnmounted 钩子，如果需要清理资源，可以在组件内部使用其他方式处理

// 获取任务详情
const fetchTaskById = async (taskId: string) => {
  try {
    const response = await jobApi.getJobById(taskId);
    if (response.success && response.data && Array.isArray(response.data) && response.data.length > 0) {
      taskDetail.value = response.data;
      // 提取 current_state 并赋值给 result
      jobState.value = taskDetail.value[0].current_state || "";
    } else {
      taskDetail.value = [];
      jobState.value = ""; // 重置 result
    }
  } catch (error) {
    taskDetail.value = [];
    jobState.value = ""; // 重置 result
  }
};

// 初始化流水线步骤
const initializePipelineSteps = () => {
  // 定义期望的步骤顺序
  const expectedOrder = [
    'jenkins_job_save',
    'log_analysis',
    'env_check',
    'online_test',
    'version_rollback'
  ];
  
  // 从任务数据中提取所有唯一的service_key
  const uniqueServiceKeys = new Set();
  if (taskData.value && Array.isArray(taskData.value)) {
    taskData.value.forEach(item => {
      if (item.service_key) {
        uniqueServiceKeys.add(item.service_key);
      }
    });
  }
  
  // 按照期望的顺序创建步骤数组
  pipelineSteps.value = expectedOrder
    .filter(key => uniqueServiceKeys.has(key))
    .map(serviceKey => ({ serviceKey }));
};

// 切换标签页
const switchTab = async (tabName: string) => {
  activeTab.value = tabName;
  if (tabName === 'logAnalysis') {
    await fetchLogAnalysis();
  }
};

// 获取日志分析数据
const fetchLogAnalysis = async () => {
  logAnalysisLoading.value = true;
  logAnalysisError.value = null;
  logAnalysisData.value = null;

  try {
    const result = await jobApi.queryTestcaseLogAnalysis(taskId.value);
    if (result.success) {
      logAnalysisData.value = result.data;
    } else {
      logAnalysisError.value = result.message;
    }
  } catch (error) {
    logAnalysisError.value = '获取日志分析数据失败，请稍后重试';
  } finally {
    logAnalysisLoading.value = false;
  }
};

// 获取步骤状态类
const getStepClassSortByTimeStamp = (serviceKey: string) => {
  // 获取该serviceKey对应的所有记录并按时间戳排序
  const records = taskData.value
    .filter(item => item.service_key === serviceKey)
    .sort((a, b) => {
      // 确保timestamp是数字类型再进行减法运算
      const dateA = new Date(b.timestamp).getTime();
      const dateB = new Date(a.timestamp).getTime();
      return dateA - dateB;
    });

  // 获取最新的状态（如果有记录的话）
  const latestStatus = records.length > 0 ? records[0].status : '';

  // 根据最新状态返回对应的样式类
  switch (latestStatus) {
    case 'success':
      return 'success';
    case 'in_progress':
      return 'warning';
    case 'failure':
      return 'error';
    default:
      return ''; // 默认无样式
  }
};

// 检查步骤是否存在
const hasStep = (stepKey: string) => {
  return pipelineSteps.value.some(step => step.serviceKey === stepKey);
};

// 返回上一页
const goBack = () => {
  router.push('/');
};

// 显示状态历史对话框
const showStatusHistory = (serviceKey: string) => {
  currentServiceKey.value = serviceKey;
  statusHistory.value = taskData.value.filter(item => item.service_key === serviceKey);
  showStatusHistoryDialog.value = true;
};

// 关闭状态历史对话框
const closeStatusHistory = () => {
  showStatusHistoryDialog.value = false;
  currentServiceKey.value = '';
  statusHistory.value = [];
};

// 格式化时间戳
const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 格式化状态
const formatStatus = (status: string) => {
  switch (status) {
    case 'success':
      return '成功';
    case 'in_progress':
      return '进行中';
    case 'failure':
      return '失败';
    default:
      return status;
  }
};

// 添加编辑对话框相关的状态
const showEditDialog = ref(false);
const editForm = ref({
  testcase_name: '',
  manualClassification: '',
  manualAnalysisResult: ''
});
const currentEditItem = ref(null);

// 打开编辑对话框
const openEditDialog = (item) => {
  currentEditItem.value = item;
  editForm.value = {
    testcase_name: item.testcase_name,
    manualClassification: item.manualClassification || '',
    manualAnalysisResult: item.manualAnalysisResult || ''
  };
  showEditDialog.value = true;
};

// 关闭编辑对话框
const closeEditDialog = () => {
  showEditDialog.value = false;
  currentEditItem.value = null;
  editForm.value = {
    testcase_name: '',
    manualClassification: '',
    manualAnalysisResult: ''
  };
};

// 保存编辑
const saveEdit = async () => {
  if (!currentEditItem.value) return;
  
  try {
    const result = await jobApi.updateTestcaseLogAnalysis({
      taskId: taskId.value,
      testcaseName: currentEditItem.value.testcase_name,
      classification: editForm.value.manualClassification,
      analysisResult: editForm.value.manualAnalysisResult
    });
    
    if (result.success) {
      await fetchLogAnalysis();
      closeEditDialog();
    } else {
      console.error('更新失败:', result.message);
    }
  } catch (error) {
    console.error('更新日志分析出错:', error);
  }
};

// 添加显示完整内容对话框相关的状态
const showFullContentDialog = ref(false);
const currentFullContentItem = ref(null);

// 显示完整内容
const showFullContent = (item) => {
  currentFullContentItem.value = item;
  showFullContentDialog.value = true;
};

// 关闭完整内容对话框
const closeFullContentDialog = () => {
  showFullContentDialog.value = false;
  currentFullContentItem.value = null;
};
</script>

<template>
  <div class="task-detail-container">
    <!-- 添加面包屑导航栏 -->
    <div class="breadcrumb">
      <router-link to="/" class="breadcrumb-item">任务列表</router-link>
      <span class="breadcrumb-separator">/</span>
      <span class="breadcrumb-item current">ID: {{ taskId }}</span>
    </div>

    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="error" class="error-message">
      <el-alert :title="error" type="error" show-icon />
    </div>
    
    <div v-else class="task-content">
      <div class="tab-container">
        <div class="tabs">
          <div
            :class="['tab', { active: activeTab === 'pipeline' }]"
            @click="switchTab('pipeline')"
          >流水线</div>
          <div
            :class="['tab', { active: activeTab === 'logAnalysis' }]"
            @click="switchTab('logAnalysis')"
          >日志分析</div>
        </div>
      </div>

      <!-- 流水线标签内容 -->
      <div v-if="activeTab === 'pipeline'" class="progress-container">
        <div class="progress-bar">
          <div class="step-container">
            <!-- 成功步骤：jenkins_job_save -->
            <div class="step"
                :class="{
                  'success': getStepClassSortByTimeStamp('jenkins_job_save') === 'success',
                  'warning': getStepClassSortByTimeStamp('jenkins_job_save') === 'warning',
                  'error': getStepClassSortByTimeStamp('jenkins_job_save') === 'error'
                }" v-if="hasStep('jenkins_job_save')">
              <div class="step-dot" @click="showStatusHistory('jenkins_job_save')">
                <div v-if="getStepClassSortByTimeStamp('jenkins_job_save') === 'success'" class="check-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('jenkins_job_save') === 'warning'" class="refresh-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('jenkins_job_save') === 'error'" class="cross-icon"></div>
              </div>
              <div class="step-text">jenkins_job_save</div>
            </div>

            <div class="arrow"
                :class="{
                  'success': getStepClassSortByTimeStamp('jenkins_job_save') === 'success',
                  'warning': getStepClassSortByTimeStamp('jenkins_job_save') === 'warning',
                  'error': getStepClassSortByTimeStamp('jenkins_job_save') === 'error'
              }" v-if="hasStep('jenkins_job_save')"></div>

            <!-- 成功步骤：log_analysis -->
            <div class="step"
                :class="{
                  'success': getStepClassSortByTimeStamp('log_analysis') === 'success',
                  'warning': getStepClassSortByTimeStamp('log_analysis') === 'warning',
                  'error': getStepClassSortByTimeStamp('log_analysis') === 'error'
                }" v-if="hasStep('log_analysis')">
              <div class="step-dot" @click="showStatusHistory('log_analysis')">
                <div v-if="getStepClassSortByTimeStamp('log_analysis') === 'success'" class="check-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('log_analysis') === 'warning'" class="refresh-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('log_analysis') === 'error'" class="cross-icon"></div>
              </div>
              <div class="step-text">log_analysis</div>
            </div>

            <div class="arrow"
                :class="{
                  'success': getStepClassSortByTimeStamp('log_analysis') === 'success',
                  'warning': getStepClassSortByTimeStamp('log_analysis') === 'warning',
                  'error': getStepClassSortByTimeStamp('log_analysis') === 'error'
                }" v-if="hasStep('log_analysis')"></div>

            <!-- 环境检查步骤：env_check -->
            <div class="step"
                :class="{
                  'success': getStepClassSortByTimeStamp('env_check') === 'success',
                  'warning': getStepClassSortByTimeStamp('env_check') === 'warning',
                  'error': getStepClassSortByTimeStamp('env_check') === 'error'
                }" v-if="hasStep('env_check')">
              <div class="step-dot" @click="showStatusHistory('env_check')">
                <div v-if="getStepClassSortByTimeStamp('env_check') === 'success'" class="check-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('env_check') === 'warning'" class="refresh-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('env_check') === 'error'" class="cross-icon"></div>
              </div>
              <div class="step-text">env_check</div>
            </div>

            <div class="arrow"
                :class="{
                  'success': getStepClassSortByTimeStamp('env_check') === 'success',
                  'warning': getStepClassSortByTimeStamp('env_check') === 'warning',
                  'error': getStepClassSortByTimeStamp('env_check') === 'error'
                }" v-if="hasStep('env_check')"></div>

            <!-- 在线测试步骤：online_test -->
            <div class="step"
                :class="{
                  'success': getStepClassSortByTimeStamp('online_test') === 'success',
                  'warning': getStepClassSortByTimeStamp('online_test') === 'warning',
                  'error': getStepClassSortByTimeStamp('online_test') === 'error'
                }" v-if="hasStep('online_test')">
              <div class="step-dot" @click="showStatusHistory('online_test')">
                <div v-if="getStepClassSortByTimeStamp('online_test') === 'success'" class="check-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('online_test') === 'warning'" class="refresh-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('online_test') === 'error'" class="cross-icon"></div>
              </div>
              <div class="step-text">online_test</div>
            </div>

            <div class="arrow"
                :class="{
                  'success': getStepClassSortByTimeStamp('online_test') === 'success',
                  'warning': getStepClassSortByTimeStamp('online_test') === 'warning',
                  'error': getStepClassSortByTimeStamp('online_test') === 'error'
                }" v-if="hasStep('online_test')"></div>

            <!-- 版本回滚步骤：version_rollback -->
            <div class="step"
                :class="{
                  'success': getStepClassSortByTimeStamp('version_rollback') === 'success',
                  'warning': getStepClassSortByTimeStamp('version_rollback') === 'warning',
                  'error': getStepClassSortByTimeStamp('version_rollback') === 'error'
                }" v-if="hasStep('version_rollback')">
              <div class="step-dot" @click="showStatusHistory('version_rollback')">
                <div v-if="getStepClassSortByTimeStamp('version_rollback') === 'success'" class="check-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('version_rollback') === 'warning'" class="refresh-icon"></div>
                <div v-if="getStepClassSortByTimeStamp('version_rollback') === 'error'" class="cross-icon"></div>
              </div>
              <div class="step-text">version_rollback</div>
            </div>
            
            <div class="arrow" 
              :class="{
                'success': getStepClassSortByTimeStamp('version_rollback') === 'success',
                'warning': getStepClassSortByTimeStamp('version_rollback') === 'warning',
                'error': getStepClassSortByTimeStamp('version_rollback') === 'error'
              }" v-if="hasStep('version_rollback')"></div>

          <!-- 结束步骤 -->
          <div class="step" :class="{
            'success': jobState === 'PASS',
            'error': jobState === 'FAIL'
          }">
            <div class="step-dot">
              <div v-if="jobState === 'PASS'" class="check-icon"></div>
              <div v-if="jobState === 'FAIL'" class="cross-icon"></div>
            </div>
            <div class="step-text">流程结束</div>
          </div>

          </div>
        </div>
        
      </div>

      <!-- 日志分析标签内容 -->
      <div v-if="activeTab === 'logAnalysis'" class="log-analysis-container">
        <div class="log-content">
          <h3>日志分析结果</h3>
          <!-- 添加加载状态显示 -->
          <div v-if="logAnalysisLoading" class="loading-text">加载中...</div>
          <!-- 添加错误信息显示 -->
          <div v-if="logAnalysisError" class="error-text">{{ logAnalysisError }}</div>
          <!-- 显示日志分析数据 -->
          <div v-if="logAnalysisData" class="log-data">
            <table class="analysis-table">
              <thead>
                <tr>
                  <th>用例名称</th>
                  <th>状态</th>
                  <th>详细信息</th>
                  <th>生成时间</th>
                  <th>分析结果</th>
                  <th>手动分类</th>
                  <th>人工分析</th>
                  <th>操作</th>
                  <th>原始数据</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in logAnalysisData.data" :key="item._id">
                  <td>{{ item.testcase_name }}</td>
                  <td>{{ item.status }}</td>
                  <td>{{ item.detail_msg }}</td>
                  <td>{{ item.createdTime }}</td>
                  <td>{{ item.result }}</td>
                  <td>{{ item.manualClassification }}</td>
                  <td>{{ item.manualAnalysisResult }}</td>
                  <td>
                  <button class="edit-btn" @click="openEditDialog(item)">修改</button>
                </td>
                <td>
                  <button class="view-full-btn" @click="showFullContent(item)">显示</button>
                </td>
                </tr>
              </tbody>
            </table>
            <div class="table-footer">
              总计: {{ logAnalysisData.total }} 条记录
            </div>
          </div>
          <el-empty v-else-if="!logAnalysisLoading && !logAnalysisError" description="暂无日志分析数据" />
        </div>
      </div>
    </div>
  </div>
  
  <!-- 完整内容对话框 -->
  <div class="full-content-dialog-overlay" v-if="showFullContentDialog" @click="closeFullContentDialog">
    <div class="full-content-dialog" @click.stop>
      <div class="full-content-body">
        <div v-for="(value, key) in currentFullContentItem" :key="key" class="content-item">
          <strong>{{ key }}:</strong>
          <span>{{ value }}</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 状态历史对话框 -->
  <div class="status-history-dialog-overlay" v-if="showStatusHistoryDialog" @click="closeStatusHistory">
    <div class="status-history-dialog" @click.stop>
      <h3>状态历史 - {{ currentServiceKey }}</h3>
      <div class="status-history-content">
        <table class="status-history-table">
          <thead>
            <tr>
              <th>时间</th>
              <th>操作</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(record, index) in sortedStatusHistory" :key="index"
                :class="{ 'success': record.status === 'success',
                         'warning': record.status === 'in_progress',
                         'error': record.status === 'failure' }">
              <td>{{ formatTimestamp(record.timestamp) }}</td>
              <td>{{ record.operation || '-' }}</td>
              <td>{{ formatStatus(record.status) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- 编辑对话框 -->
  <div class="dialog-overlay" v-if="showEditDialog" @click="closeEditDialog">
    <div class="dialog-content" @click.stop>
      <div class="form-group">
        <label>手动分类:</label>
        <select v-model="editForm.manualClassification" class="select-input">
          <option value="">请选择分类</option>
          <option value="升级用例失败">升级用例失败</option>
          <option value="接入用例失败">接入用例失败</option>
          <option value="ping包用例失败">ping包用例失败</option>
          <option value="灌包流量为0">灌包流量为0</option>
          <option value="灌包流量不足">灌包流量不足</option>
          <option value="other">other</option>
        </select>
      </div>
      <div class="form-group">
        <label>人工分析结果:</label>
        <input v-model="editForm.manualAnalysisResult" placeholder="请输入人工分析结果"></input>
      </div>
      <div class="dialog-footer">
        <button class="save-btn" @click="saveEdit">保存</button>
        <button class="cancel-btn" @click="closeEditDialog">取消</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.task-detail-container {
  padding: 20px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #409EFF;
  text-decoration: none;
}

.breadcrumb-item.current {
  color: #606266;
  font-weight: bold;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #909399;
}

.tab-container {
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #EBEEF5;
}

.tab {
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab.active {
  color: #409EFF;
  border-bottom-color: #409EFF;
}

.progress-container {
  margin-top: 20px;
}

.progress-bar {
  margin-bottom: 30px;
}

.step-container {
  display: flex;
  justify-content: center; /* 改为居中对齐 */
  position: relative;
  margin-bottom: 40px;
  margin-left: auto; /* 添加左右自动边距 */
  margin-right: auto; /* 添加左右自动边距 */
  margin-top: 200px; /* 添加顶部边距使整体下移 */
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #F2F6FC;
  border: 2px solid #DCDFE6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.step.success .step-dot {
  background-color: #67C23A;
  border-color: #67C23A;
}

.step.warning .step-dot {
  background-color: #E6A23C;
  border-color: #E6A23C;
}

.step.error .step-dot {
  background-color: #F56C6C;
  border-color: #F56C6C;
}

.check-icon:after {
  content: "✓";
  color: white;
  font-size: 14px;
}

.refresh-icon:after {
  content: "⟳";
  color: white;
  font-size: 14px;
}

.error-icon:after {
  content: "✕";
  color: white;
  font-size: 14px;
}

.step-label {
  font-size: 12px;
  color: #606266;
}

.logs-title {
  margin: 20px 0;
  color: #303133;
  font-size: 18px;
}

.loading, .error-message {
  margin: 20px 0;
}

.log-analysis-container {
  margin-top: 20px;
}

.log-content h3 {
  margin-bottom: 16px;
  color: #303133;
}

.loading-text, .error-text {
  padding: 20px;
  text-align: center;
}

.error-text {
  color: #F56C6C;
}

.analysis-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  table-layout: fixed;  /* 固定表格布局，使列宽设置生效 */
}

.analysis-table th,
.analysis-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  max-width: 200px;        /* 设置最大列宽 */
  white-space: nowrap;     /* 防止文本换行 */
  overflow: hidden;        /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

/* 添加悬停提示 */
.analysis-table td:hover {
  overflow: visible;
  white-space: normal;
  word-break: break-all;
  position: relative;
  z-index: 1;
  background-color: #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 为不同列设置不同的宽度 */
.analysis-table th:nth-child(1),
.analysis-table td:nth-child(1) {
  text-align: center;
  width: 15%;  /* 用例名称 */
}

.analysis-table th:nth-child(2),
.analysis-table td:nth-child(2) {
  text-align: center;
  width: 7%;  /* 状态 */
}

.analysis-table th:nth-child(3),
.analysis-table td:nth-child(3) {
  text-align: center;
  width: 20%;  /* 详细信息 */
}

.analysis-table th:nth-child(4),
.analysis-table td:nth-child(4) {
  text-align: center;
  width: 8%;  /* 生成时间 */
}

.analysis-table th:nth-child(5),
.analysis-table td:nth-child(5) {
  text-align: center;
  width: 8%;  /* 分析结果 */
}

.analysis-table th:nth-child(6),
.analysis-table td:nth-child(6) {
  text-align: center;
  width: 8%;  /* 手动分类 */
}

.analysis-table th:nth-child(7),
.analysis-table td:nth-child(7) {
  text-align: center;
  width: 10%;  /* 人工分析 */
}

.analysis-table th:nth-child(8),
.analysis-table td:nth-child(8) {
  text-align: center;
  width: 5%;  /* 修改 */
}

.analysis-table th:nth-child(9),
.analysis-table td:nth-child(9) {
  text-align: center;
  width: 5%;  /* 显示全文 */
}

.analysis-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.analysis-table tr:nth-child(even) {
  background-color: #fafafa;
}

.analysis-table tr:hover {
  background-color: #f0f0f0;
}

.table-footer {
  text-align: right;
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

.arrow {
  position: relative;
  width: 150px;
  height: 3.5px;
  background-color: #d9d9d9; /* 默认灰色 */
  margin: 0 5px;
  top: 12px; /* 调整箭头位置，使其与圆点中心对齐 */
  z-index: 2;
}

/* 黄色状态的箭头 - 当前正在进行 */
.arrow.warning {
  background-color: #faad14;
}

/* 绿色状态的箭头 - 已完成 */
.arrow.success {
  background-color: #52c41a;
}

/* 红色状态的箭头 - 失败 */
.arrow.error {
  background-color: #f5222d;
}


.arrow::after {
  content: '';
  position: absolute;
  right: -5px;
  top: -5.5px; /* 调整箭头尖端位置，使其与箭头线条对齐 */
  width: 0;
  height: 0;
  border-left: 12px solid #d9d9d9; /* 默认灰色 */
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

/* 黄色状态的箭头尖端 - 当前正在进行 */
.arrow.warning::after {
  border-left-color: #faad14;
}

/* 绿色状态的箭头尖端 - 已完成 */
.arrow.success::after {
  border-left-color: #52c41a;
}

/* 红色状态的箭头尖端 - 失败 */
.arrow.error::after {
  border-left-color: #f5222d;
}

/* 蓝色状态的箭头尖端 - 不再使用 */
.arrow.active::after {
  border-left-color: #666;
}

.status-history-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.status-history-dialog {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 800px;  /* 增加最小宽度 */
  max-width: 90%;    /* 增加最大宽度比例 */
  max-height: 80vh;
  overflow-y: auto;
}

/* 确保弹窗在小屏幕上也能正常显示 */
@media screen and (max-width: 900px) {
  .status-history-dialog {
    min-width: 95%;
    margin: 10px;
  }
}

.status-history-content {
  margin: 20px 0;
}

.status-history-table {
  width: 100%;
  border-collapse: collapse;
}

.status-history-table th,
.status-history-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.status-history-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.status-history-table td {
  word-break: break-word;
}

/* 添加悬停提示 */
.status-history-table td:hover {
  overflow: visible;
  white-space: normal;
  word-break: break-all;
  position: relative;
  z-index: 1;
  background-color: #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 为不同列设置不同的宽度 */
.status-history-table th:nth-child(1),
.status-history-table td:nth-child(1) {
  text-align: center;
  width: 40%;  /* 时间 */
}

.status-history-table th:nth-child(2),
.status-history-table td:nth-child(2) {
  text-align: center;
  width: 20%;  /* 状态 */
}

.status-history-table th:nth-child(3),
.status-history-table td:nth-child(3) {
  text-align: center;
  width: 30%;  /* 操作 */
}

/* 确保表格单元格内容溢出时的处理 */
.status-history-table td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 0;  /* 确保text-overflow生效 */
}

.status-history-table tr:nth-child(even) {
  background-color: #fafafa;
}

.status-history-table tr:hover {
  background-color: #f0f0f0;
}

.log-data {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  white-space: pre-wrap;
}

.full-content-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 编辑对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90%;
}

.full-content-dialog {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 800px; /* 固定宽度为800px */
  max-width: 90%; /* 在小屏幕上最大宽度为90% */
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 74.5%;
  padding: 4px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer button {
  margin-left: 10px;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.select-input {
  width: 74.5%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.select-input:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.select-input option {
  padding: 8px;
}

.textarea-input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

.input-field {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.save-btn {
  background-color: #409EFF;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn:hover {
  background-color: #66B1FF;
}

.cancel-btn {
  background-color: #ccc;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #999;
}

.edit-btn {
  padding: 4px 8px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.edit-btn:hover {
  background-color: #40a9ff;
}

.view-full-btn {
  padding: 4px 8px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.view-full-btn:hover {
  background-color: #40a9ff;
}


.full-content-body {
  margin: 20px 0;
}

.close-btn {
  padding: 6px 16px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.close-btn:hover {
  background-color: #e0e0e0;
}

.content-item {
  margin: 10px 0;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.content-item strong {
  display: inline-block;
  width: 150px;
  color: #666;
}
</style>


