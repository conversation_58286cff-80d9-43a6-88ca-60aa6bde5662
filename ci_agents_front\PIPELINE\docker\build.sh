#!/bin/bash
set -e

SERVICE_NAME=$(awk -F '=' '/^SERVICE_NAME/ {print $2}' build_config.ini)
TAG=$(awk -F '=' '/^TAG/ {print $2}' build_config.ini)
IMAGE_NAME=$SERVICE_NAME:$TAG
CONTAINER_NAME=$SERVICE_NAME-$TAG

clean_pack_tar(){
    pwd
    echo "--before.sh-- rm begin"
    # 删除既有UI服务tar包,镜像tar及zip
    rm -rf *.tar || true
    rm -rf *.zip || true
    rm -rf ../../$SERVICE_NAME.tar || true
    echo "--before.sh-- rm end"
    ls -l
    echo "--before.sh-- tar begin"
    # 将前端的dist目录的相对位置创建UI服务tar包
    echo "source code"
    ls -l ../../
    cd ../..
    tar czvf $SERVICE_NAME.tar . --exclude=./$SERVICE_NAME.tar || true
    mv $SERVICE_NAME.tar ./PIPELINE/docker/$SERVICE_NAME.tar

    cd ./PIPELINE/docker
    ls -l
    echo "--before.sh-- tar end"
    ls -l
}

build_image(){
    #停止并删除容器
    docker stop $CONTAINER_NAME || echo "ignore"
    docker rm $CONTAINER_NAME || echo "ignore"
    docker ps -a

    #删除镜像并构建镜像
    docker rmi -f $IMAGE_NAME || echo "ignore"
    docker build --build-arg SERVICE_NAME=$SERVICE_NAME -t $IMAGE_NAME .
    docker images -a

    #打包镜像
    docker save -o $IMAGE_NAME.tar $IMAGE_NAME
    zip $CONTAINER_NAME.zip $IMAGE_NAME.tar deploy.sh
}

main(){
    clean_pack_tar
    build_image
}

main