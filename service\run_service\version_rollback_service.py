from domain.model.dto.logdto import LogJobInfo
from infrastructure.logger.logger import logger
from service.check_env_service import EnvMonitorService
from service.logging_service import log_execution, LoggingService
from service.run_service.run_service import Service
import sys
from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
from domain.repository.cron_task import EsCronTask
from domain.model.dto.corn_task import *
from domain.repository.es_env import EsPcEnv, EsVersionEnv
import asyncio
from service.tools.ActionService import ActionService
from service.tools.UmeService import UmeService
import time


class VersionRollbackService(Service):
    def __init__(self, service_type, subtype, state, needed, task_id, env_id, es, cron_task_id):
        super().__init__(service_type, subtype, state, needed, es)
        self.task_id = task_id
        self.env_id = env_id
        self._es_version_env = EsVersionEnv()
        self.job_name = needed.get("job_name", "")
        self.build_number = needed.get("build_number", "")
        self.cron_task_id = cron_task_id


    def execute(self):
        """
            根据subtype判断后续流程，如果subtype是"update"后续应该触发升级
            如果subtype是"online_test"，存放线上复测队列
        """
        version = self.needed.get('tar_path', '')
        roll_back_res = self.get_verison_rollback_state()
        if not roll_back_res.result:
            # 回退失败了邮件通知
            self._send_email(
                subject=f"{self.env_id}发送版本失败失败",
                content=f"发送版本失败失败\n下一个测试版本: {version}"
            )
            return
        else:
            if roll_back_res.data.status.value == "成功":
                time.sleep(60)
                # 更新数据
                if self._handle_env_check():
                    if self.subtype == "update":
                        trace_info = f"{self.env_id}回退版本已经完成，准备升级{version}"
                    elif self.subtype == "online_test":
                        trace_info = f"{self.env_id}进入回退后的online_test流程"
                    else:
                        trace_info = f"{self.env_id}不存在的子类型"
                    self._update_task_status(self.task_id, self.cron_task_id, "completed", self.env_id, trace_info)
                    if self.subtype == "update":
                        # 继续升级
                        logger.info("进入回退后的版本升级流程")
                        # 后续加入断链接口
                        self._handle_version_upgrade(version)
                    elif self.subtype == "online_test":
                        logger.info("进入回退后的online_test流程")
                        # 放测试队列
                        if self._handle_env_check():
                            self._handle_online_test()
                        else:
                            return
                    else:
                        self._send_email(
                            subject=f"{self.env_id}不存在的子类型",
                            content=f"线上回退时发生错误:\n任务ID: {self.task_id}\n版本: {version}"
                        )
                        raise NotImplementedError("Subclasses should implement this method.")
                else:
                    return
            elif roll_back_res.data.status.value in ["失败", "异常"]:
                self._send_email(
                    subject=f"{self.env_id}版本回退失败",
                    content=f"版本回退失败\n最后测试版本: {version},message:{roll_back_res.data.output.get('message')}"
                )
                trace_info = f"{self.env_id}版本回退失败,最后测试版本: {version},message:{roll_back_res.data.output.get('message')}"
                self._update_task_status(self.task_id, self.cron_task_id, roll_back_res.data.output.get('message'), self.env_id, trace_info)
                return
            else:
                logger.info("version_rollback is still building, will check again later")
                return

    def get_verison_rollback_state(self):
        pipelineUuid = self.needed.get("pipelineUuid", "")
        pipelineStageId = self.needed.get("pipelineStageId", "")
        recordId = self.needed.get("recordId", "")
        # 发送消息
        body = {
            "pipelineUuid": pipelineUuid,
            "pipelineStageId": pipelineStageId,
            "recordId": recordId
        }
        result = ActionService().get_execution_info(body)
        return result
    def query_fullpath(self,version):
        result = self._es_version_env.query_version_list(self.task_id)
        if result:
            version_list = eval(result[0].get("version_list",[]))
        else:
            self._send_email(
                subject=f"{self.env_id}没有对应的taskid任务",
                content=f"查询version_list失败{self.task_id}"
            )
            return ""
        fulluri = ""
        for version_dict in version_list:
            for versionstr, full_path in version_dict.items():
                if versionstr == version:
                    fulluri+=full_path
        return fulluri
    def _handle_version_upgrade(self, version):
        fulluri = self.query_fullpath(version)
        if not fulluri:
            self._send_email(
                subject=f"{self.env_id}没有对应fullpath",
                content=f"发送版本升级失败\n最后测试版本: {version}"
            )
            return
        else:
            fulluri = fulluri + "/01-gNB/litepass/PKG/" + version + ".tar"
            logger.info(
                f"fulluri:{fulluri}")
        umeres = UmeService().version_upgrade(self.env_id, fulluri)
        if not umeres.result:
            # 发送邮件
            self._send_email(
                subject=f"{self.env_id}发送版本升级失败",
                content=f"发送版本升级失败\n最后测试版本: {version}"
            )
            return
        else:
            pipelineStageId = umeres.data.pipelineStageId
            pipelineUuid = umeres.data.pipelineUuid
            recordId = umeres.data.recordId
            self.save_local_test_task(self.task_id, self.env_id, version, "version_upgrade", "online_test",
                                      self.job_name, self.build_number,
                                      pipelineUuid, pipelineStageId, recordId)
        logger.info(
            f"版本回退定时任务：Generated next version for version_upgrade testing: {version}, task_id :{self.task_id}")

    def _handle_env_check(self):
        ems = EnvMonitorService({"env_id": self.env_id})
        device_info_list = ems.get_env_device_info()
        for device_info in device_info_list:
            for tool in ["query_ue_com", "query_ue_ipconfig", "query_pc_net", "query_ume_alarm", "query_me_connect"]:
                try:
                    result = ems.execute_tool(device_info, tool)
                    if not result:
                        trace_info = f"环境ID: {ems.document.get('env_id')}\n工具名称: {tool}\n执行结果: 失败"
                        self.save_log("env_check", "failure", trace_info)
                        return False
                except:
                    trace_info = f"环境ID: {ems.document.get('env_id')}\n工具名称: {tool}\n执行结果: 失败"
                    self.save_log("env_check", "failure", trace_info)
                    return False
        trace_info = f"环境ID: {ems.document.get('env_id')}\n环境检查成功"
        self.save_log("env_check","success",trace_info)
        return True

    def save_log(self,service_key, status, trace_info):
        LoggingService.log_execution(
            service_type="log_save",
            operation="log_save",
            status=status,
            task_id=self.task_id,
            env_id=self.env_id,
            details={"trace_info": trace_info},
            service_key=service_key
        )
    def _handle_online_test(self):
        msg = LogJobInfo(
            log_name=self.needed.get("log_name", ""),
            job_name=self.job_name,
            build_number=self.build_number,
            env_id=self.env_id,
            task_id=self.task_id,
            service_type="online_test",
            subtype="rollback"
        )
        asyncio.run(CiAgentsQueue().push_info(msg))
        logger.info(f"版本回退定时任务：放入线上复测队列, task_id :{self.task_id}")

if __name__ == "__main__":
    fulluri = "https://artsz.zte.com.cn/artifactory/webapp/#/artifacts/browse/tree/General/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Main/NR-V4.20.20.20MainR150/NR-V4.20.20.20MainR150_2505271644"
    version = 'NR-V4.20.20.20MainR150_2505271644'
    fulluri = fulluri + "/01-gNB/litepass/PKG/" + version + ".tar"
    print(fulluri)
    # umeres = UmeService().version_upgrade("RAN3-上海高频CI团队-VAT1021",fulluri)
    # print(umeres)
    body = {
        "pipelineUuid": "186075813992615937",
        "pipelineStageId": "186075813992615936",
        "recordId": 1
    }
    result = ActionService().get_execution_info(body)
    print(result)