# encoding:utf-8
import os
import shutil
import zipfile
import asyncio

from infrastructure.utils.Dir import get_all_file_path_in_dir, get_target_files_in_dir
from infrastructure.utils.FileHandler import rename_path
from infrastructure.utils.Repeat import retries_on_flag
from infrastructure.logger.logger import logger


def get_name_list(file_path):
    with zipfile.ZipFile(file_path, 'r') as zipobj:
        # os.seq
        zipobj.extractall(file_path.split(".zip")[0])
        # path = rename_garbled_filenames(file_path.split(".zip")[0])
        file_path_list = get_all_file_path_in_dir(file_path.split(".zip")[0])
        # 写一个获取所有目录下的文件路径
        return file_path_list


def rename_file(filename: str) -> str:
    try:
        newfilename = filename.encode('cp437').decode('gbk')
    except:
        newfilename = filename.encode('utf-8').decode('utf-8')
    return newfilename


def rename(root_path, filename):
    try:
        oldpath = os.path.join(root_path, filename)
        try:
            newfilename = filename.encode('cp437').decode('gbk')
        except:
            newfilename = filename.encode('utf-8').decode('utf-8')
        newpath = os.path.join(root_path, newfilename)
        os.rename(oldpath, newpath)
    except Exception as e:
        print('error:', e)


# unzip遇到中文会乱码
def rename_garbled_filenames(path):
    for item in os.listdir(path):
        path_temp = os.path.join(path, item)
        if os.path.isdir(path_temp):
            rename_garbled_filenames(path_temp)
        rename(path, item)


def get_target_files_in_zip(filePath: str, pat: str) -> list[str]:
    """ pat 参数说明参考 get_target_files_in_dir 函数的 pat 参数 """
    unzip_all_files(filePath)
    rename_garbled_filenames(filePath.split('.zip')[0])
    return get_target_files_in_dir(filePath.split('.zip')[0], pat=pat)


def get_target_files_in_now_zip(filePath: str, pat: str, isUseTempPath=False) -> list[str]:
    dirPath = os.path.join(os.path.dirname(filePath), 'temp')
    with zipfile.ZipFile(filePath, 'r') as zip_ref:
        for file in zip_ref.namelist():
            zip_ref.extract(file, dirPath)
    rename_garbled_filenames(dirPath)
    if not isUseTempPath:
        dirPath = filePath.split('.zip')[0]
        shutil.rmtree(dirPath, True)
        rename_path(os.path.join(os.path.dirname(filePath), 'temp'), dirPath)
    return get_target_files_in_dir(dirPath, pat)


def unzip_all_files(filePath: str) -> None:
    with zipfile.ZipFile(filePath, 'r') as zip_ref:
        dirPath = filePath.split('.zip')[0]
        zip_ref.extractall(dirPath)
    fileList = get_target_files_in_dir(dirPath, pat='*.zip')
    for zipFilePath in fileList:
        unzip_all_files(zipFilePath)


def zip_folder(folderPath: str, isDeleteFolder: bool | None = True):
    zipPath = folderPath + ".zip"
    # 创建zip文件，并指定压缩级别
    with zipfile.ZipFile(zipPath, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # os.walk遍历文件夹
        for root, dirs, files in os.walk(folderPath):
            for file in files:
                # 创建在zip文件中的路径
                filePath = os.path.join(root, file)
                inZipPath = os.path.relpath(filePath, folderPath)
                # 将文件添加到zip文件中
                zipf.write(filePath, inZipPath)
    if isDeleteFolder: shutil.rmtree(folderPath)
    return zipPath
