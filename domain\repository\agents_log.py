from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document
from datetime import datetime


@Singleton
class EsAgentsLog(Document):
    """
    Elasticsearch repository for storing agent execution logs
    """
    def __init__(self):
        Document.__init__(self, 'agents_log', 'agents_log')

    def log_execution(self, service_type, operation, status, details=None, task_id=None, env_id=None, service_key=None):
        """
        Log an execution event to Elasticsearch

        Args:
            service_type (str): Type of service (e.g., 'jenkins_job_save', 'log_analysis')
            operation (str): Operation being performed
            status (str): Status of the operation ('success', 'failure', 'in_progress')
            details (dict, optional): Additional details about the operation
            task_id (str, optional): Associated task ID
            env_id (str, optional): Associated environment ID
            service_key (str, optional): The key from AGENTS_SERVICE_OBJECT

        Returns:
            dict: Result of the Elasticsearch index operation
        """
        log_entry = {
            "service_type": service_type,
            "operation": operation,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "details": details or {},
        }

        # Always include task_id and env_id in the log, even if they're None
        # This makes it easier to query and filter logs
        log_entry["task_id"] = task_id
        log_entry["env_id"] = env_id

        # Include the service key from AGENTS_SERVICE_OBJECT if provided
        if service_key:
            log_entry["service_key"] = service_key

        return self.index(id=datetime.now().isoformat(), body=log_entry)

    def get_logs_by_service_type(self, service_type, limit=100):
        """
        Get logs for a specific service type

        Args:
            service_type (str): Type of service to filter by
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        query = {
            "query": {
                "match": {
                    "service_type": service_type
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_task_id(self, task_id, limit=100):
        """
        Get logs for a specific task ID

        Args:
            task_id (str): Task ID to filter by
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        query = {
            "query": {
                "match": {
                    "task_id": task_id
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_env_id(self, env_id, limit=100):
        """
        Get logs for a specific environment ID

        Args:
            env_id (str): Environment ID to filter by
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        query = {
            "query": {
                "match": {
                    "env_id": env_id
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_status(self, status, limit=100):
        """
        Get logs with a specific status

        Args:
            status (str): Status to filter by ('success', 'failure', 'in_progress')
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        query = {
            "query": {
                "match": {
                    "status": status
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_service_key(self, service_key, limit=100):
        """
        Get logs for a specific service key

        Args:
            service_key (str): Service key to filter by
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        query = {
            "query": {
                "match": {
                    "service_key": service_key
                }
            },
            "sort": [
                {"timestamp": {"order": "desc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_task_ids(self, task_ids, limit=10000):
        """
        根据task_id列表获取日志

        Args:
            task_ids (list): Task ID列表
            limit (int, optional): Maximum number of logs to return

        Returns:
            tuple: (total_count, logs)
        """
        if not task_ids:
            return 0, []

        query = {
            "query": {
                "terms": {
                    "task_id": task_ids
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": limit
        }

        return self.search(body=query)

    def get_logs_by_time_range(self, start_date, end_date, env_id=None, limit=10000):
        """
        根据时间范围查询日志

        Args:
            start_date (str): 开始日期 YYYY-MM-DD
            end_date (str): 结束日期 YYYY-MM-DD
            env_id (str, optional): 环境ID过滤
            limit (int): 返回记录数量限制

        Returns:
            tuple: (total_count, logs)
        """
        must_match = []

        # 时间范围过滤
        time_range = {
            "range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00",
                    "lte": f"{end_date}T23:59:59"
                }
            }
        }
        must_match.append(time_range)

        # 环境ID过滤
        if env_id:
            must_match.append({"match_phrase": {"env_id": env_id}})

        query = {
            "query": {
                "bool": {
                    "must": must_match
                }
            },
            "sort": [
                {"timestamp": {"order": "asc"}}
            ],
            "size": limit
        }

        return self.search(body=query)
