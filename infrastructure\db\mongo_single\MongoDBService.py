from bson import ObjectId

from config.AppSettings import settings
from infrastructure.Singleton import Singleton
from infrastructure.db.mongo_single.MongoDBHandler import MongoDbHandler
from infrastructure.logger.logger import logger


@Singleton
class MongoDBService(object):
    def __init__(self, db={}):
        if not db:
            db = settings.env_conf["db_mongo"]
        self._db = MongoDbHandler(db)

    def is_table_exist(self, tablename):
        return self._db.is_table_exist(tablename)

    def is_index_exist(self, index, collection):
        return self._db.is_index_exist(index, collection)

    def get_all_index_name(self, collection):
        return self._db.get_all_index(collection)

    def create_index(self, index, expireAfterSeconds=None):
        self._db.create_index(index, expireAfterSeconds)

    def update_many(self, filter, attr_dict, tablename):
        return self._db.update_many(filter, attr_dict, tablename)

    def update_one(self, filter, attr_dict, tablename, upsert=False):
        return self._db.update_one(filter, attr_dict, tablename, upsert)

    def insert_many(self, attr_dicts, table_name):
        return self._db.insert_many(attr_dicts, table_name)

    def query(self, condtion, table_name):
        return self._db.query(condtion, table_name)

    def query_with_constraint(self, condition: dict, collection: str, constraint: dict = {}):
        return self._db.query_with_constraint(condition, collection, constraint)

    def count(self, condtion, table_name):
        return self._db.count(condtion, table_name)

    def update(self, attrDict):
        if '_id' in attrDict:
            objectId = ObjectId(attrDict.pop('_id'))
            queryResult = self._db.query_with_constraint({'_id': objectId}, collection=self._collection,
                                                         constraint={"_id": 1})
            if len(queryResult) != 0:
                return self._db.update({'_id': ObjectId(queryResult[0]["_id"])}, attrDict, self._collection)
            else:
                logger.error('ObjectId Not Exist %s: %s' % (self.__class__, attrDict))
                return None
        return self._db.save(attrDict, self._collection)
