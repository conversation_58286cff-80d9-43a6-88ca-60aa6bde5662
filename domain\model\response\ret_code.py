from dataclasses import dataclass
from domain.model.enum.exception_enum import ExceptionEnum


@dataclass
class RetCode:
    code: str = ExceptionEnum.SUCCESS.code
    msg: str = ExceptionEnum.SUCCESS.msg
    msgId: str = ExceptionEnum.SUCCESS.name

    @classmethod
    def from_exception_enum(cls, enum: ExceptionEnum, msg: str = None):
        return cls(
            code=enum.code,
            msg=msg if msg else enum.msg,
            msgId=enum.name
        )
