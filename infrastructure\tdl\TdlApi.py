# -*-coding:utf-8-*-
import json
import sys
import os
sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))
from infrastructure.tdl.Tdl import Tdl


class ActionApi:
    @staticmethod
    def execute(data):
        result = json.loads(Tdl().adapt_and_execute_cmd("action.execute", {"body": data}).text)
        return result
    
    @staticmethod
    def get_execution_info(data):
        result = json.loads(Tdl().adapt_and_execute_cmd("action.get_execution_info", data).text)
        return result

    @staticmethod
    def get_execution_logs(data):
        result = json.loads(Tdl().adapt_and_execute_cmd("action.get_execution_logs", data).text)
        return result


class EnvConfigApi:
    @staticmethod
    def get_historical_config(data):
        result = json.loads(Tdl().adapt_and_execute_cmd("env_config.get_historical_config", data).text)
        return result


if __name__ == '__main__':
    action_data = {
    "actionId": "86db0578995996b712f1ee580c393c83",
    "para": {},
    "envId": "RAN3-上海高频CI团队-VAT1014",
    "config": {
        "UME-10_226_209_230": {
            "attr": {
                "ip": "**************",
                "port": "28001",
                "username": "g5_ume_ci",
                "password": "Ume_5gNr_Ci",
                "dataAreaId": "VAT1014"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "UME-10_226_209_230",
            "type": "UME"
        },
        "GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "ip": "*************",
                "meId": "8110",
                "gnbId": "8110",
                "subNetwork": "16016",
                "plmn": "460-11",
                "plmnList": [
                    "460-11"
                ],
                "gnbIdLength": "24"
            },
            "links": [
                "UME-10_226_209_230",
                "CELL-26-GNB-8110-460-11-10_230_21_100",
                "CELL-28-GNB-8110-460-11-10_230_21_100",
                "CELL-25-GNB-8110-460-11-10_230_21_100",
                "CELL-27-GNB-8110-460-11-10_230_21_100"
            ],
            "id": "GNB-8110-460-11-10_230_21_100",
            "type": "GNB"
        },
        "CELL-26-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "26",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27150.0",
                "pci": "26",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-26-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-28-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "28",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27349.92",
                "pci": "28",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-28-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-25-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "25",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27050.04",
                "pci": "25",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-25-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-27-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "27",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27249.96",
                "pci": "27",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-27-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "UE-1-5126-10_230_69_122": {
            "attr": {
                "ermsSerialNo": 1,
                "controlIp": "*************",
                "controlPort": "5126",
                "serviceMgrPort": "",
                "ueId": "",
                "ueType": "HFQUALCOMM",
                "name": "CPE-高频",
                "telphoneNumber": None,
                "ueAttr": {
                    "cpeId": "1",
                    "imsi": "460087960001536",
                    "ues": [
                        "1"
                    ],
                    "ueGroups": [
                        "1"
                    ],
                    "workDir": ""
                }
            },
            "links": [],
            "id": "UE-1-5126-10_230_69_122",
            "type": "UE"
        },
        "BizCode": {
            "ip": "",
            "port": "",
            "type": "BizCode"
        }
    },
    "refs": [],
    "actionType": "composite"
}
    execute_result = ActionApi.execute(action_data)
    print(f"action execute ret: {execute_result}")

    execute_info_data = {
        "recordId": execute_result.get("data").get("recordId"),
        "pipelineStageId": execute_result.get("data").get("pipelineStageId"),
        "pipelineUuid": execute_result.get("data").get("pipelineUuid")
    }

    execute_info = ActionApi.get_execution_info(execute_info_data)
    print(f"action execute info: {execute_info}")

    execute_logs_data = {
        "pipelineStageId": execute_result.get("data").get("pipelineStageId"),
        "recordId": execute_result.get("data").get("recordId"),
        "type": "nested"
    }

    execute_logs = ActionApi.get_execution_logs(execute_logs_data)
    print(f"action execute logs: {execute_logs}")

    historical_config_data = {
        "envId": "RAN3-上海高频CI团队-VAT1014",
        "groupNo": ""
    }
    historical_config = EnvConfigApi.get_historical_config(historical_config_data)
    print(f"action historical config: {historical_config}")



