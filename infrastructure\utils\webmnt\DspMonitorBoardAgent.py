# -*- coding:utf-8 -*
import telnetlib
import time
import re
import struct

from .BaseMonitor import *
from .DspMonitorTelnet import *


class ChipOPAgent(OPAgent):
    def __init__(self, boardname, boardid, cfg, corescfg, monitor, coretype_list, checkflag, initConfig):
        OPAgent.__init__(self, monitor)
        self.boardname = boardname
        self.boardid = boardid
        self.coredirect = {}
        self.coreprotect = {}
        self.cfg = cfg
        self.mapstatus = {}
        self.corescfg = corescfg
        self.coretype_list = coretype_list
        self.curfun = 0xffff
        self.curcore = 0xffff
        self.BrdName = lambda name: " -boardname %s " % str(name)
        self.BrdId = lambda id: "-boardid %s" % str(id)
        self.CoreId = lambda id: "-coreid %s" % str(id)
        self.FunId = lambda id: "-funid %s" % str(id)
        self.MemAddr = lambda addr: "-mem %s" % str(addr)
        self.Memlen = lambda size: "-len %s" % str(size)
        self.DataFile = lambda f: "-file %s" % str(f)
        self.Direct = lambda flag: "-direct %s" % str(flag)
        self.Protect = lambda flag: "-protect %s" % str(flag)
        self.Value = lambda v: "-value %s" % str(v)
        self.Var = lambda var: "-var %s" % str(var)
        self.Map = lambda map: " -map  %s" % str(map)
        self.Out = lambda out: " -out %s" % str(out)
        self.Module = lambda name: "-module %s" % str(name)
        self.Level = lambda level: "-level %s" % str(level)
        self.Start = lambda flag: "-start %s" % str(flag)
        self.BrdSet = lambda brdname: "-board %s" % str(brdname)
        self.FilterSet = lambda filter: "-filter %s" % str(filter)
        self.StateSet = lambda condition: "-condition %s" % str(condition)
        self.funname = lambda fun: "-funname %s" % str(fun)
        self.num = lambda num: "-num %s" % str(num)
        self.savename = lambda file: "-save %s" % str(file)
        self.event = lambda event: "-event %s" % str(event)
        self.sname = lambda fun: "-startname %s" % str(fun)
        self.sline = lambda fun: "-startline %s" % str(fun)
        self.ename = lambda fun: "-endname %s" % str(fun)
        self.eline = lambda fun: "-endline %s" % str(fun)
        self.ptype1 = lambda fun: "-passbacktype1 %s" % str(fun)
        self.ptype2 = lambda fun: "-passbacktype2 %s" % str(fun)
        self.paddr1 = lambda fun: "-passbackaddr1 %s" % str(fun)
        self.paddr2 = lambda fun: "-passbackaddr2 %s" % str(fun)
        self.pmin1 = lambda fun: "-passbackmin1 %s" % str(fun)
        self.pmax1 = lambda fun: "-passbackmax1 %s" % str(fun)
        self.pmin2 = lambda fun: "-passbackmin2 %s" % str(fun)
        self.pmax2 = lambda fun: "-passbackmax2 %s" % str(fun)
        self.pmuType = lambda fun: "-pmutype %s" % str(fun)
        self.triggermode = lambda fun: "-triggermode %s" % str(fun)
        self.triggertype = lambda fun: "-triggertype %s" % str(fun)
        self.bustype = lambda fun: "-bustype %s" % str(fun)
        self.addr = lambda fun: "-addr %s" % str(fun)
        self.datatypeOrLen = lambda fun: "-datatypeOrLen %s" % str(fun)
        self.taskIndex = lambda fun: "-taskIndex %s" % str(fun)
        self.statusmap = {}
        self.core_width = {}
        self.needCheckCoffFlag = checkflag
        self.ToolType = 'webMnt'
        self.initConfig = initConfig
        self._initboard()

    def _StatusCheckrunner(self, cmdfun, checkfun, failmsg):
        res = eval(cmdfun)
        status = checkfun(res)
        count = 0
        while not status and count < STATUS_CHECK_COUNT:
            time.sleep(STATUS_CHECK_WAIT)
            res = eval(cmdfun)
            status = checkfun(res)
            count += 1
        if (not status):
            raise MonitorExcption(failmsg)

    def _runner(self, fun, interval, failmsg, busymsg):
        logging.info(fun)
        bbusy = False
        count = 0
        res = None
        while count < MONITOR_REPEAT_RUN_COUNT:
            logging.info("run time " + str(count))
            res = eval(fun)
            bbusy = self._isBusy(res)
            if (self._isSuccess(res) and (not bbusy)):
                break
            count += 1
            if (bbusy):
                time.sleep(interval)

        if not res:
            raise MonitorExcption(failmsg)

        if bbusy:
            raise MonitorExcption(busymsg)

        if (not self._isSuccess(res)):
            raise MonitorExcption(failmsg)

        return res

    def _initboard(self):
        result = self._FreshBoardType()
        if (not result):
            raise MonitorExcption("Fresh Board Fail")

        result = self._OpenBoard()
        if (not result):
            raise MonitorExcption("Open Board Fail")
        # time.sleep(5)

    #         for coreid in self.cfg.keys():
    #             self._loadmapex(coreid)
    #         for (coreid, item) in self.cfg.items():
    #             if (len(item) == 4 and item[2] != "" and item[3] != ""):
    #                 if (not os.path.exists(item[2])) or (not os.path.exists(item[3])):
    #                     raise MonitorExcption("config core %s map Fail,%s or %s is no exist" % (coreid, item[2], item[3]))
    #
    #                 result = self._ChangeMap(coreid, item[2], item[3])
    #
    #                 if (not result):
    #                     raise MonitorExcption("config core %s map Fail,%s %s" % (coreid, item[2], item[3]))

    def _loadmapex(self, coreid):
        # maplist = ['.map', '.lin', '.pdb']
        outlist = ['.out', '.eld', '.exe', '.elf', '.axf', '.a']
        if (coreid in self.mapstatus):
            logging.info("core %d has configed" % coreid)
            return
        item = self.cfg[coreid]

        # webmnt默认进行自动加载，失败后手动加载；type. 0：手动配置；1：自动配置
        if self._ChangeMapAuto(coreid):
            result, configType = True, 1
        elif int(self.initConfig.get(MANUAL_LOAD_MAP_SWITCH, 1)) != 0:
            result, configType = self._ChangeMap(coreid, item[2], item[3]), 0
        if not result:
            raise MonitorExcption("config core %s map Fail,%s %s" % (coreid, item[2], item[3]))
        self.mapstatus[coreid] = configType

    def enableDirect(self, coreid, benable):
        self.coredirect[coreid] = DIRECT if benable else NODIRECT

    def enableProtect(self, coreid, benable):
        self.coreprotect[coreid] = PROTECT if benable else NOPROTECT

    def _endian(self, core):
        coresendian = []
        for item in self.corescfg:
            coresendian += [item[2], ] * item[0]
        return coresendian[core] if (core >= 0 and core < len(coresendian)) else BIG_END

    def isBigEndian(self, core):
        if BIG_END == self._endian(core):
            return True
        return False

    def get_bit_width(self, chip_type):
        if chip_type in self.core_width.keys():
            return self.core_width[chip_type]
        cmd = "ClientGetBitWide -chiptype %s" % chip_type
        res = self.run(cmd)
        bit = res.split('\r\n')[0]
        bit = bit.split(' ')[3]
        bit = int(bit)
        self.core_width[chip_type] = bit
        return bit

    def _cpuArc(self, core):
        coreArclist = []
        for item in self.corescfg:
            if len(item) > 3:
                coreArclist += [item[3], ] * item[0]
            else:
                chip_type = item[1]
                bit_width = self.get_bit_width(chip_type)
                coreArclist += [bit_width, ] * item[0]
        return coreArclist[core] if (core >= 0 and core < len(coreArclist)) else CPU_ARC_32

    def isArc32Cpu(self, core):
        if CPU_ARC_32 == self._cpuArc(core):
            return True
        return False

    def __getdirect(self, coreid):
        if (int(coreid) in list(self.cfg.keys())):
            return DEFAULT_LINK_DIRECT[self.cfg[int(coreid)][1]]
        return DEFAULT_LINK_DIRECT[CPU_AGENT]

        # coretypes = []
        # curcoretype = "unknown"
        # for item in self.corescfg:
        #     coretypes += [item[1], ] * item[0]
        #
        # if (coreid < len(coretypes)):
        #     curcoretype = coretypes[coreid]
        #
        # if (curcoretype in DEFAULT_CORES_DIRECT.keys()):
        #     return DEFAULT_CORES_DIRECT[curcoretype]

    def _isDirect(self, coreid):
        if (coreid in list(self.coredirect.keys())):
            return self.coredirect[coreid]
        else:
            return self.__getdirect(coreid)

        #     # coretypes = []
        #     # curcoretype = "unknown"
        #     # for item in self.corescfg:
        #     #     coretypes += [item[1], ] * item[0]
        #     #
        #     # if (coreid < len(coretypes)):
        #     #     curcoretype = coretypes[coreid]
        #     #
        #     # if (curcoretype in DEFAULT_CORES_DIRECT.keys()):
        #     #     return DEFAULT_CORES_DIRECT[curcoretype]
        #
        # raise MonitorExcption("core type error ,no config %s %d %s " % ( self.boardname,coreid,curcoretype))

    def _isProtect(self, coreid):
        return self.coreprotect[coreid] if (coreid in list(self.coreprotect.keys())) else NOPROTECT

    def _isBusy(self, res):
        if (not res):
            return False
        return False if -1 == str(res).find("Cmd is Except") else True

    def _isSuccess(self, res):
        if (not res):
            return False
        return False if -1 == str(res).find("~Success~") else True

    def _checkres(self, fmt, res):
        if (not fmt or not res):
            return False
        if (not self._isSuccess(res)):
            return False
        return True if re.search(fmt, res, re.M | re.I) else False

    def _CheckChangeFunc(self, res):
        return self._checkres("Fun\s+change\s+ok", res)

    def _CheckChangeCore(self, res):
        return self._checkres("Core\s+change\s+ok", res)

    def _CheckLinkStatus(self, res):
        return self._checkres("Link\s+status\s+1", res)

    def _CheckCoffStatus(self, res):
        return self._checkres("Coff\s+status\s+1", res)

    def _CheckEnableStatus(self, res):
        return self._checkres("Enable\s+status\s+1", res)

    def _FreshBoardType(self):
        cmd = "ClientFreshBoardType -boardname %s -boardid %s" % (str(self.boardname), str(self.boardid))
        res = self.run(cmd)
        return self._checkres("ok", res)
        # return self._checkres("Fresh\s+board\s+Type\s+ok", res)

    def _OpenBoard(self):
        cmd = "ClientOpenBoard -boardname %s -boardid %s" % (str(self.boardname), str(self.boardid))
        res = self.run(cmd)
        return self._checkres("Open\s+board\s+ok", res)

    def _ChangeMapAuto(self, coreid):
        cmd = "ClientChangeMap  -boardname %s -boardid %s -coreid %s -out %s" % (
            str(self.boardname), str(self.boardid), str(coreid), "auto")
        res = self.run(cmd)
        return self._checkres("Load\s+Map\s+ok", res)

    def _ChangeMap(self, coreid, map, out):
        cmd = "ClientChangeMap  -boardname %s -boardid %s -coreid %s  -map  %s -out %s" % (
            str(self.boardname), str(self.boardid), str(coreid), str(map), str(out))
        res = self.run(cmd)
        return self._checkres("Load\s+Map\s+ok", res)

    def _ChangeMap1(self, coreid, map, out):  # log解析新加分支
        cmd = "ClientChangeMap  -boardname %s -boardid %s -coreid %s  -map  %s -out %s" % (
            str(self.boardname), str(self.boardid), str(coreid), str(map), str(out))
        return self.run(cmd)

    def _ParseLog(self, coreid, logfile):
        cmd = "ClientLogParseSet -boardname %s -boardid %s -coreid %s -logfile %s" % (
            str(self.boardname), str(self.boardid), str(coreid), str(logfile))
        cmd = cmd.replace("/", "\\")
        return self.run(cmd)

    def _ChangeCore(self, coreid):
        cmd = "ClientChangeCore -boardname %s -boardid %s -coreid %s" % (
            str(self.boardname), str(self.boardid), str(coreid))
        return self.run(cmd)

    def _ChangeFunc(self, coreid, funid):
        cmd = "ClientChangeFunc -boardname %s -boardid %s -coreid %s -funid %s" % (
            str(self.boardname), str(self.boardid), str(coreid), str(funid))
        return self.run(cmd)

    def _GetLinkStatus(self, coreid):
        cmd = "ClientGetLinkStatus -boardname %s -boardid %s -coreid %s" % (
            str(self.boardname), str(self.boardid), str(coreid))
        return self.run(cmd)

    def _GetCoffStatus(self, coreid):
        cmd = "ClientGetCoffStatus  -boardname %s -boardid %s -coreid %s" % (
            str(self.boardname), str(self.boardid), str(coreid))
        return self.run(cmd)

    def _GetEnableStatus(self, coreid, funid):
        cmd = "ClientGetEnableStatus -boardname %s -boardid %s -coreid %s -funid %s" % (
            str(self.boardname), str(self.boardid), str(coreid), str(funid))
        return self.run(cmd)

    def gencmd(self, name, coreid, *params):
        cmd = "%s -boardname %s -boardid %s -coreid %s {params} -direct %s -protect %s" % (
            name, str(self.boardname), str(self.boardid), str(coreid), str(self._isDirect(coreid)),
            str(self._isProtect(coreid)))
        return cmd.format(params=" ".join(params))

    def gencmd_log(self, name, coreid, *params):
        cmd = "%s -boardname %s -boardid %s -coreid %s {params}" % (
            name, str(self.boardname), str(self.boardid), str(coreid))
        return cmd.format(params=" ".join(params))

    def _getVarInfo0(self, coreid, var):
        if (not self._VarRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientGetVarInfo", coreid, self.Var(var))
        return self.run(cmd)

    def _getVarChildrenNameList0(self, coreid, var):
        if (not self._VarRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientGetVarChildrenNameList", coreid, self.Var(var))
        return self.run(cmd)

    def _ReadMemtoFile(self, coreid, mem, len, file):
        if (not self._MemRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientReadMemtoFile", coreid, self.MemAddr(mem), self.Memlen(len), self.DataFile(file))
        return self.run(cmd, 60000)

    def _WriteFiletoMem(self, coreid, mem, file):
        if (not self._MemRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientWriteFiletoMem", coreid, self.MemAddr(mem), self.DataFile(file))
        return self.run(cmd, 60000)

    def _ReadMemNB(self, coreid, mem, len):
        if (not self._MemRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientReadMemNB", coreid, self.MemAddr(mem), self.Memlen(len))
        return self.run(cmd, 100)

    def _WriteMemNB(self, coreid, mem, len, value):
        if (not self._MemRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientWriteMemNB", coreid, self.MemAddr(mem), self.Memlen(len), self.Value(value))
        return self.run(cmd, 100)

    def _ReadVar(self, coreid, var):
        if (not self._VarRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientReadVar", coreid, self.Var(var))
        return self.run(cmd)

    def _WriteVar(self, coreid, var, value):
        if (not self._VarRWCheck(coreid)):
            raise Exception("status error test stop")
        cmd = self.gencmd("ClientWriteVar", coreid, self.Var(var), self.Value(value))
        return self.run(cmd)

    def _PcAddrParse(self, coreid, addr):
        if not self._VarRWCheck(coreid):
            raise Exception("status error test stop")
        if self.ToolType == "webMnt" and self._autoMapCheck(coreid):
            raise Exception("PcAddrParse can not use AutoParseMap. Please parse Map manually.")
        cmd = self.gencmd("ClientPcAddrParse", coreid, self.MemAddr(addr))
        res = self.run(cmd)
        return res

    def _LogGetModul(self, coreid):
        # webMnt 未加载map或者是自动加载的map时，转手动加载本地map
        if not coreid in self.mapstatus or self.mapstatus[coreid] == 1:
            item = self.cfg[coreid]
            result = self._ChangeMap(coreid, item[2], item[3])
            if (not result):
                raise MonitorExcption("config core %s map Fail,%s %s" % (coreid, item[2], item[3]))
            self.mapstatus[coreid] = 0
        cmd = self.gencmd_log('ClientLogGetModul', coreid)
        return self.run(cmd)

    def _LogLevelSet(self, coreid, modulName, modulLevel):
        cmd = self.gencmd_log('ClientLogLevelSet', coreid, self.Module(modulName), self.Level(modulLevel))
        return self.run(cmd)

    def _LogStart(self, coreid, flag):
        cmd = self.gencmd_log('ClientLogStart', coreid, self.Start(flag))
        return self.run(cmd)

    def _LogSave(self, coreid, file):
        cmd = self.gencmd_log('ClientLogSave2File', coreid, self.DataFile(file))
        return self.run(cmd)

    def _LogExtBrdSet(self, coreid, boardName):
        cmd = self.gencmd_log('ClientLogExtBoardSet', coreid, self.BrdSet(boardName))
        return self.run(cmd)

    def _LogExtFilter(self, coreid, filterName):
        cmd = self.gencmd_log('ClientLogExtFilterSet', coreid, self.FilterSet(filterName))
        return self.run(cmd)

    def _LogExtConditionSet(self, coreid, condition):
        cmd = self.gencmd_log('ClientLogExtConditionSet', coreid, self.StateSet(condition))
        return self.run(cmd)

    def _LogExtLevelSet(self, coreid, modulName, modulLevel):
        cmd = self.gencmd_log('ClientLogExtLevelSet', coreid, self.Module(modulName), self.Level(modulLevel))
        return self.run(cmd)

    def _LogExtStart(self, coreid, flag):
        cmd = self.gencmd_log('ClientLogExtStart', coreid, self.Start(flag))
        return self.run(cmd)

    def _LogExtSave(self, coreid, file):
        cmd = self.gencmd_log('ClientLogExtSave2File', coreid, self.DataFile(file))
        return self.run(cmd)

    def _DciInit(self, coreid):
        cmd = self.gencmd_log('ClientDciInit', coreid)
        return self.run(cmd)

    def _DciTaskAdd(self, coreid, triggermode, triggertype, bustype, addr, datatypeOrLen):
        cmd = self.gencmd_log('ClientDciTaskAdd', coreid, self.triggermode(triggermode), self.triggertype(triggertype), self.bustype(bustype), self.addr(addr), self.datatypeOrLen(datatypeOrLen))
        return self.run(cmd)

    def _DciTaskTriggerInfo(self, coreid):
        cmd = self.gencmd_log('ClientDciTaskTriggerInfo', coreid)
        return self.run(cmd)

    def _parseDciTaskInfo(self, res):
        p = re.compile("taskIndex:(.*?),.*\nreturnValue:(.*?),.*\n+", re.M | re.I)
        m = p.search(res)
        taskIndex = -1
        Status = False
        if m:
            taskIndex = m.group(1)
            Status = False if -1 == str(res).find("~Success~") else True
        return taskIndex, Status

    def _DciTaskDel(self, coreid, taskIndex):
        cmd = self.gencmd_log('ClientDciTaskDel', coreid, self.taskIndex(taskIndex))
        return self.run(cmd)

    def _DciClose(self, coreid):
        cmd = self.gencmd_log('ClientDciClose', coreid)
        return self.run(cmd)

    def DciInit(self, coreid):
        res = self._runner("self._DciInit({})".format(coreid),
                           MONITOR_CMD_WAIT_TIME,
                           "DciInit Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def DciTaskAdd(self, coreid, triggermode, triggertype, bustype, addr, datatypeOrLen):
        res = self._runner("self._DciTaskAdd({}, \"{}\", \"{}\", \"{}\", \"{}\", \"{}\")".format(coreid, triggermode, triggertype, bustype, addr, datatypeOrLen),
                           MONITOR_CMD_WAIT_TIME,
                           "DciTaskAdd Fail",
                           "Monitor is busy all the time,stop read")
        return self._parseDciTaskInfo(res)

    def DciTaskTriggerInfo(self, coreid):
        res = self._runner("self._DciTaskTriggerInfo({})".format(coreid),
                           MONITOR_CMD_WAIT_TIME,
                           "DciTaskTriggerInfo Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)
    def DciTaskDel(self, coreid, taskIndex):
        res = self._runner("self._DciTaskDel({}, \"{}\")".format(coreid, taskIndex),
                           MONITOR_CMD_WAIT_TIME,
                           "DciTaskDel Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def DciClose(self, coreid):
        res = self._runner("self._DciClose({})".format(coreid),
                           MONITOR_CMD_WAIT_TIME,
                           "DciClose Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def _autoMapCheck(self, core):
        # type. 0：手动配置；1：自动配置
        if (core in self.mapstatus) and self.mapstatus[core] == 1:
            return True
        return False

    def _StatusCheck(self, core, funid):
        self._loadmapex(core)
        # monitortype = "Java"
        # if monitortype=="Java":
        #     return True
        # self._StatusCheckrunner("self._ChangeCore({})".format(core),
        #                         self._CheckChangeCore,
        #                         "Change core %s fail" % str(core))

        self._StatusCheckrunner("self._ChangeFunc({},\"{}\")".format(core, funid),
                                self._CheckChangeFunc,
                                "Change fun %s Fail" % str(funid))

        self._StatusCheckrunner("self._GetLinkStatus({})".format(core),
                                self._CheckLinkStatus,
                                "core %s  is disconnect" % str(core))
        if self.needCheckCoffFlag:
            self._StatusCheckrunner("self._GetCoffStatus({})".format(core),
                                self._CheckCoffStatus,
                                "core %s axf&map no load" % str(core))

        # self._StatusCheckrunner("self._GetEnableStatus({},{})".format(core, funid),
        #                        self._CheckEnableStatus,
        #                        "core %s  is disable" % str(core))
        return True

    def _MemRWCheck(self, core):
        return self._StatusCheck(core, MEMRW)

    def _VarRWCheck(self, core):
        return self._StatusCheck(core, VARRW)

    def isComplexVarType(self, mask):
        if (mask == 0xffffffff):
            return False
        else:
            BaseType = mask & ((0x1 << VAR_BASICTYPE_LENGTH) - 1)
            if BaseType == 8 or BaseType == 9 or BaseType == 10:
                return True

            ExtType = mask >> VAR_BASICTYPE_LENGTH
            if (ExtType != 0):
                return True

            return False

    def _parseVarInfo(self, coreid, var, res):
        result = MonitorResult(self._endian(coreid), res)  # dict(Addr=None, Len=None, Status=False, Res=res)
        p = re.compile("Var\s+(.*?)\s+address\s+(0x\w+)\s+len\s+(\d+)\s+Type\s+(.*?)\s+Mask\s+(\d+)", re.M | re.I)
        m = p.search(res)
        result = MonitorResult(self._endian(coreid), res)  # dict(Addr=None, Len=None, Status=False, Res=res)
        if m:
            result.Var = m.group(1)
            result.Addr = int(str(m.group(2)), 16)
            result.Len = int(m.group(3))
            result.Status = True
            result.type = m.group(4).replace("struct", "")
            result.mask = int(m.group(5))
        return result

    def _parseVarChildrenNameList(self, coreid, var, res):
        result = []
        res = res.split('\n')[1]
        p = re.compile("var.+is not struct\(\*\) type!", re.M | re.I)
        m = p.search(res)
        if m:
            return result
        p = re.compile("\[(.+)]", re.M | re.I)
        m = p.search(res)
        if m:
            child = m.group(1)
            result = child.split(',')
            result = [name.strip() for name in result]
        return result

    def _parseWriteMemAck(self, coreid, res):  # new  ljnwarning
        result = MonitorResult(self._endian(coreid), res)
        memaddrp = re.compile("Write mem : (\w+),(\d+)", re.M | re.S)
        m = memaddrp.search(res)
        if 'Success' in res and m != None:
            result.Addr = int(m.group(1), 16)
            result.Len = int(m.group(2))
            result.Status = True
        return result

    def _parseReadMemAck(self, coreid, res):
        result = MonitorResult(self._endian(coreid), res)

        memaddrp = re.compile("Read Mem Addr: (0x\w+), len (\d+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = memaddrp.search(res)
        if (m):
            result.Addr = int(m.group(1), 16)
            result.Len = int(m.group(2))
            result.Status = True
            result.setData(m.group(3))
        return result

    def GetVarInfo(self, coreid, var):
        var = var.replace(" ", "")
        res = self._runner("self._getVarInfo0({},\"{}\")".format(coreid, var),
                           MONITOR_CMD_WAIT_TIME,
                           "Get Var Info fail",
                           "Monitor is busy all the time,stop get var info")
        return self._parseVarInfo(coreid, var, res)

    def GetVarChildrenNameList(self, coreid, var):
        var = var.replace(" ", "")
        res = self._runner("self._getVarChildrenNameList0({},\"{}\")".format(coreid, var),
                           MONITOR_CMD_WAIT_TIME,
                           "Get Var children name list fail",
                           "Monitor is busy all the time,stop get var info")
        return self._parseVarChildrenNameList(coreid, var, res)

    def _ReadMemNB0(self, coreid, mem, len):
        res = self._runner("self._ReadMemNB({},{},{})".format(coreid, mem, len),
                           MONITOR_CMD_WAIT_TIME,
                           "Read Memory Fail",
                           "Monitor is busy all the time,stop read")
        return self._parseReadMemAck(coreid, res)

    def isMemmEqual(self, coreid, value, mem, size):
        if (size not in list(Fmtdict.keys())):
            raise MonitorExcption("var len error %s not in [1,2,4,8]" % str(size))
        fmt = Fmtdict[size]

        endian = ">" if self.isBigEndian(coreid) else "<"
        fmt = endian + (Fmtdict[size] if (value > 0) else Fmtdict[size].lower())
        valuebuf = struct.pack(fmt, value)

        logging.info(struct.unpack(str(len(valuebuf)) + "B", valuebuf))
        logging.info(struct.unpack(str(len(mem)) + "B", mem))
        # struct.unpack(str(len(valuebuf))+"B",valuebuf)repr(mem))
        for i in range(0, size):
            if valuebuf[i] != mem[i]:
                return False
        return True

    def _WriteMemNB1(self, coreid, mem, len, value):  # 改写变量不回读校验直接返�?
        if (mem == 0):
            raise MonitorExcption("can`t write 0 addr")
        if (mem == 0xffffffff):
            raise MonitorExcption("can`t write 0xffffffff addr")
        if (len not in [1, 2, 4, 8]):
            raise MonitorExcption("Len error %s,Support 1,2,4,8")

        res = self._runner("self._WriteMemNB({},{},{},{})".format(coreid, mem, len, value),
                           MONITOR_CMD_WAIT_TIME,
                           "Write Memory Fail",
                           "Monitor is busy all the time,stop Write")

        return self._parseWriteMemAck(coreid, res)

    def _WriteMemNB0(self, coreid, mem, len, value):
        if (mem == 0):
            raise MonitorExcption("can`t write 0 addr")

        if (mem == 0xffffffff):
            raise MonitorExcption("can`t write 0xffffffff addr")

        if (len not in [1, 2, 4, 8]):
            raise MonitorExcption("Len error %s,Support 1,2,4,8")

        res = self._runner("self._WriteMemNB({},{},{},{})".format(coreid, mem, len, value),
                           MONITOR_CMD_WAIT_TIME,
                           "Write Memory Fail",
                           "Monitor is busy all the time,stop Write")
        monitorResult = MonitorResult(self._endian(coreid), res)
        monitorResult.Status = 'write end' in res
        return monitorResult

    def _varlen(self, varinfo, coreid):
        readlen = varinfo.Len
        if (varinfo.type.find("*") != -1):
            if (self.isArc32Cpu(coreid)):
                readlen = 4
            else:
                readlen = 8

        if (readlen not in [1, 2, 4, 8]):
            if (self.isComplexVarType(varinfo.mask)):
                return readlen
            else:
                raise MonitorExcption("only Support 1,2,4,8 bytes,curlen:" + str(varinfo.Len))
        else:
            return readlen

    def ReadMem(self, coreid, mem, len):
        logging.info("|||||||||||||||||||||||Begin ReadMemNB||||||||||||||||||")
        return self._ReadMemNB0(coreid, mem, len)

    def WriteMem(self, coreid, mem, len, value, flag=0):
        logging.info("|||||||||||||||||||||||Begin WriteMemNB||||||||||||||||||")
        if flag == 1:
            return self._WriteMemNB1(coreid, mem, len, value)
        else:
            return self._WriteMemNB0(coreid, mem, len, value)

    def ReadVar(self, coreid, var):
        logging.info("|||||||||||||||||||||||Begin ReadVar ||||||||||||||||||")
        logging.info("{} {}".format(coreid, var))
        if self.ToolType == "webMnt":
            # webmnt模式下不进行字符串的拆分
            varpathstack = [var]
        else:
            varpathstack = var.split("->")
            varpathstack.reverse()
        cur = varpathstack.pop()
        varinfo = self.GetVarInfo(coreid, cur)
        readlen = self._varlen(varinfo, coreid)
        addrAck = self._ReadMemNB0(coreid, varinfo.Addr, readlen)

        while len(varpathstack) > 0:
            cur = varpathstack.pop()
            curvar = "(%s)0x%08x.%s" % (varinfo.type, addrAck.Value, cur)
            varinfo = self.GetVarInfo(coreid, curvar)
            readlen = self._varlen(varinfo, coreid)
            addrAck = self._ReadMemNB0(coreid, varinfo.Addr, readlen)
        return addrAck

    def ReadVarInfo(self, coreid, var):
        logging.info("|||||||||||||||||||||||ReadVarInfo ||||||||||||||||||")
        logging.info("{} {}".format(coreid, var))
        return self.GetVarInfo(coreid, var)

    def ReadVarChildrenNameList(self, coreid, var):
        logging.info("|||||||||||||||||||||||ReadVarChildrenNameList ||||||||||||||||||")
        logging.info("{} {}".format(coreid, var))
        if '->' not in var:
            return self.GetVarChildrenNameList(coreid, var)

        varpathstack = var.split('->')
        varpathstack.reverse()
        cur = varpathstack.pop()
        varinfo = self.GetVarInfo(coreid, cur)
        readlen = self._varlen(varinfo, coreid)
        addrAck = self._ReadMemNB0(coreid, varinfo.Addr, readlen)
        curvar = ''
        while len(varpathstack) > 0:
            cur = varpathstack.pop()
            curvar = "(%s)0x%08x.%s" % (varinfo.type, addrAck.Value, cur)

            if len(varpathstack) != 0:
                varinfo = self.GetVarInfo(coreid, curvar)
                readlen = self._varlen(varinfo, coreid)
                addrAck = self._ReadMemNB0(coreid, varinfo.Addr, readlen)
        return self.GetVarChildrenNameList(coreid, curvar)

    def valueConvert(self, coreid, value, size):
        fmt = "I"
        endian = ">" if self.isBigEndian(coreid) else "<"
        if (size not in list(Fmtdict.keys())):
            raise MonitorExcption("var len error %s not in [1,2,4,8]" % str(len))
        fmt = endian + Fmtdict[size]
        if (value < 0):
            fmt = fmt.lower()
        return struct.unpack(Fmtdict[size], (struct.pack(fmt, value)))[0]

    def WriteVar(self, coreid, var, value, flag=0):
        logging.info("|||||||||||||||||||||||Begin WriteVar ||||||||||||||||||")
        logging.info("{} {} {} ".format(coreid, var, value))
        varpathstack = [var]
        cur = varpathstack.pop()
        varinfo = self.GetVarInfo(coreid, cur)
        readlen = self._varlen(varinfo, coreid)

        while len(varpathstack) > 0:
            addrAck = self._ReadMemNB0(coreid, varinfo.Addr, readlen)
            cur = varpathstack.pop()
            if 0 == addrAck.Value:
                raise MonitorExcption("value of pointer var is 0,you will write 0 addr")
            curvar = "(%s)0x%08x.%s" % (varinfo.type, addrAck.Value, cur)
            varinfo = self.GetVarInfo(coreid, curvar)
            readlen = self._varlen(varinfo, coreid)
        if flag == 1:
            return self._WriteMemNB1(coreid, varinfo.Addr, readlen, value)
        else:
            return self._WriteMemNB0(coreid, varinfo.Addr, readlen, value)

    def ReadMemtoFile(self, coreid, mem, len, file):
        logging.info("|||||||||||||||||||||||Begin ReadMemtoFile  ||||||||||||||||||")
        file = file.replace("/", "\\\\")
        cmd = "self._ReadMemtoFile({}, {}, {}, \"{}\")".format(coreid, mem, len, file)
        res = self._runner(cmd,
                           MONITOR_CMD_WAIT_TIME,
                           "read memory to file fail",
                           "Monitor is busy all the time,stop read mem to file")
        return self._isSuccess(res)

    def WriteFiletoMem(self, coreid, mem, file):
        file = file.replace("/", "\\\\")
        logging.info("|||||||||||||||||||||||Begin WriteFiletoMem  ||||||||||||||||||")
        cmd = "self._WriteFiletoMem({}, {}, \"{}\")".format(coreid, mem, file)
        res = self._runner(cmd,
                           MONITOR_CMD_WAIT_TIME,
                           "write memory to file fail",
                           "Monitor is busy all the time,stop write file to mem")
        return self._isSuccess(res)

    def LogGetModul(self, coreid):
        logging.info("|||||||||||||||||||||||Begin LogPrint  ||||||||||||||||||")
        res = self._runner("self._LogGetModul({})".format(coreid),
                           MONITOR_CMD_WAIT_TIME,
                           "Log Moduleget Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogLevelSet(self, coreid, module, level):
        getRes = self.LogGetModul(coreid)
        setRes = self._runner("self._LogLevelSet({}, \"{}\", \"{}\")".format(coreid, module, level),
                           MONITOR_CMD_WAIT_TIME,
                           "Log Moduleset Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(getRes) and self._isSuccess(setRes)

    def LogStartSet(self, coreid, start):
        res = self._runner("self._LogStart({}, \"{}\")".format(coreid, start),
                           MONITOR_CMD_WAIT_TIME,
                           "Log Startset Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogSave2File(self, coreid, file):
        file = file.replace("/", "\\\\")
        logging.info("|||||||||||||||||||||||Begin LogSave2File  ||||||||||||||||||")
        cmd = "self._LogSave({}, \"{}\")".format(coreid, file)
        res = self._runner(cmd,
                           MONITOR_CMD_WAIT_TIME,
                           "los save to file fail",
                           "Monitor is busy all the time,stop write file to mem")
        return self._isSuccess(res)

    def LogExtBoardSet(self, coreid, brdType):
        res = self._runner("self._LogExtBrdSet({}, \"{}\")".format(coreid, brdType),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx brdType Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogExtFilterSet(self, coreid, filter):
        res = self._runner("self._LogExtFilter({}, \"{}\")".format(coreid, filter),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx filter Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogExtConditionSet(self, coreid, condition):
        res = self._runner("self._LogExtConditionSet({}, \"{}\")".format(coreid, condition),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx condition Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogExtLevelSet(self, coreid, module, level):
        res = self._runner("self._LogExtLevelSet({}, \"{}\", \"{}\")".format(coreid, module, level),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx level Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def LogExtStartSet(self, coreid, start):
        res = self._runner("self._LogExtStart({}, \"{}\")".format(coreid, start),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx start Fail",
                           "Monitor is busy all the time,stop read")

    def LogExtSave2File(self, coreid, file):
        file = file.replace("/", "\\\\")
        logging.info("|||||||||||||||||||||||Begin LogSave2File  ||||||||||||||||||")
        cmd = "self._LogExtSave({}, \"{}\")".format(coreid, file)
        res = self._runner(cmd,
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx save to file fail",
                           "Monitor is busy all the time,stop write file to mem")
        return self._isSuccess(res)

    def ChangeMap(self, coreid, map='', out=''):
        item = self.cfg[coreid]
        map = map or item[2]
        out = out or item[3]
        res = self._runner("self._ChangeMap1({}, \"{}\", \"{}\")".format(coreid, map, out),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx level Fail",
                           "Monitor is busy all the time,stop read")
        result = self._isSuccess(res)
        if result:
            self.mapstatus[coreid] = out == 'auto' and 1 or 0# 1自动加载map, 0 手动加载map
        return result

    def ParseLogFile(self, coreid, logfile):
        # map = r'"%s"' % map
        # out = r'"%s"' % out
        # logfile = logfile.replace("/", "\\")
        res = self._runner("self._ParseLog({}, \"{}\")".format(coreid, logfile),
                           MONITOR_CMD_WAIT_TIME,
                           "LogEx level Fail",
                           "Monitor is busy all the time,stop read")
        return self._isSuccess(res)

    def PcAddrParse(self, coreid, addr):
        res = self._runner("self._PcAddrParse({}, \"{}\")".format(coreid, addr),
                           MONITOR_CMD_WAIT_TIME,
                           "PcAddrParse Fail",
                           "Monitor is busy all the time,stop read")
        # return self._isSuccess(res)
        result = MonitorResult(self._endian(coreid), res)
        return result

    def getPmuType(self, coreid):
        core_type = self.coretype_list[coreid]
        if core_type == 'XC12' or core_type == 'XC16':
            return 'Mem'
        else:
            return 'Msg'

    def _PmuLine(self, coreid, config):
        event = ";".join('%s' % id for id in config['event'])
        event = '[%s]' % str(event)
        sname = config['start_file']
        ename = config['end_file']
        sline = str(config['start_line'])
        eline = str(config['end_line'])
        path = config['save_file']
        num = str(config['num'])

        if not self._StatusCheck(coreid, PMU):
            raise Exception("status error test stop")

        pmutype = self.getPmuType(coreid)
        if pmutype == 'Msg':
            cmd = self.gencmd_log("ClientPmuLine", coreid, self.sname(sname), self.sline(sline),
                                  self.ename(ename), self.eline(eline), self.num(num),
                                  self.event(event), self.savename(path), self.pmuType(self.getPmuType(coreid)))
        else:   # Mem
            passback = config['passback']
            filter = config['filter']
            passback_addr1 = str(passback['passback_addr1'])
            passback_type1 = str(passback['passback_type1'])
            passback_addr2 = str(passback['passback_addr2'])
            passback_type2 = str(passback['passback_type2'])
            filter_addr_min1 = str(filter['passback_min1'])
            filter_addr_max1 = str(filter['passback_max1'])
            filter_addr_min2 = str(filter['passback_min2'])
            filter_addr_max2 = str(filter['passback_max2'])
            cmd = self.gencmd_log("ClientPmuLine", coreid, self.sname(sname), self.sline(sline),
                                  self.ename(ename), self.eline(eline), self.num(num), self.event(event),
                                  self.savename(path), self.paddr1(passback_addr1), self.paddr2(passback_addr2),
                                  self.ptype1(passback_type1), self.ptype2(passback_type2),
                                  self.pmin1(filter_addr_min1), self.pmax1(filter_addr_max1),
                                  self.pmin2(filter_addr_min2), self.pmax2(filter_addr_max2),
                                  self.pmuType(self.getPmuType(coreid)))
        res = self.looprun(cmd, timeout=3)
        return res

    def PmuLine(self, coreid, config):
        res = self._PmuLine(coreid, config)
        result = MonitorResult(self._endian(coreid), res)
        cp = re.compile("[\n]((\w+ : \d+[\r\n])+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            arrstr = m.group(0)
            arr = re.compile("(\w+) : (\d+)[\r\n]", re.M | re.S).findall(arrstr)
            value = {}
            for item in arr:
                value[item[0]] = int(item[1])
            result.Value = value
            result.Status = True
        return result

    def _PmuFunNum(self, coreid, config):
        event = ";".join('%s' % id for id in config['event'])
        event = '[%s]' % str(event)
        fun = config['fun_name']
        path = config['save_file']
        num = str(config['num'])

        if not self._StatusCheck(coreid, PMU):
            raise Exception("status error test stop")
        pmutype = self.getPmuType(coreid)
        if pmutype == 'Msg':
            cmd = self.gencmd_log("ClientPmuFunNum", coreid, self.funname(fun), self.num(num), self.event(event),
                                  self.savename(path), self.pmuType(self.getPmuType(coreid)))
        else:
            passback = config['passback']
            filter = config['filter']
            passback_addr1 = str(passback['passback_addr1'])
            passback_type1 = str(passback['passback_type1'])
            passback_addr2 = str(passback['passback_addr2'])
            passback_type2 = str(passback['passback_type2'])
            filter_addr_min1 = str(filter['passback_min1'])
            filter_addr_max1 = str(filter['passback_max1'])
            filter_addr_min2 = str(filter['passback_min2'])
            filter_addr_max2 = str(filter['passback_max2'])
            cmd = self.gencmd_log("ClientPmuFunNum", coreid, self.funname(fun), self.num(num), self.event(event),
                                  self.savename(path), self.paddr1(passback_addr1), self.paddr2(passback_addr2),
                                  self.ptype1(passback_type1), self.ptype2(passback_type2),
                                  self.pmin1(filter_addr_min1), self.pmax1(filter_addr_max1),
                                  self.pmin2(filter_addr_min2), self.pmax2(filter_addr_max2),
                                  self.pmuType(self.getPmuType(coreid)))

        res = self.looprun(cmd, timeout=3)
        return res

    def PmuFunNum(self, coreid, config):
        res = self._PmuFunNum(coreid, config)
        result = MonitorResult(self._endian(coreid), res)
        cp = re.compile("[\n]((\w+ : \d+[\r\n])+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            arrstr = m.group(0)
            arr = re.compile("(\w+) : (\d+)[\r\n]", re.M | re.S).findall(arrstr)
            value = {}
            for item in arr:
                value[item[0]] = int(item[1])
            result.Value = value
            result.Status = True
        return result

    def _PmuFunTime(self, coreid, fun, time):
        if not self._StatusCheck(coreid, PMU):
            raise Exception("status error test stop")
        cmd = self.gencmd_log("ClientPmuFunTime", coreid, self.funname(fun), self.num(time))
        res = self.looprun(cmd, timeout=3)
        return res

    def PmuFunTime(self, coreid, fun, time):
        res = self._PmuFunTime(coreid, fun, time)
        result = MonitorResult(self._endian(coreid), res)
        cp = re.compile("[\n]count : (\d+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Value = int(m.group(1))
            result.Status = True
        return result

    def _PmuQuery(self, coreid):
        # if not self._StatusCheck(coreid, PMU):
        #     raise Exception("status error test stop")
        cmd = self.gencmd_log("ClientPmuQuery", coreid)
        res = self.run(cmd, timeout=3)
        return res

    def _PmuParseValue(self, result, res):
        #  返回为空，此次查询中无任务已完成
        cp = re.compile("[\n]null[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Value = None
            result.Status = False
            return result
        # 保存任务信息
        result.Status = True
        cp = re.compile("[\n]Task: (.*?) end[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Task = m.group(1)
        # 保存异常信息
        cp = re.compile("[\n]Exception: (.*?) end[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Exception = m.group(1)
        # 返回为循环次�?
        cp = re.compile("[\n]count : (\d+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Value = int(m.group(1))
            return result
        # 返回为性能数据
        cp = re.compile("[\n]((\w+ : \d+[\r\n])+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            arrstr = m.group(0)
            arr = re.compile("(\w+) : (\d+)[\r\n]", re.M | re.S).findall(arrstr)
            value = {}
            for item in arr:
                value[item[0]] = int(item[1])
            result.Value = value
        return result

    def PmuQuery(self, coreid):
        res = self._PmuQuery(coreid)
        result = PmuResult(res)

        # 当前核无配置任务
        cp = re.compile("[\n]do not exist[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.NoneTask = True  # 板不存在任务
            result.Status = True
            return
        # 处理任务数据
        result = self._PmuParseValue(result, res)

        # 已完成的任务�?
        cp = re.compile("[\n]Completed Count: (\d+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Completed = int(m.group(1))  # 表示已完成，待取的任务数
        # 未完成的任务�?
        cp = re.compile("[\n]UnCompleted Count: (\d+)[\r\n]+(.*?)~End~", re.M | re.S)
        m = cp.search(res)
        if (m):
            result.Uncompleted = int(m.group(1))  # 表示总的剩余的未完成任务�?
        return result