from enum import Enum


class HttpCodeEnum(Enum):
    success = "0000"
    system_failed = "0001"
    auth_failed = "0002"
    business_failed = "0003"
    validated_failed = "0004"
    server_failed = "0005"


class HttpMessageIdEnum(Enum):
    success = "SUCCESS"
    system_failed = "SystemFailed"
    auth_failed = "AuthFailed"
    business_failed = "BusinessFailed"
    validated_failed = "ValidatedFailed"
    server_failed = "ServerFailed"


CODE_MESSAGE = {
    HttpCodeEnum.success.value: HttpMessageIdEnum.success.value,
    HttpCodeEnum.system_failed.value: HttpMessageIdEnum.system_failed.value,
    HttpCodeEnum.auth_failed.value: HttpMessageIdEnum.auth_failed.value,
    HttpCodeEnum.business_failed.value: HttpMessageIdEnum.business_failed.value,
    HttpCodeEnum.validated_failed.value: HttpMessageIdEnum.validated_failed.value,
    HttpCodeEnum.server_failed.value: HttpMessageIdEnum.server_failed.value
}


class HttpStatusCodeEnum(Enum):
    internal_err = 500