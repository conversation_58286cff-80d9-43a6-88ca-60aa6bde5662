from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from datetime import datetime
from domain.model.dto.logdto import LogJobInfo
from domain.repository.es_jenkins_job import EsJenkinsJob
from infrastructure.utils.json2 import marshal
from service.env_service import VersionEnvService
from service.logging_service import log_execution
from service.service_base import ServiceBase
from infrastructure.logger.logger import logger
from domain.model.dto.jenkins_job_aggregation import (
    JenkinsJobAggregationRequest,
    JenkinsJobAggregationResponse,
    TimeRange,
    EnvGroupStats,
    PrincipalGroupStats,
    TaskIdGroupStats
)
from domain.repository.es_env import EsBizCoreEnv
from typing import Optional, Dict, List
from collections import defaultdict


class JenkinsJobService(ServiceBase):
    """
    Service for handling Jenkins job operations.
    Provides functionality to save Jenkins job information and trigger log analysis.
    """

    def __init__(self, service_msg_dict):
        """
        Initialize the service with Jenkins job information.

        Args:
            service_msg_dict: Either a JenkinsJobInfo object or a dictionary with job information

        Raises:
            TypeError: If service_msg_dict is neither a dict nor a JenkinsJobInfo object
        """
        # Set common service attributes
        self.set_service_attributes(self, dict(service_msg_dict))

        if isinstance(service_msg_dict, JenkinsJobInfo):
            self.jenkins_job = service_msg_dict
        elif isinstance(service_msg_dict, dict):
            self.jenkins_job = JenkinsJobInfo(**service_msg_dict)
        else:
            error_msg = f"Expected dict or JenkinsJobInfo, got {type(service_msg_dict)}"
            logger.error(error_msg)
            raise TypeError(error_msg)

        # Override task_id and env_id with values from jenkins_job
        self.task_id = self.jenkins_job.task_id
        self.env_id = self.jenkins_job.env_id

    def save_jenkins_job(self):
        """
        Save the Jenkins job information to Elasticsearch.

        Returns:
            dict: Result of the Elasticsearch index operation
        """
        logger.info(f"Saving Jenkins job: {self.jenkins_job.jenkins_job_name} for env: {self.jenkins_job.env_id}")
        return EsJenkinsJob().index(id=datetime.now().isoformat(), body=eval(marshal(self.jenkins_job)))

    @log_execution(operation="run")
    async def run(self):
        """
        Main execution method that saves the job and triggers log analysis if needed.

        Returns:
            dict: Result of the save operation
        """
        # Save the Jenkins job
        result = self.save_jenkins_job()
        logger.info(f"Jenkins job saved with result: {result}")

        # Save version environment information
        VersionEnvService().save_version_env_info(self.jenkins_job)
        logger.info(f"Version environment information saved for job: {self.jenkins_job.jenkins_job_name}")

        # If test result is True, we're done
        if bool(eval(self.jenkins_job.version_test_result)):
            logger.info(f"Test result is True, no further action needed for job: {self.jenkins_job.jenkins_job_name}")
            return result

        # If test result is False, queue for log analysis
        logger.info(f"Test result is False, queueing for log analysis: {self.jenkins_job.jenkins_job_name}")
        msg = LogJobInfo(
            log_name=self.jenkins_job.jenkins_log_name,
            job_name=self.jenkins_job.jenkins_job_name,
            build_number=self.jenkins_job.jenkins_build_number,
            env_id=self.jenkins_job.env_id,
            task_id=self.jenkins_job.task_id,
            service_type="log_analysis"
        )

        from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
        await CiAgentsQueue().push_info(msg)
        logger.info(f"Message queued for log analysis: {self.jenkins_job.jenkins_job_name}")

        return result

    @staticmethod
    def aggregate_jenkins_jobs(
        start_date: str,
        end_date: str,
        env_id: Optional[str] = None,
        group_by_env: bool = False
    ) -> JenkinsJobAggregationResponse:
        """
        根据时间范围聚合Jenkins任务信息，支持manual_processing_hours求和

        Args:
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            env_id: 环境ID过滤(可选)
            group_by_env: 是否按环境分组

        Returns:
            JenkinsJobAggregationResponse: 聚合结果
        """
        try:
            logger.info(f"开始聚合Jenkins任务: {start_date} 到 {end_date}, env_id: {env_id}, group_by_env: {group_by_env}")

            # 调用repository层的聚合方法
            es_jenkins_job = EsJenkinsJob()
            aggregation_result = es_jenkins_job.aggregate_by_time_range(
                start_date=start_date,
                end_date=end_date,
                env_id=env_id,
                group_by_env=group_by_env
            )

            # 构建响应对象
            time_range = TimeRange(start_date=start_date, end_date=end_date)

            env_groups = None
            if group_by_env and aggregation_result.get('env_groups'):
                env_groups = [
                    EnvGroupStats(**env_data)
                    for env_data in aggregation_result['env_groups']
                ]

            response = JenkinsJobAggregationResponse(
                total_jobs=aggregation_result.get('total_jobs', 0),
                total_manual_hours=aggregation_result.get('total_manual_hours', 0.0),
                unique_task_count=aggregation_result.get('unique_task_count', 0),
                time_range=time_range,
                env_groups=env_groups
            )

            logger.info(f"Jenkins任务聚合完成: 总任务数={response.total_jobs}, 总人工时间={response.total_manual_hours}小时")
            return response

        except Exception as e:
            logger.error(f"Jenkins任务聚合失败: {str(e)}")
            raise e

    @staticmethod
    def get_principal_by_env_ids(env_ids: List[str]) -> Dict[str, str]:
        """
        根据环境ID列表获取对应的负责人信息

        Args:
            env_ids: 环境ID列表

        Returns:
            Dict[str, str]: 环境ID到负责人的映射
        """
        try:
            es_biz_core = EsBizCoreEnv()
            env_principal_map = {}

            for env_id in env_ids:
                try:
                    _, data_list = es_biz_core.query_by_filter_without_sort({"env_id": env_id})
                    if data_list and len(data_list) > 0:
                        principal = data_list[0].get('principal', '')
                        env_principal_map[env_id] = principal
                    else:
                        env_principal_map[env_id] = ''
                except Exception as e:
                    logger.warning(f"获取环境 {env_id} 的负责人信息失败: {str(e)}")
                    env_principal_map[env_id] = ''

            return env_principal_map

        except Exception as e:
            logger.error(f"批量获取负责人信息失败: {str(e)}")
            return {env_id: '' for env_id in env_ids}

    @staticmethod
    def aggregate_jenkins_jobs_advanced(
        start_date: str,
        end_date: str,
        env_id: Optional[str] = None,
        principal: Optional[str] = None,
        task_id: Optional[str] = None,
        group_by_env: bool = False,
        group_by_principal: bool = False,
        group_by_task_id: bool = False
    ) -> JenkinsJobAggregationResponse:
        """
        高级Jenkins任务聚合，支持按负责人和任务ID聚合

        Args:
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            env_id: 环境ID过滤(可选)
            principal: 负责人过滤(可选)
            task_id: 任务ID过滤(可选)
            group_by_env: 是否按环境分组
            group_by_principal: 是否按负责人分组
            group_by_task_id: 是否按任务ID分组

        Returns:
            JenkinsJobAggregationResponse: 聚合结果
        """
        try:
            logger.info(f"开始高级Jenkins任务聚合: {start_date} 到 {end_date}")

            # 如果按负责人过滤，需要先获取对应的环境ID列表
            filtered_env_ids = None
            if principal:
                es_biz_core = EsBizCoreEnv()
                _, env_data_list = es_biz_core.query_by_filter_without_sort({"principal": principal})
                filtered_env_ids = [env_data.get('env_id') for env_data in env_data_list if env_data.get('env_id')]

                if not filtered_env_ids:
                    # 如果没有找到对应负责人的环境，返回空结果
                    time_range = TimeRange(start_date=start_date, end_date=end_date)
                    return JenkinsJobAggregationResponse(
                        total_jobs=0,
                        total_manual_hours=0.0,
                        unique_task_count=0,
                        time_range=time_range
                    )

            # 调用repository层的高级聚合方法
            es_jenkins_job = EsJenkinsJob()

            # 如果有负责人过滤但没有指定env_id，使用过滤出的环境ID列表
            if principal and not env_id and filtered_env_ids:
                # 对每个环境分别查询然后合并结果
                all_results = []
                for filtered_env_id in filtered_env_ids:
                    result = es_jenkins_job.aggregate_by_time_range_advanced(
                        start_date=start_date,
                        end_date=end_date,
                        env_id=filtered_env_id,
                        task_id=task_id,
                        group_by_env=group_by_env,
                        group_by_task_id=group_by_task_id
                    )
                    all_results.append(result)

                # 合并结果
                aggregation_result = JenkinsJobService._merge_aggregation_results(all_results, start_date, end_date)
            else:
                aggregation_result = es_jenkins_job.aggregate_by_time_range_advanced(
                    start_date=start_date,
                    end_date=end_date,
                    env_id=env_id,
                    task_id=task_id,
                    group_by_env=group_by_env,
                    group_by_task_id=group_by_task_id
                )

            # 构建响应对象
            time_range = TimeRange(start_date=start_date, end_date=end_date)

            # 处理环境分组
            env_groups = None
            if group_by_env and aggregation_result.get('env_groups'):
                env_groups = [
                    EnvGroupStats(**env_data)
                    for env_data in aggregation_result['env_groups']
                ]

            # 处理任务ID分组
            task_id_groups = None
            if group_by_task_id and aggregation_result.get('task_id_groups'):
                # 获取所有相关环境的负责人信息
                env_ids = [task_data.get('env_id') for task_data in aggregation_result['task_id_groups'] if task_data.get('env_id')]
                env_principal_map = JenkinsJobService.get_principal_by_env_ids(env_ids)

                task_id_groups = []
                for task_data in aggregation_result['task_id_groups']:
                    task_env_id = task_data.get('env_id', '')
                    task_principal = env_principal_map.get(task_env_id, '')

                    task_id_groups.append(TaskIdGroupStats(
                        task_id=task_data['task_id'],
                        total_jobs=task_data['total_jobs'],
                        total_manual_hours=task_data['total_manual_hours'],
                        env_id=task_env_id,
                        principal=task_principal
                    ))

            # 处理负责人分组
            principal_groups = None
            if group_by_principal:
                principal_groups = JenkinsJobService._generate_principal_groups(
                    aggregation_result, env_groups or []
                )

            response = JenkinsJobAggregationResponse(
                total_jobs=aggregation_result.get('total_jobs', 0),
                total_manual_hours=aggregation_result.get('total_manual_hours', 0.0),
                unique_task_count=aggregation_result.get('unique_task_count', 0),
                time_range=time_range,
                env_groups=env_groups,
                principal_groups=principal_groups,
                task_id_groups=task_id_groups
            )

            logger.info(f"高级Jenkins任务聚合完成: 总任务数={response.total_jobs}, 总人工时间={response.total_manual_hours}小时")
            return response

        except Exception as e:
            logger.error(f"高级Jenkins任务聚合失败: {str(e)}")
            raise e

    @staticmethod
    def _merge_aggregation_results(results: List[Dict], start_date: str, end_date: str) -> Dict:
        """
        合并多个聚合结果

        Args:
            results: 聚合结果列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict: 合并后的聚合结果
        """
        merged_result = {
            "total_jobs": 0,
            "total_manual_hours": 0.0,
            "unique_task_count": 0,
            "time_range": {
                "start_date": start_date,
                "end_date": end_date
            },
            "env_groups": [],
            "task_id_groups": []
        }

        unique_task_ids = set()

        for result in results:
            merged_result["total_jobs"] += result.get("total_jobs", 0)
            merged_result["total_manual_hours"] += result.get("total_manual_hours", 0.0)

            # 收集唯一任务ID（这里简化处理，实际应该从原始数据获取）
            if result.get("env_groups"):
                merged_result["env_groups"].extend(result["env_groups"])

            if result.get("task_id_groups"):
                merged_result["task_id_groups"].extend(result["task_id_groups"])
                for task_group in result["task_id_groups"]:
                    unique_task_ids.add(task_group["task_id"])

        merged_result["unique_task_count"] = len(unique_task_ids)
        return merged_result

    @staticmethod
    def _generate_principal_groups(aggregation_result: Dict, env_groups: List[EnvGroupStats]) -> List[PrincipalGroupStats]:
        """
        根据环境分组生成负责人分组统计

        Args:
            aggregation_result: 聚合结果
            env_groups: 环境分组列表

        Returns:
            List[PrincipalGroupStats]: 负责人分组统计列表
        """
        if not env_groups:
            return []

        try:
            # 获取所有环境的负责人信息
            env_ids = [env_group.env_id for env_group in env_groups]
            env_principal_map = JenkinsJobService.get_principal_by_env_ids(env_ids)

            # 按负责人分组
            principal_stats = defaultdict(lambda: {
                "total_jobs": 0,
                "total_manual_hours": 0.0,
                "unique_task_count": 0,
                "env_ids": []
            })

            for env_group in env_groups:
                principal = env_principal_map.get(env_group.env_id, '未知')
                principal_stats[principal]["total_jobs"] += env_group.total_jobs
                principal_stats[principal]["total_manual_hours"] += env_group.total_manual_hours
                principal_stats[principal]["unique_task_count"] += env_group.unique_task_count
                principal_stats[principal]["env_ids"].append(env_group.env_id)

            # 转换为PrincipalGroupStats对象
            result = []
            for principal, stats in principal_stats.items():
                result.append(PrincipalGroupStats(
                    principal=principal,
                    total_jobs=stats["total_jobs"],
                    total_manual_hours=stats["total_manual_hours"],
                    unique_task_count=stats["unique_task_count"],
                    env_ids=stats["env_ids"]
                ))

            return result

        except Exception as e:
            logger.error(f"生成负责人分组统计失败: {str(e)}")
            return []
