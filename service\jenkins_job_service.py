from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from datetime import datetime
from domain.model.dto.logdto import LogJobInfo
from domain.repository.es_jenkins_job import EsJenkinsJob
from infrastructure.utils.json2 import marshal
from service.env_service import VersionEnvService
from service.logging_service import log_execution
from service.service_base import ServiceBase
from infrastructure.logger.logger import logger


class JenkinsJobService(ServiceBase):
    """
    Service for handling Jenkins job operations.
    Provides functionality to save Jenkins job information and trigger log analysis.
    """

    def __init__(self, service_msg_dict):
        """
        Initialize the service with Jenkins job information.

        Args:
            service_msg_dict: Either a JenkinsJobInfo object or a dictionary with job information

        Raises:
            TypeError: If service_msg_dict is neither a dict nor a JenkinsJobInfo object
        """
        # Set common service attributes
        self.set_service_attributes(self, dict(service_msg_dict))

        if isinstance(service_msg_dict, JenkinsJobInfo):
            self.jenkins_job = service_msg_dict
        elif isinstance(service_msg_dict, dict):
            self.jenkins_job = JenkinsJobInfo(**service_msg_dict)
        else:
            error_msg = f"Expected dict or JenkinsJobInfo, got {type(service_msg_dict)}"
            logger.error(error_msg)
            raise TypeError(error_msg)

        # Override task_id and env_id with values from jenkins_job
        self.task_id = self.jenkins_job.task_id
        self.env_id = self.jenkins_job.env_id

    def save_jenkins_job(self):
        """
        Save the Jenkins job information to Elasticsearch.

        Returns:
            dict: Result of the Elasticsearch index operation
        """
        logger.info(f"Saving Jenkins job: {self.jenkins_job.jenkins_job_name} for env: {self.jenkins_job.env_id}")
        return EsJenkinsJob().index(id=datetime.now().isoformat(), body=eval(marshal(self.jenkins_job)))

    @log_execution(operation="run")
    async def run(self):
        """
        Main execution method that saves the job and triggers log analysis if needed.

        Returns:
            dict: Result of the save operation
        """
        # Save the Jenkins job
        result = self.save_jenkins_job()
        logger.info(f"Jenkins job saved with result: {result}")

        # Save version environment information
        VersionEnvService().save_version_env_info(self.jenkins_job)
        logger.info(f"Version environment information saved for job: {self.jenkins_job.jenkins_job_name}")

        # If test result is True, we're done
        if bool(eval(self.jenkins_job.version_test_result)):
            logger.info(f"Test result is True, no further action needed for job: {self.jenkins_job.jenkins_job_name}")
            return result

        # If test result is False, queue for log analysis
        logger.info(f"Test result is False, queueing for log analysis: {self.jenkins_job.jenkins_job_name}")
        msg = LogJobInfo(
            log_name=self.jenkins_job.jenkins_log_name,
            job_name=self.jenkins_job.jenkins_job_name,
            build_number=self.jenkins_job.jenkins_build_number,
            env_id=self.jenkins_job.env_id,
            task_id=self.jenkins_job.task_id,
            service_type="log_analysis"
        )

        from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
        await CiAgentsQueue().push_info(msg)
        logger.info(f"Message queued for log analysis: {self.jenkins_job.jenkins_job_name}")

        return result
