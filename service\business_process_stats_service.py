from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict, Counter
import logging

from domain.model.dto.business_process_stats import (
    BusinessProcessStatsRequest, 
    BusinessProcessStatsResponse,
    BusinessProcessStats,
    EnvBusinessProcessStats,
    TaskProcessInfo
)
from domain.repository.es_jenkins_job import EsJenkinsJob
from domain.repository.agents_log import EsAgentsLog
from infrastructure.logger.logger import logger


class BusinessProcessStatsService:
    """业务流程统计服务"""
    
    def __init__(self):
        self.jenkins_job_repo = EsJenkinsJob()
        self.agents_log_repo = EsAgentsLog()
        
        # 业务流程类型定义
        self.PROCESS_TYPES = {
            "日志保存": "test_passed",
            "智能分析": "intelligent_analysis", 
            "环境检测": "env_check",
            "复测": "retest",
            "回溯": "rollback"
        }
    
    def get_business_process_stats(self, request: BusinessProcessStatsRequest) -> BusinessProcessStatsResponse:
        """
        获取业务流程统计数据
        
        Args:
            request: 统计请求参数
            
        Returns:
            BusinessProcessStatsResponse: 统计结果
        """
        try:
            # 设置默认时间范围（当天）
            if not request.start_date:
                request.start_date = datetime.now().strftime('%Y-%m-%d')
            if not request.end_date:
                request.end_date = request.start_date
                
            logger.info(f"开始统计业务流程数据，时间范围: {request.start_date} 到 {request.end_date}")
            
            # 1. 获取时间范围内的jenkins任务数据
            total, jenkins_jobs = self.jenkins_job_repo.query_by_time_range(
                request.start_date, 
                request.end_date, 
                request.env_id
            )
            
            if total == 0:
                logger.info("未找到符合条件的jenkins任务数据")
                return BusinessProcessStatsResponse()
            
            # 2. 按task_id聚合，取最早的记录
            earliest_jobs = self._get_earliest_jobs_by_task_id(jenkins_jobs)
            
            # 3. 获取对应的agents_log数据
            task_ids = list(earliest_jobs.keys())
            _, agents_logs = self.agents_log_repo.get_logs_by_task_ids(task_ids)
            
            # 4. 分析业务流程类型
            task_process_infos = self._analyze_business_processes(earliest_jobs, agents_logs)
            
            # 5. 生成统计数据
            response = self._generate_stats_response(task_process_infos, request.start_date, request.end_date)
            
            logger.info(f"业务流程统计完成，共处理 {len(task_process_infos)} 个任务")
            return response
            
        except Exception as e:
            logger.error(f"业务流程统计失败: {str(e)}")
            raise e
    
    def _get_earliest_jobs_by_task_id(self, jenkins_jobs: List[Dict]) -> Dict[str, Dict]:
        """
        按task_id聚合，获取每个task_id最早的记录
        
        Args:
            jenkins_jobs: jenkins任务列表
            
        Returns:
            Dict[str, Dict]: task_id -> 最早的jenkins任务记录
        """
        earliest_jobs = {}
        
        for job in jenkins_jobs:
            task_id = job.get('task_id')
            if not task_id:
                continue
                
            timestamp = job.get('timestamp')
            if not timestamp:
                continue
                
            # 如果是第一次遇到这个task_id，或者当前记录更早
            if (task_id not in earliest_jobs or 
                timestamp < earliest_jobs[task_id].get('timestamp')):
                earliest_jobs[task_id] = job
                
        return earliest_jobs
    
    def _analyze_business_processes(self, earliest_jobs: Dict[str, Dict], 
                                  agents_logs: List[Dict]) -> List[TaskProcessInfo]:
        """
        分析业务流程类型
        
        Args:
            earliest_jobs: 按task_id聚合的最早jenkins任务
            agents_logs: agents日志数据
            
        Returns:
            List[TaskProcessInfo]: 任务流程信息列表
        """
        # 按task_id分组agents_logs
        logs_by_task = defaultdict(list)
        for log in agents_logs:
            task_id = log.get('task_id')
            if task_id:
                logs_by_task[task_id].append(log)
        
        task_process_infos = []
        
        for task_id, job in earliest_jobs.items():
            # 获取该任务的所有日志，按时间排序
            task_logs = sorted(logs_by_task.get(task_id, []), 
                             key=lambda x: x.get('timestamp', ''))
            
            # 分析业务流程类型
            process_type = self._determine_process_type(job, task_logs)
            
            # 提取service_keys和details_subtypes
            service_keys = [log.get('service_key', '') for log in task_logs if log.get('service_key')]
            details_subtypes = []
            for log in task_logs:
                details = log.get('details', {})
                if isinstance(details, dict):
                    message = details.get('message', {})
                    if isinstance(message, dict):
                        subtype = message.get('subtype', '')
                        if subtype and subtype != 'normal':
                            details_subtypes.append(subtype)
            
            # 处理manual_processing_hours的类型转换
            manual_hours = job.get('manual_processing_hours', 0)
            if isinstance(manual_hours, (int, float)):
                manual_hours_str = str(manual_hours)
            else:
                manual_hours_str = str(manual_hours) if manual_hours is not None else '0'

            task_info = TaskProcessInfo(
                task_id=task_id,
                env_id=job.get('env_id', ''),
                timestamp=job.get('timestamp', ''),
                version_test_result=job.get('version_test_result', ''),
                status=job.get('status', ''),
                current_state=job.get('current_state'),
                fail_reason=job.get('fail_reason', ''),
                manual_processing_hours=manual_hours_str,
                service_keys=service_keys,
                process_type=process_type,
                details_subtypes=details_subtypes
            )
            
            task_process_infos.append(task_info)
        
        return task_process_infos

    def _determine_process_type(self, job: Dict, task_logs: List[Dict]) -> str:
        """
        根据jenkins任务和agents日志确定业务流程类型

        Args:
            job: jenkins任务记录
            task_logs: 该任务的agents日志列表（按时间排序）

        Returns:
            str: 业务流程类型
        """
        version_test_result = job.get('version_test_result', '')
        service_keys = [log.get('service_key', '') for log in task_logs if log.get('service_key')]

        # 检查是否有非normal的subtype
        has_non_normal_subtype = False
        for log in task_logs:
            details = log.get('details', {})
            if isinstance(details, dict):
                message = details.get('message', {})
                if isinstance(message, dict):
                    subtype = message.get('subtype', 'normal')
                    if subtype != 'normal':
                        has_non_normal_subtype = True
                        break

        # 检查是否包含version_upgrade或version_rollback
        has_version_operations = any(key in ['version_upgrade', 'version_rollback'] for key in service_keys)

        # 业务流程判断逻辑
        if has_version_operations or has_non_normal_subtype:
            return "回溯"

        if version_test_result == "True":
            # 成功的情况下，如果有jenkins_job_save且状态为success
            for log in task_logs:
                if (log.get('service_key') == 'jenkins_job_save' and
                    log.get('status') == 'success'):
                    return "日志保存"
            return "日志保存"  # 默认为用例通过

        elif version_test_result == "False":
            # 失败的情况下，根据service_key序列判断
            unique_service_keys = list(dict.fromkeys(service_keys))  # 保持顺序去重

            if set(unique_service_keys) == {'jenkins_job_save', 'log_analysis'}:
                return "智能分析"
            elif set(unique_service_keys) == {'jenkins_job_save', 'log_analysis', 'env_check'}:
                return "环境检测"
            elif set(unique_service_keys) == {'jenkins_job_save', 'log_analysis', 'env_check', 'online_test'}:
                return "复测"
            elif 'jenkins_job_save' in unique_service_keys and 'log_analysis' in unique_service_keys:
                # 如果包含基础的jenkins_job_save和log_analysis，但还有其他步骤
                return "智能分析"

        # 默认情况
        return "智能分析"

    def _generate_stats_response(self, task_process_infos: List[TaskProcessInfo],
                               start_date: str, end_date: str) -> BusinessProcessStatsResponse:
        """
        生成统计响应数据

        Args:
            task_process_infos: 任务流程信息列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            BusinessProcessStatsResponse: 统计响应
        """
        # 按日期统计
        by_date_stats = self._generate_date_stats(task_process_infos, start_date, end_date)

        # 按环境统计
        by_env_stats = self._generate_env_stats(task_process_infos)

        # 总体统计
        total_stats = Counter(info.process_type for info in task_process_infos)

        return BusinessProcessStatsResponse(
            by_date=by_date_stats,
            by_env=by_env_stats,
            total_stats=dict(total_stats),
            date_range={
                "start_date": start_date,
                "end_date": end_date
            }
        )

    def _generate_date_stats(self, task_process_infos: List[TaskProcessInfo],
                           start_date: str, end_date: str) -> List[BusinessProcessStats]:
        """
        生成按日期的统计数据

        Args:
            task_process_infos: 任务流程信息列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[BusinessProcessStats]: 按日期统计的结果
        """
        # 按日期分组
        date_groups = defaultdict(list)
        for info in task_process_infos:
            # 从timestamp中提取日期
            timestamp = info.timestamp
            if timestamp:
                try:
                    date = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                    date_groups[date].append(info)
                except:
                    # 如果时间格式解析失败，使用开始日期
                    date_groups[start_date].append(info)

        # 生成每日统计
        date_stats = []
        current_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            day_infos = date_groups.get(date_str, [])

            process_counts = Counter(info.process_type for info in day_infos)

            date_stat = BusinessProcessStats(
                date=date_str,
                process_counts=dict(process_counts),
                total_count=len(day_infos)
            )
            date_stats.append(date_stat)

            current_date += timedelta(days=1)

        return date_stats

    def _generate_env_stats(self, task_process_infos: List[TaskProcessInfo]) -> List[EnvBusinessProcessStats]:
        """
        生成按环境的统计数据

        Args:
            task_process_infos: 任务流程信息列表

        Returns:
            List[EnvBusinessProcessStats]: 按环境统计的结果
        """
        # 按环境分组
        env_groups = defaultdict(list)
        for info in task_process_infos:
            env_groups[info.env_id].append(info)

        # 生成环境统计
        env_stats = []
        for env_id, env_infos in env_groups.items():
            process_counts = Counter(info.process_type for info in env_infos)

            env_stat = EnvBusinessProcessStats(
                env_id=env_id,
                process_counts=dict(process_counts),
                total_count=len(env_infos)
            )
            env_stats.append(env_stat)

        # 按环境ID排序
        env_stats.sort(key=lambda x: x.env_id)

        return env_stats

    def get_business_process_detail(self, request: BusinessProcessStatsRequest,
                                   target_date: str, target_process_type: str = None) -> List[Dict]:
        """
        获取指定日期的业务流程详细信息

        Args:
            request: 统计请求参数
            target_date: 目标日期
            target_process_type: 目标业务流程类型（可选，如果不指定则返回所有流程类型）

        Returns:
            List[Dict]: 详细信息列表，按业务流程类型聚合
        """
        try:
            logger.info(f"开始获取详细信息，日期: {target_date}, 流程类型: {target_process_type}")

            # 1. 获取时间范围内的jenkins任务数据
            total, jenkins_jobs = self.jenkins_job_repo.query_by_time_range(
                request.start_date,
                request.end_date,
                request.env_id
            )

            if total == 0:
                logger.info("未找到符合条件的jenkins任务数据")
                return []

            # 2. 按task_id聚合，取最早的记录
            earliest_jobs = self._get_earliest_jobs_by_task_id(jenkins_jobs)

            # 3. 获取对应的agents_log数据
            task_ids = list(earliest_jobs.keys())
            _, agents_logs = self.agents_log_repo.get_logs_by_task_ids(task_ids)

            # 4. 分析业务流程类型
            task_process_infos = self._analyze_business_processes(earliest_jobs, agents_logs)

            # 5. 筛选出指定日期和流程类型的任务
            detail_list = []
            for task_info in task_process_infos:
                # 检查日期匹配（如果指定了日期）
                task_date = self._extract_date_from_timestamp(task_info.timestamp)
                date_matches = target_date is None or task_date == target_date

                # 检查流程类型匹配（如果指定了流程类型）
                process_type_matches = target_process_type is None or task_info.process_type == target_process_type

                if date_matches and process_type_matches:
                    detail_list.append({
                        "task_id": task_info.task_id,
                        "env_id": task_info.env_id,
                        "date": task_date,
                        "process_type": task_info.process_type,
                        "current_state": task_info.current_state or "UNKNOWN",
                        "status": task_info.status or "UNKNOWN",
                        "fail_reason": task_info.fail_reason,
                        "manual_processing_hours": task_info.manual_processing_hours,
                        "version_test_result": task_info.version_test_result,
                        "details_subtypes": task_info.details_subtypes,
                        "timestamp": task_info.timestamp
                    })

            # 6. 不做聚合，直接返回单条任务列表
            result = []
            for detail in detail_list:
                result.append({
                    "task_id": detail["task_id"],
                    "env_id": detail["env_id"],
                    "date": detail["date"],
                    "process_type": detail["process_type"],
                    "current_state": detail["current_state"],
                    "execution_status": self._get_execution_status([detail]),
                    "manual_processing_hours": detail["manual_processing_hours"],
                    "fail_reason": detail["fail_reason"],
                    "version_test_result": detail["version_test_result"],
                    "details_subtypes": detail["details_subtypes"],
                    "timestamp": detail["timestamp"],
                    "status": detail["status"]
                })

            logger.info(f"获取详细信息完成，共找到 {len(result)} 种业务流程类型的数据")
            return result

        except Exception as e:
            logger.error(f"获取业务流程详细信息失败: {str(e)}")
            raise e

    def _extract_date_from_timestamp(self, timestamp: str) -> str:
        """从时间戳中提取日期"""
        try:
            if timestamp:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d')
        except:
            pass
        return datetime.now().strftime('%Y-%m-%d')

    def _get_dominant_state(self, flag:str, tasks: List[Dict]) -> str:
        """获取主要状态（优先显示失败状态）"""
        states = [task.get(flag, "UNKNOWN") for task in tasks]
        if "FAIL" in states or "failure" in states:
            return "FAIL"
        elif "PASS" in states or "success" in states:
            return "PASS"
        elif "in_progress" in states:
            return "IN PROGRESS"
        else:
            return "UNKNOWN"

    def _sum_manual_hours(self, tasks: List[Dict]) -> str:
        """计算总的人工处理时间"""
        total_hours = 0.0
        for task in tasks:
            try:
                hours = float(task.get("manual_processing_hours", "0"))
                total_hours += hours
            except:
                pass
        return str(total_hours)

    def _get_execution_status(self, tasks: List[Dict]) -> str:
        """
        获取执行结果状态，基于agents_log中步骤内的最新记录

        Args:
            tasks: 任务列表

        Returns:
            str: 执行状态 success/in_progress/failure
        """
        try:
            # 收集所有任务的task_id
            task_ids = [task["task_id"] for task in tasks]

            if not task_ids:
                return "unknown"

            # 获取这些任务的agents_log记录
            _, agents_logs = self.agents_log_repo.get_logs_by_task_ids(task_ids)

            if not agents_logs:
                return "unknown"

            # 按task_id分组，获取每个任务的最新状态
            task_status_map = defaultdict(list)
            for log in agents_logs:
                task_id = log.get("task_id")
                if task_id in task_ids:
                    task_status_map[task_id].append(log)

            # 统计各种状态的数量
            status_counts = defaultdict(int)

            for task_id, logs in task_status_map.items():
                if not logs:
                    status_counts["unknown"] += 1
                    continue

                # 按时间戳排序，获取最新的记录
                sorted_logs = sorted(logs, key=lambda x: x.get("timestamp", ""), reverse=True)
                latest_log = sorted_logs[0]

                # 获取状态并标准化
                status = latest_log.get("status", "unknown").lower()
                status_counts[status] += 1

            # 根据优先级确定整体状态
            # 优先级：failure > in_progress > success > unknown
            if status_counts.get("failure", 0) > 0:
                return "failure"
            elif status_counts.get("in_progress", 0) > 0:
                return "in_progress"
            elif status_counts.get("success", 0) > 0:
                return "success"
            else:
                return "unknown"

        except Exception as e:
            logger.error(f"获取执行状态失败: {str(e)}")
            return "unknown"


