from infrastructure.utils.jenkinsCLient import JenkinsClient
from typing import Optional


def online_retest(jenkins_client: JenkinsClient, job_name: str, parameters: Optional[dict] = None):
    """
    在线重测函数，触发 Jenkins Job 进行重测。

    :param jenkins_client: JenkinsClient 实例
    :param job_name: <PERSON> Job 名称
    :param parameters: 可选，Job 参数字典
    """
    # 调用 trigger_job 方法触发 Jenkins Job
    try:
        build_number = jenkins_client.trigger_job(job_name, parameters)
        print(f"Job '{job_name}' 已成功触发，构建编号: {build_number}")
    except Exception as e:
        print(f"触发 Job '{job_name}' 时发生错误: {e}")
