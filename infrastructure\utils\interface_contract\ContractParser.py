# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import copy
import os
import re

from infrastructure.utils.Singleton import Singleton
from infrastructure.logger.logger import logger


@Singleton
class ContractParser(object):
    '''
    classdocs
    '''

    def __init__(self):
        '''
        Constructor
        '''
        self._currentDir = os.path.dirname(os.path.realpath(__file__))
        self._contractFileDir = os.path.join((self._currentDir).split('interface_contract')[0], "contract_file")
        self._contractDict = {}
        self._load_all_contract_file()

    def contract(self):
        return self._contractDict

    def sub_contract_para(self, contractPath, paraDict):
        if contractPath not in self._contractDict:
            raise Exception('Not such contract: ' + contractPath)
        oneContract = copy.deepcopy(self._contractDict[contractPath])
        self._sub_contract_items(oneContract, paraDict)
        self._sub_contract_excute_unit(oneContract, paraDict)
        return oneContract

    def get_default_config(self, contractPath):
        contractFileName = contractPath.split('.')[0]
        contractDefaultConfig = contractFileName + '.DEFAULT_CONFIG'
        return self._contractDict[contractDefaultConfig]

    def _sub_contract_excute_unit(self, oneContract, paraDict):
        if 'contract' in oneContract:
            new_excuteUnits = []
            for excuteUnit in oneContract['contract']:
                new_excuteUnit = {}
                for key, value in excuteUnit.items():
                    if isinstance(value, str):
                        new_excuteUnit[key] = f"{value}" % paraDict
                    elif key == 'contractParas':
                        new_contractParas = {}
                        for k, v in excuteUnit[key].items():
                            new_contractParas[k] = f"{v}" % paraDict
                        new_excuteUnit[key] = new_contractParas
                    elif key == 'funcParas':
                        new_funcParas = []
                        for i in range(len(excuteUnit[key])):
                            new_funcParas.append(f"{excuteUnit[key][i]}" % paraDict)
                        new_excuteUnit[key] = new_funcParas
                new_excuteUnits.append(new_excuteUnit)
            oneContract['contract'] = new_excuteUnits

    def _sub_contract_items(self, oneContract, paraDict):
        for key, value in oneContract.items():
            if isinstance(value, str):
                oneContract[key] = value % paraDict

    def _load_all_contract_file(self):
        for contractFileTupple in self._get_all_contract_files():
            contractFileName = contractFileTupple[0]
            contractFilePath = contractFileTupple[1]
            try:
                module = __import__(contractFilePath + '.' + contractFileName, globals(), locals(), ['*'])
                self._load_module_and_save(module, contractFileName)
            except Exception:
                logger.error(u'导入契约文件：{0}失败，请检查该文件格式是否合法'.format(contractFileName))
        return self._contractDict

    def _load_module_and_save(self, module, contractFileName):
        for var in dir(module):
            if var not in ['__builtins__', '__doc__', '__file__', '__name__', '__package__']:
                key = contractFileName + '.' + var
                if key in self._contractDict:
                    raise Exception('Contract File has the same record')
                self._contractDict[key] = eval('module.' + var)

    def _get_all_contract_files(self):
        contractFiles = set()
        importPath = 'infrastructure/utils/contract_file'
        for path, _, filelist in os.walk(self._contractFileDir):
            for f in filelist:
                shotname, extension = os.path.splitext(f)
                if shotname != '__init__' and extension != 'pyc':
                    contractFiles.add((shotname, re.sub('\\\\|/', '.', importPath)))
        return contractFiles

    def sub_contract(self, oneContract, paraDict):
        for key, value in oneContract.items():
            if key == 'contract':
                if isinstance(value, str):
                    oneContract[key] = value

if __name__ == '__main__':
    print(os.path.dirname(os.path.realpath(__file__)))
    print(os.path.join(os.path.dirname(os.path.realpath(__file__)).split('interface_contract')[0], "contract_file"))
    print(ContractParser().contract())
