import time, re, os
from domain.model.testcaseloganalyzer.log_analysis import process_error_format_html, \
    get_html_raw_info, filter_relevant_info, get_html_testcase
from domain.repository.es_jenkins_job import <PERSON>s<PERSON><PERSON><PERSON><PERSON>ob
from domain.repository.testcaselogrepository.LogAnalysisRepository import Test<PERSON>aseLogRepository
from domain.model.testcaseloganalyzer.testcaselog import Test<PERSON><PERSON><PERSON>og
from infrastructure.logger.logger import logger
from infrastructure.utils.sftp.SftpService import SftpService
from infrastructure.utils.FileHandler import mk_dir
from domain.model.dto.env_check import EnvCheck
from domain.model.testcaseloganalyzer.constants import fail_class, error_type_substrings, ue_fail_rules
from service.logging_service import LoggingService
from infrastructure.utils.json2 import marshal


# from infrastructure.AiStudioApi import AiStudioApi


class LogAnalysisService:
    def __init__(self, log_info: dict):
        self.log_info = log_info
        self.file_path = log_info.get("log_name")
        self._es_jenkins_job = EsJenkinsJob()

    def query_task_is_exit(self,env_id,task_id):
        result = self._es_jenkins_job.query_exit_task(env_id,task_id)
        return len(result) > 1
    async def run(self):
        start_time = time.time()
        logger.info("log analysis start")
        file = self.file_path.split("/")[-1]
        target_path = f"{os.getcwd()}/tmp_testcase_logs/"
        mk_dir(target_path)
        SftpService().download_file(f"/home/<USER>/{file}",
                                    target_path)
        fail_testcases = []
        process_error_format_html(target_path + file)
        log_text = get_html_raw_info(target_path, file)
        suite_tests_name = get_html_testcase(log_text)
        filter_relevant_info(log_text, fail_testcases)
        res = []
        for testcase in fail_testcases:
            # question = testcase.get("message")
            # answer_res = await AiStudioApi(question).send()
            # testcase.update({"ai_result": str(answer_res)})
            case_log = TestCaseLog(
                testcase_name=testcase.get("testcase_name").split(".")[-1],
                msg=testcase.get("message", ""),
                detail_msg=testcase.get("detail_msg", ""),
                fail_keywords=testcase.get("fail_keywords", []),
                status=testcase.get("status"),
                source=f"/home/<USER>/{file}",
                # ai_result=str(answer_res)
                subtype=self.log_info.get("subtype", "normal"),
                task_id=self.log_info.get("task_id", ""),
                parent_suites=testcase.get("testcase_name")
            )
            result = self.classify_label(case_log, suite_tests_name)
            if fail_class["other"] == result and case_log.msg:
                result = case_log.msg
            case_log.result = result
            testcase.update({"result": result})
            TestCaseLogRepository().save(case_log)
            res.append(case_log)
        LoggingService.log_execution(
            service_type="log_save",
            operation="log_save",
            status="fail" if not res else "success",
            details={"message": self.log_info, "results_list": marshal(res)},
            task_id=self.log_info.get("task_id"),
            env_id=self.log_info.get("env_id"),
            service_key="log_analysis"
        )
        if self.query_task_is_exit(self.log_info.get("env_id", ""), self.log_info.get("task_id", "")):
            return self.get_abnormal_file_status(fail_testcases)
        excution_time = time.time() - start_time
        logger.info(f'log analysis end,excution time: {excution_time}s')
        await self.push_msg(fail_testcases)
        return res

    async def push_msg(self, fail_testcases):
        msg = EnvCheck(
            log_name=self.log_info.get("log_name"),
            job_name=self.log_info.get("job_name"),
            build_number=self.log_info.get("build_number"),
            env_id=self.log_info.get("env_id"),
            task_id=self.log_info.get("task_id"),
            service_type="env_check",
            log_analysis_results=fail_testcases,
            subtype=self.log_info.get("subtype", "normal")
        )

        from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
        await CiAgentsQueue().push_info(msg)

    def classify_label(self, testcase, suite_tests_name: list):
        if not testcase.msg:
            return fail_class.get("other")
        if self.parse_ue_error(testcase):
            return fail_class.get("ue_attach")
        if any(sub in testcase.testcase_name for sub in error_type_substrings.get("flooding")):
            return self.check_zero_comparison_sides(testcase.detail_msg)
        if any(sub in testcase.testcase_name for sub in error_type_substrings.get("ping")):
            return fail_class.get("ping")
        if any(sub in testcase.testcase_name for sub in error_type_substrings.get("attach")):
            return fail_class.get("attach")
        if any(sub in name for name in suite_tests_name for sub in error_type_substrings.get("upgrade")):
            return fail_class.get("upgrade")
        return fail_class.get("other")

    def classify_flooding(self, testcase):
        pattern = re.compile(r"Arguments:\s*\[\s*'([^']+)")
        num_pattern = re.compile(r"\d+(?:\.\d+)?")
        match = pattern.search(testcase.detail_msg)
        if match:
            content = match.group(1)
            numbers = num_pattern.findall(content)
            numbers = [float(num) for num in numbers]
            if any(num == 0 for num in numbers):
                return fail_class.get("flooding_zero")
            else:
                return fail_class.get("flooding_low")
        return fail_class.get("other")

    def check_zero_comparison_sides(self, text):
        match = re.search(r"Arguments:\s*\[\s*(.*?)\s*(\||\])", text)
        if not match:
            fail_class.get("other")
        content = match.group(1)
        items = re.findall(r"'(.*?)'", content)
        for item in items:
            comparisons = re.findall(r"([^\s<>]+)\s*([<>])\s*([^\s<>]+)", item)
            for left, op, right in comparisons:
                if eval(left) == 0 or eval(right) == 0:
                    return fail_class.get("flooding_zero")
        return fail_class.get("flooding_low")

    def get_abnormal_file_status(self, fail_testcases):
        if not fail_testcases:
            return {"status": "success"}
        return {"status": "fail"}

    def parse_ue_error(self, testcase):
        if ue_fail_rules.get("execute_error") in testcase.msg:
            return True
        if ue_fail_rules.get("name") in testcase.testcase_name and ue_fail_rules.get("assert_error") in testcase.msg:
            return True
        return False

