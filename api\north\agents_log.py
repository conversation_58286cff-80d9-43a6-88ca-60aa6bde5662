from fastapi import APIRouter, Query, Body
from domain.model.response.response import Response, ExceptionEnum
from domain.repository.agents_log import EsAgentsLog
import logging
from infrastructure.logger.logger import logger
from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
from domain.model.dto.logdto import LogJobInfo
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from domain.repository.es_jenkins_job import EsJenkinsJob
from domain.model.dto.env_check import EnvCheck
import json
from datetime import datetime

router = APIRouter(prefix="/agents_log")


@router.get("/", response_model=Response)
async def get_logs(
    service_type: str = Query(None, description="Filter by service type"),
    task_id: str = Query(None, description="Filter by task ID"),
    env_id: str = Query(None, description="Filter by environment ID"),
    status: str = Query(None, description="Filter by status (success, failure, in_progress)"),
    service_key: str = Query(None, description="Filter by service key (from AGENTS_SERVICE_OBJECT)"),
    limit: int = Query(100, description="Maximum number of logs to return")
):
    """
    Get agent execution logs with optional filtering
    """
    try:
        if service_type:
            _, logs = EsAgentsLog().get_logs_by_service_type(service_type, limit)
            return Response.build(logs)
        elif task_id:
            _, logs = EsAgentsLog().get_logs_by_task_id(task_id, limit)
            return Response.build(logs)
        elif env_id:
            _, logs = EsAgentsLog().get_logs_by_env_id(env_id, limit)
            return Response.build(logs)
        elif status:
            _, logs = EsAgentsLog().get_logs_by_status(status, limit)
            return Response.build(logs)
        elif service_key:
            _, logs = EsAgentsLog().get_logs_by_service_key(service_key, limit)
            return Response.build(logs)
        else:
            # Get all logs
            query = {
                "query": {"match_all": {}},
                "sort": [{"timestamp": {"order": "desc"}}],
                "size": limit
            }
            _, logs = EsAgentsLog().search(body=query)
            return Response.build(logs)
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取日志失败")


@router.get("/service_types", response_model=Response)
async def get_service_types():
    """
    Get a list of all service types in the logs
    """
    try:
        query = {
            "size": 0,
            "aggs": {
                "service_types": {
                    "terms": {"field": "service_type.keyword", "size": 100}
                }
            }
        }

        result = EsAgentsLog()._es.search(index=EsAgentsLog()._index, body=query)
        service_types = [bucket["key"] for bucket in result["aggregations"]["service_types"]["buckets"]]

        return Response.build(service_types)
    except Exception as e:
        logger.error(f"Error retrieving service types: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取服务类型列表失败")


@router.get("/operations", response_model=Response)
async def get_operations():
    """
    Get a list of all operations in the logs
    """
    try:
        query = {
            "size": 0,
            "aggs": {
                "operations": {
                    "terms": {"field": "operation.keyword", "size": 100}
                }
            }
        }

        result = EsAgentsLog()._es.search(index=EsAgentsLog()._index, body=query)
        operations = [bucket["key"] for bucket in result["aggregations"]["operations"]["buckets"]]

        return Response.build(operations)
    except Exception as e:
        logger.error(f"Error retrieving operations: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取操作列表失败")


@router.get("/service_keys", response_model=Response)
async def get_service_keys():
    """
    Get a list of all service keys in the logs
    """
    try:
        query = {
            "size": 0,
            "aggs": {
                "service_keys": {
                    "terms": {"field": "service_key.keyword", "size": 100}
                }
            }
        }

        result = EsAgentsLog()._es.search(index=EsAgentsLog()._index, body=query)
        service_keys = [bucket["key"] for bucket in result["aggregations"]["service_keys"]["buckets"]]

        return Response.build(service_keys)
    except Exception as e:
        logger.error(f"Error retrieving service keys: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg="获取服务键列表失败")


async def _find_log_entry(task_id: str, env_id: str, service_key: str, operation: str):
    """
    Find the most recent log entry for a step, prioritizing in_progress state.

    Returns:
        tuple: (log_entry, used_in_progress)
    """
    # First, try to find the most recent in_progress log entry
    in_progress_query = {
        "query": {
            "bool": {
                "must": [
                    {"term": {"task_id.keyword": task_id}},
                    {"term": {"env_id.keyword": env_id}},
                    {"term": {"service_key.keyword": service_key}},
                    {"term": {"operation.keyword": operation}},
                    {"term": {"status.keyword": "in_progress"}}
                ]
            }
        },
        "sort": [{"timestamp": {"order": "desc"}}],
        "size": 1
    }

    _, in_progress_logs = EsAgentsLog().search(body=in_progress_query)

    # If found, use the in_progress log
    if in_progress_logs:
        log_entry = in_progress_logs[0]
        logger.info(f"Using in_progress log for retry: {log_entry.get('id', 'unknown')}")
        return log_entry, True

    # Otherwise, fall back to the most recent log (likely failure)
    failure_query = {
        "query": {
            "bool": {
                "must": [
                    {"term": {"task_id.keyword": task_id}},
                    {"term": {"env_id.keyword": env_id}},
                    {"term": {"service_key.keyword": service_key}},
                    {"term": {"operation.keyword": operation}}
                ]
            }
        },
        "sort": [{"timestamp": {"order": "desc"}}],
        "size": 1
    }

    _, logs = EsAgentsLog().search(body=failure_query)

    if not logs:
        return None, False

    log_entry = logs[0]
    logger.warning(f"No in_progress log found for retry, using most recent log: {log_entry.get('id', 'unknown')}")
    return log_entry, False


async def _retry_jenkins_job_save(task_id: str, env_id: str, service_key: str):
    """
    Retry a jenkins_job_save operation by retrieving the original job and pushing it to the queue.
    """
    logger.info(f"Handling retry for jenkins_job_save service")
    queue = CiAgentsQueue()

    # Find the most recent jenkins job record for this task_id
    jenkins_jobs = EsJenkinsJob().query_by_filter_sort({"task_id": task_id}, "timestamp", size=1)

    if not jenkins_jobs or not jenkins_jobs[1]:
        return Response.from_exception(
            ExceptionEnum.NOT_FOUND,
            msg=f"No jenkins job found for task_id: {task_id}"
        )

    # Get the jenkins job record and update timestamp
    jenkins_job_record = jenkins_jobs[1][0]
    logger.info(f"Found jenkins job record: {json.dumps(jenkins_job_record, default=str)[:200]}...")
    jenkins_job_record["timestamp"] = datetime.now().isoformat()

    try:
        # Create and push JenkinsJobInfo object
        jenkins_job_info = JenkinsJobInfo(**jenkins_job_record)
        await queue.push_info(jenkins_job_info)
        logger.info(f"Successfully queued jenkins_job_save retry for task_id: {task_id}")

        # Return success response
        return Response.build({
            "success": True,
            "message": f"Successfully queued retry for jenkins_job_save",
            "task_id": task_id,
            "env_id": env_id,
            "service_key": service_key,
            "retry_timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Error creating JenkinsJobInfo: {str(e)}")
        return Response.from_exception(
            ExceptionEnum.BAD_REQUEST,
            msg=f"Error creating JenkinsJobInfo: {str(e)}"
        )


def _extract_message_from_details(details: dict, base_message: dict):
    """
    Extract message data from log entry details, handling various formats.
    """
    message = base_message.copy()

    if not isinstance(details, dict):
        return message

    logger.info(f"Found details in log entry: {json.dumps(details, default=str)[:200]}...")

    # Case 1: Message field exists as a dictionary
    if "message" in details and isinstance(details["message"], dict):
        msg_data = details["message"]
        logger.info(f"Found message dict in details, preserving entire structure")

        # Check if the message contains complex nested structures
        has_complex_structure = any(isinstance(v, (dict, list)) for v in msg_data.values())

        if has_complex_structure:
            # For complex structures, preserve the entire message object
            logger.info(f"Message contains complex nested structures, preserving as a whole")
            message["message"] = msg_data
        else:
            # For simple structures, copy individual fields
            for key, value in msg_data.items():
                if key not in message:  # Don't overwrite task_id, env_id, service_type
                    message[key] = value

    # Case 2: Message field exists as a string (possibly JSON)
    elif "message" in details and isinstance(details["message"], str):
        try:
            # Try to parse as JSON
            parsed_message = json.loads(details["message"])
            if isinstance(parsed_message, dict):
                logger.info(f"Parsed message string as JSON dict")
                for key, value in parsed_message.items():
                    if key not in message:
                        message[key] = value
        except json.JSONDecodeError:
            # Not JSON, just use as is
            if "message" not in message:
                message["message"] = details["message"]

        logger.info(f"Extracted message details from log entry")

    # Case 3: No message field, use filtered details
    else:
        logger.info(f"No message field found, using entire details object")

        if "message" not in message:
            # Filter out error-related fields
            filtered_details = {k: v for k, v in details.items()
                              if k not in ["error", "traceback", "result"]}

            # If we have a complex structure, preserve it entirely
            if any(isinstance(v, (dict, list)) for v in filtered_details.values()):
                logger.info(f"Found complex nested structure in details, preserving entire object")
                message["message"] = filtered_details
            else:
                # For simple key-value pairs, add them directly to the message
                for key, value in filtered_details.items():
                    if key not in message:
                        message[key] = value

        logger.info(f"Used details object for message context")

    return message


def _extract_log_job_info_data(message: dict):
    """
    Extract the required fields for LogJobInfo from a message.
    """
    log_job_info_data = {
        "task_id": message.get("task_id"),
        "env_id": message.get("env_id"),
        "service_type": message.get("service_type")
    }

    # Extract from nested message if available
    if "message" in message and isinstance(message["message"], dict):
        msg_data = message["message"]

        for field in ["log_name", "job_name", "build_number", "subtype"]:
            if field in msg_data:
                log_job_info_data[field] = msg_data[field]
    else:
        # Extract from top-level message
        for field in ["log_name", "job_name", "build_number", "subtype"]:
            if field in message:
                log_job_info_data[field] = message[field]

    return log_job_info_data


def _has_complex_structure(message: dict):
    """
    Check if a message has complex nested structures that need special handling.
    """
    if "message" not in message or not isinstance(message["message"], dict):
        return False

    msg_data = message["message"]

    # Check for log_analysis_results which indicates complex structure
    if "log_analysis_results" in msg_data and isinstance(msg_data["log_analysis_results"], list):
        return True

    return False


def _is_env_check(message: dict):
    """
    Check if a message is for the env_check service.
    """
    if not _has_complex_structure(message):
        return False

    msg_data = message["message"]
    return "service_type" in msg_data and msg_data["service_type"] == "env_check"


async def _process_message_retry(message: dict, queue: CiAgentsQueue):
    """
    Process a retry for a process_message operation.
    """
    # Log the message structure
    message_json = json.dumps(message, default=str)
    logger.info(f"Prepared message for retry: {message_json[:200]}...")

    # Log nested structure details if present
    if "message" in message and isinstance(message["message"], dict):
        msg_keys = list(message["message"].keys())
        logger.info(f"Message contains nested structure with keys: {msg_keys}")

        if "log_analysis_results" in message["message"]:
            results = message["message"]["log_analysis_results"]
            if isinstance(results, list):
                logger.info(f"Found log_analysis_results array with {len(results)} items")

    # Extract LogJobInfo data
    log_job_info_data = _extract_log_job_info_data(message)
    logger.info(f"Creating LogJobInfo with data: {json.dumps(log_job_info_data, default=str)[:200]}...")

    # Handle different message types
    if _is_env_check(message):
        # For env_check service, create an EnvCheck object
        try:
            logger.info(f"Creating EnvCheck object for env_check service")
            env_check_data = message["message"]
            env_check = EnvCheck(**env_check_data)
            await queue.push_info(env_check)
            logger.info(f"Successfully pushed EnvCheck to queue")
        except Exception as e:
            logger.error(f"Error creating EnvCheck: {str(e)}")
            logger.info(f"Falling back to pushing raw message")
            await queue.push_info(message)
    elif _has_complex_structure(message):
        # For other complex structures, push the raw message directly
        logger.info(f"Message has complex nested structure, pushing raw message directly")
        await queue.push_info(message)
    else:
        try:
            # Create and push the LogJobInfo object
            log_job_info = LogJobInfo(**log_job_info_data)
            await queue.push_info(log_job_info)
            logger.info(f"Successfully pushed LogJobInfo to queue")
        except Exception as e:
            # If there's an error creating LogJobInfo, fall back to pushing the raw message
            logger.error(f"Error creating LogJobInfo: {str(e)}")
            logger.info(f"Falling back to pushing raw message")
            await queue.push_info(message)


@router.post("/retry", response_model=Response)
async def retry_failed_step(
    task_id: str = Body(..., description="Task ID of the failed step"),
    env_id: str = Body(..., description="Environment ID of the failed step"),
    service_key: str = Body(..., description="Service key of the failed step"),
    operation: str = Body(..., description="Operation of the failed step")
):
    """
    Retry a failed step in the pipeline by pushing a new message to the queue
    with the same context data as the original message
    """
    try:
        # Special handling for jenkins_job_save service type
        if service_key == "jenkins_job_save":
            return await _retry_jenkins_job_save(task_id, env_id, service_key)

        # For other service types, find the log entry
        log_entry, used_in_progress = await _find_log_entry(task_id, env_id, service_key, operation)

        if not log_entry:
            return Response.from_exception(
                ExceptionEnum.NOT_FOUND,
                msg="No log entry found for the specified parameters"
            )

        # Only process_message and run operations are supported for non-jenkins_job_save services
        if operation not in ["process_message", "run"]:
            return Response.from_exception(
                ExceptionEnum.BAD_REQUEST,
                msg=f"Retry is only supported for 'jenkins_job_save' service type or 'process_message'/'run' operations, got service_key='{service_key}', operation='{operation}'"
            )

        # Create base message
        message = {
            "task_id": task_id,
            "env_id": env_id,
            "service_type": service_key
        }

        # Extract message from log entry details
        if "details" in log_entry and isinstance(log_entry["details"], dict):
            message = _extract_message_from_details(log_entry["details"], message)

        # Initialize queue
        queue = CiAgentsQueue()

        # Process the message based on operation type
        if operation == "process_message":
            await _process_message_retry(message, queue)
        else:
            # For other operations, push the message directly
            await queue.push_info(message)

        # Return success response
        return Response.build({
            "success": True,
            "message": f"Successfully queued retry for {service_key} {operation}",
            "task_id": task_id,
            "env_id": env_id,
            "service_key": service_key,
            "operation": operation,
            "used_in_progress_state": used_in_progress,
            "retry_timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error retrying failed step: {str(e)}")
        return Response.from_exception(ExceptionEnum.SERVER_FAILED, msg=f"Failed to retry step: {str(e)}")
