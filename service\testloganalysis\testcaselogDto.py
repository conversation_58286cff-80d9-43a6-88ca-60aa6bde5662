"""
@Author: <EMAIL>
@Date: 2025/4/22 下午7:31
@File: testcaselogDto.py
@Description: 
"""
from pydantic import BaseModel, Field, model_validator
from typing import Literal, Optional
from datetime import datetime


class LogQueryInfo(BaseModel):
    task_id: Optional[str] = None
    startTime: Optional[str] = None
    endTime: Optional[str] = None
    curPage: int = 1
    pageSize: int = 10
    testcase_name: Optional[str] = None
    msg: Optional[str] = None
    detail_msg: Optional[str] = None
    status: str = ""
    env_id: str = ""
    updatedTime: str = ""
    updatedBy: str = ""
    source: str = ""
    ai_result: str = ""
    result: str = ""
    manualAnalysisResult: str = ""
    subtype: Optional[Literal['normal', "first_time", 'rollback', 'update']] = "normal"
    manualClassification: str = ""


class ManualConfigInfo(BaseModel):
    task_id: str
    testcase_name: str
    manualClassification: Optional[str] = None
    manualAnalysisResult: Optional[str] = None
