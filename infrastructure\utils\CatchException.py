# coding=utf-8
import logging
import traceback


def catchExceptionReturnTrueFalse(func):
    def dec(*args, **kw):
        try:
            func(*args, **kw)
            return True
        except Exception as e:
            print(traceback.print_exc())
            logging.error(e)
            return False

    return dec


def catchExceptionReturnOriginValue(func):
    def dec(*args, **kw):
        try:
            return func(*args, **kw)
        except Exception as e:
            print(traceback.print_exc())
            logging.error(e)
            return None

    return dec
