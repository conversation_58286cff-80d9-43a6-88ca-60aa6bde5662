# -*- coding = utf-8 -*- #
# @Time : 2024/4/23 19:29
# <AUTHOR> 10333573
import datetime
import os

import asyncio

from infrastructure.logger.logger import logger
from infrastructure.utils.scripts.Executor import _exec


async def run(script, timeout=5, decode="GBK", **kwargs):
    if timeout >= 60 * 30:
        timeout = 60 * 30
        logger.warn(f"Maximum script execution time allowed by the biz-code is {timeout} seconds")
    args = kwargs.get("args", [])
    rlt = await _exec(g_bat_cmd(script, *args), timeout)
    return rlt["stdout"].decode(decode), rlt["stderr"].decode(decode)


def g_bat_cmd(s, *args):
    if not os.path.exists(s):
        raise FileNotFoundError(f"Script not found: {s}")

    # 判断脚本类型，构造不同的执行命令
    ext = os.path.splitext(s)[1].lower()

    if ext == ".bat":
        cmd = ["cmd.exe", "/c", s] + list(args)
    elif ext == ".sh":
        cmd = ["bash", s] + list(args)
    elif ext == ".py":
        cmd = ["python", s] + list(args)
    else:
        raise ValueError(f"Unsupported script type: {ext}")
    return cmd


def t_f1(s, **t):
    loop = asyncio.get_event_loop()
    a = loop.run_until_complete(run(s, **t))
    logger.info(f"out:")
    logger.info(a[0])
    logger.info(f"err:")
    logger.info(a[1])
