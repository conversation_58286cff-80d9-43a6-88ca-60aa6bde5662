import os, sys, stat, logging, shutil
import paramiko

from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_flag

_XFER_FILE = "file"
_XFER_DIR = "dir"


class Sftp():
    def __init__(self, sftp_conf: dict):
        self.host = sftp_conf["host"]
        self.port = sftp_conf["port"]
        self.user = sftp_conf["username"]
        self.pwd = sftp_conf["password"]
        self.__transport = None
        self.sftp = None
        self.connect()

    def __del__(self):
        """在对象被销毁时关闭SFTP连接"""
        self.close()

    @retries_on_flag(3, isRaiseException=True, everyTryDelaySecs=60)
    def connect(self):
        try:
            self.__transport = paramiko.Transport((self.host, self.port))
            self.__transport.connect(username=self.user, password=self.pwd)
            self.sftp = paramiko.SFTPClient.from_transport(self.__transport)
        except Exception as e:
            logger.error("连接失败，请检查sftp配置信息！:{0}".format(e))
            self.close()
            return False

    def close(self):
        if self.sftp:
            self.sftp.close()
        if self.__transport:
            self.__transport.close()

    # self.sftp.chdir 保存远处的路径
    @retries_on_flag(3, isRaiseException=False, everyTryDelaySecs=3)
    def upload_multi(self, local_path, remote_path="/sftpuser", replace=True, new_name=None):
        local_path = local_path.replace('\\', '/')
        remote_path = remote_path.replace('\\', '/')
        if not os.path.exists(local_path):
            logging.error("本地资源不存在，请检查：" + local_path)
            raise Exception("本地资源不存在，请检查：" + local_path)
        if not self.make_dir(remote_path):
            return False
        filetype, filename = self._get_file_type(local_path)
        if new_name:
            filename = new_name
        if filetype == _XFER_DIR:
            self.upload_dir(local_path, remote_path, replace)
        elif filetype == _XFER_FILE:
            self.upload_file(local_path, filename, replace)
        # self.remove_uploaded()
        return True

    def upload_dir(self, local_dir, remote_dir, isReplace):
        for file in os.listdir(local_dir):
            filepath = os.path.join(local_dir, file)
            if os.path.isfile(filepath):
                self.upload_file(filepath, file, isReplace)
            elif os.path.isdir(filepath):
                try:
                    self.sftp.chdir(file)
                except:
                    self.sftp.mkdir(file)
                    self.sftp.chdir(file)
                self.upload_dir(filepath, file, isReplace)
        self.sftp.chdir('..')

    def upload_file(self, filepath, filename, isReplace):
        if not os.path.isfile(filepath) or not os.path.exists(filepath) or self.sftp is None:
            logging.warning("请检查: 上传非文件或者本地文件不存在或者sftp未连接")
            return

        if not isReplace:
            if filename in self.sftp.listdir():
                logging.warning(
                    '[*] 这个文件已经存在了，选择跳过:' + filepath + ' -> ' + self.sftp.getcwd() + '/' + filename)
                return
        try:
            self.sftp.put(filepath, filename)
            # logging.info('[+] 上传成功:' + filepath + ' -> ' + self.sftp.getcwd() + '/' + filename)
        except Exception as e:
            logging.error('[+] 上传失败:' + filepath + ' because ' + str(e))

    # create tagetdir
    def make_dir(self, target_path):
        try:
            self.sftp.chdir('/')
            dirs = target_path.split('/')
        except:
            return False

        for dir in dirs:
            try:
                self.sftp.chdir(dir)
            except:
                self.sftp.mkdir(dir, mode=0o777)
                self.sftp.chdir(dir)
                logging.info("create dir:{0}".format(dir))
        return True

    def _get_file_type(self, local_dir):
        if os.path.isfile(local_dir):
            index = local_dir.rfind('/')
            return _XFER_FILE, local_dir[index + 1:]
        if os.path.isdir(local_dir):
            return _XFER_DIR, ""

    def list_remote_dir(self, target_dir):
        return self.sftp.listdir(target_dir)

    # 非常小心定义local文件，不然会删除本地文件。
    def remove_uploaded(self, local_path):
        try:
            shutil.rmtree(local_path)
        except:
            os.remove(local_path)

    def download_file(self, remoteFile, localFile):
        localPath = os.path.split(localFile)[0]
        localFileName = os.path.split(localFile)[1]
        if not os.path.exists(localPath):
            os.mkdir(localPath)
        if localFileName == '':
            localFile = os.path.join(localFile, os.path.split(remoteFile)[1]) 
        self.sftp.get(remoteFile, localFile)
        return localFile


if __name__ == "__main__":
    pass
