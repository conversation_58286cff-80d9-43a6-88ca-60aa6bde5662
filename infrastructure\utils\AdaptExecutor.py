import json
import logging

from infrastructure.const import common
from infrastructure.utils.HttpResponse import HttpResponse
from infrastructure.utils.utils import get_user_locale, VALIDATED_FAILED


class AdaptExecutor(object):
    def __init__(self, svc_err_conn_info: str, svc_err_code_info: str, svc_err_base_info: str,
                 svc_exception_cls, adapt_exec_func):
        self._svc_err_conn_info = svc_err_conn_info
        self._svc_err_code_info = svc_err_code_info
        self._svc_err_base_info = svc_err_base_info
        self._svc_exception_cls = svc_exception_cls
        self._adapt_exec_func = adapt_exec_func

    @property
    def svc_err_conn_info(self):
        return self._svc_err_conn_info

    @property
    def svc_err_code_info(self):
        return self._svc_err_code_info

    @property
    def svc_err_base_info(self):
        return self._svc_err_base_info

    @property
    def svc_exception_cls(self):
        return self._svc_exception_cls

    @property
    def adapt_exec_func(self):
        return self._adapt_exec_func

    def adapt_and_execute_cmd(self, interface_key, attr_dict: dict = {}):
        locale_translator = get_user_locale(attr_dict.get(common.LANG_ID, common.ZH_CN)).translate
        response = self._adapt_exec_func(interface_key, attr_dict)
        if response is None:
            raise self._svc_exception_cls(self._svc_err_conn_info)
        if response.status_code >= 300 or response.status_code < 200:
            raise self._svc_exception_cls(self._svc_err_code_info, {common.CODE: response.status_code})
        try:
            http_response = HttpResponse(json.loads(response.text))
        except Exception as err:
            logging.error("%s: %s" % (self._svc_err_base_info, response.text))
            logging.error(err, exc_info=True)
            raise self._svc_exception_cls(response.text)
        if http_response.is_success():
            return response
        # todo bugfix ValidatedFailed Response not add prefix _svc_err_base_infos
        err_msg = "%s " % locale_translator(self._svc_err_base_info) + http_response.failReason
        logging.error("%s:%s" % (err_msg, http_response.bo))
        raise self._svc_exception_cls(err_msg)
