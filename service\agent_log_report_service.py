# -*-coding:utf-8-*-
import json
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional

from domain.repository.agents_log import EsAgentsLog
from domain.repository.es_env import EsPcEnv, EsVersionEnv
from infrastructure.logger.logger import logger


class AgentLogReportService:
    """
    Agent日志报告服务，用于获取和处理agent_log数据
    """
    
    def __init__(self):
        """初始化服务"""
        self._agents_log = EsAgentsLog()
        self._biz_core_env = EsPcEnv()
        self._version_env = EsVersionEnv()
        
    def get_all_logs_report(self, limit: int = 1000, skip: int = 0) -> List[Dict[str, Any]]:
        """
        获取所有日志记录的报告
        
        Args:
            limit: 返回记录的最大数量
            skip: 跳过的记录数量
            
        Returns:
            List[Dict[str, Any]]: 格式化后的日志报告列表
        """
        try:
            # 获取所有日志记录
            query = {
                "query": {"match_all": {}},
                "sort": [{"timestamp": {"order": "desc"}}],
                "from": skip,
                "size": limit
            }
            total, logs = self._agents_log.search(body=query)
            
            logger.info(f"从agents_log获取到 {total} 条记录，实际返回 {len(logs)} 条")
            
            if not logs:
                logger.warning("未找到任何日志记录")
                return []
            
            # 处理日志记录
            return self._process_logs(logs)
            
        except Exception as e:
            logger.error(f"获取日志报告时发生错误: {str(e)}")
            return []
    
    def get_logs_by_task_id(self, task_id: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取指定任务ID的日志报告
        
        Args:
            task_id: 任务ID
            limit: 返回记录的最大数量
            
        Returns:
            List[Dict[str, Any]]: 格式化后的日志报告列表
        """
        try:
            # 获取指定任务ID的日志记录
            _, logs = self._agents_log.get_logs_by_task_id(task_id, limit)
            
            if not logs:
                logger.warning(f"未找到任务ID为 {task_id} 的日志记录")
                return []
            
            # 处理日志记录
            return self._process_logs(logs)
            
        except Exception as e:
            logger.error(f"获取任务 {task_id} 的日志报告时发生错误: {str(e)}")
            return []
    
    def get_logs_by_env_id(self, env_id: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取指定环境ID的日志报告
        
        Args:
            env_id: 环境ID
            limit: 返回记录的最大数量
            
        Returns:
            List[Dict[str, Any]]: 格式化后的日志报告列表
        """
        try:
            # 获取指定环境ID的日志记录
            _, logs = self._agents_log.get_logs_by_env_id(env_id, limit)
            
            if not logs:
                logger.warning(f"未找到环境ID为 {env_id} 的日志记录")
                return []
            
            # 处理日志记录
            return self._process_logs(logs)
            
        except Exception as e:
            logger.error(f"获取环境 {env_id} 的日志报告时发生错误: {str(e)}")
            return []
    
    def _process_logs(self, logs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理日志记录，提取所需信息并格式化
        
        Args:
            logs: 原始日志记录列表
            
        Returns:
            List[Dict[str, Any]]: 格式化后的日志报告列表
        """
        result = []
        env_principal_cache = {}  # 缓存环境ID到负责人的映射
        
        for log in logs:
            env_id = log.get("env_id", "")
            task_id = log.get("task_id", "")
            
            # 跳过没有环境ID的记录
            if not env_id:
                continue
            
            # 获取负责人信息（使用缓存避免重复查询）
            principal = env_principal_cache.get(env_id)
            if principal is None:
                principal = self._get_principal(env_id)
                env_principal_cache[env_id] = principal
            
            # 获取版本信息
            version_info = self._get_version_info(task_id, env_id)
            
            # 获取子类型信息
            subtype = self._get_subtype(log)
            
            # 计算持续时间
            duration = self._calculate_duration(log)
            
            # 构建记录
            record = {
                "timestamp": log.get("timestamp", ""),
                "env_id": env_id,
                "task_id": task_id,
                "principal": principal,
                "version_test_result": "success" if version_info.get("version_test_result") == "True" else "failure",
                "version_branch": version_info.get("version_branch", ""),
                "subtype": subtype,
                "version": version_info.get("curr_version", ""),
                "duration": duration
            }
            
            result.append(record)
        
        return result
    
    def _get_principal(self, env_id: str) -> str:
        """
        获取环境的负责人
        
        Args:
            env_id: 环境ID
            
        Returns:
            str: 负责人信息
        """
        try:
            _, env_data = self._biz_core_env.query_by_filter_without_sort({"env_id": env_id})
            if env_data and len(env_data) > 0:
                return env_data[0].get("principal", "未知")
            return "未知"
        except Exception as e:
            logger.error(f"获取环境 {env_id} 的负责人信息时发生错误: {str(e)}")
            return "未知"
    
    def _get_version_info(self, task_id: str, env_id: str) -> Dict[str, Any]:
        """
        获取版本信息
        
        Args:
            task_id: 任务ID
            env_id: 环境ID
            
        Returns:
            Dict[str, Any]: 版本信息
        """
        try:
            if not task_id or not env_id:
                return {}
                
            _, version_data = self._version_env.query_by_filter_without_sort({
                "task_id": task_id,
                "env_id": env_id
            })
            
            if version_data and len(version_data) > 0:
                return version_data[0]
            return {}
        except Exception as e:
            logger.error(f"获取任务 {task_id} 环境 {env_id} 的版本信息时发生错误: {str(e)}")
            return {}
    
    def _get_subtype(self, log: Dict[str, Any]) -> str:
        """
        从日志中提取子类型信息
        
        Args:
            log: 日志记录
            
        Returns:
            str: 子类型
        """
        # 首先检查日志本身的subtype字段
        subtype = log.get("subtype", "")
        if subtype:
            return subtype
        
        # 然后检查details中的subtype字段
        details = log.get("details", {})
        if isinstance(details, dict):
            subtype = details.get("subtype", "")
            if subtype:
                return subtype
            
            # 检查message中的subtype
            message = details.get("message", {})
            if isinstance(message, dict):
                subtype = message.get("subtype", "")
                if subtype:
                    return subtype
        
        # 根据操作类型推断子类型
        operation = log.get("operation", "")
        if "rollback" in operation.lower():
            return "rollback"
        elif "retest" in operation.lower():
            return "retest"
        elif "first" in operation.lower() or "initial" in operation.lower():
            return "first"
        
        # 默认为normal
        return "normal"
    
    def _calculate_duration(self, log: Dict[str, Any]) -> str:
        """
        计算操作持续时间
        
        Args:
            log: 日志记录
            
        Returns:
            str: 持续时间，格式为 HH:MM:SS
        """
        try:
            # 尝试从日志中获取开始和结束时间
            start_time = None
            end_time = None
            
            # 从details中获取时间信息
            details = log.get("details", {})
            if isinstance(details, dict):
                # 尝试获取start_time和end_time
                start_time_str = details.get("start_time")
                end_time_str = details.get("end_time")
                
                # 如果没有明确的start_time和end_time，尝试从其他字段获取
                if not start_time_str and not end_time_str:
                    # 尝试从begin_time和finish_time获取
                    start_time_str = details.get("begin_time")
                    end_time_str = details.get("finish_time")
                    
                    # 尝试从start和end获取
                    if not start_time_str and not end_time_str:
                        start_time_str = details.get("start")
                        end_time_str = details.get("end")
            
                # 处理时间字符串
                if start_time_str and end_time_str:
                    try:
                        # 尝试多种时间格式
                        for fmt in [
                            # ISO格式
                            lambda s: datetime.fromisoformat(s.replace('Z', '+00:00')),
                            # 时间戳格式（秒）
                            lambda s: datetime.fromtimestamp(float(s)),
                            # 时间戳格式（毫秒）
                            lambda s: datetime.fromtimestamp(float(s) / 1000),
                            # 常见日期时间格式
                            lambda s: datetime.strptime(s, "%Y-%m-%d %H:%M:%S"),
                            lambda s: datetime.strptime(s, "%Y/%m/%d %H:%M:%S"),
                        ]:
                            try:
                                start_time = fmt(start_time_str)
                                end_time = fmt(end_time_str)
                                break
                            except (ValueError, TypeError):
                                continue
                    except Exception:
                        pass
            
            # 如果没有找到开始和结束时间，尝试使用日志的timestamp和当前时间
            if not start_time or not end_time:
                timestamp_str = log.get("timestamp")
                if timestamp_str:
                    try:
                        # 尝试解析timestamp
                        start_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        # 如果有status为"in_progress"，使用当前时间作为结束时间
                        if log.get("status") == "in_progress":
                            end_time = datetime.now()
                    except ValueError:
                        pass
            
            # 如果找到了开始和结束时间，计算持续时间
            if start_time and end_time:
                # 确保end_time大于start_time
                if end_time > start_time:
                    duration = end_time - start_time
                    # 格式化为小时:分钟:秒
                    hours, remainder = divmod(duration.total_seconds(), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            
            # 如果无法计算持续时间，返回空字符串
            return ""
        except Exception as e:
            logger.error(f"计算持续时间时发生错误: {str(e)}")
            return ""


if __name__ == "__main__":
    # 创建服务实例
    service = AgentLogReportService()
    
    # 直接获取所有日志报告
    print("正在获取所有日志报告...")
    logs = service.get_all_logs_report(limit=1000)
    
    # 打印结果
    print(f"找到 {len(logs)} 条记录")
    print(json.dumps(logs, indent=2, ensure_ascii=False))
