import atexit
import json
import re
import ssl
import threading
from time import sleep

from websocket import create_connection

from infrastructure.db.redis.RedisPool import redisClient
from infrastructure.logger.logger import logger
from infrastructure.utils.ContextManager import teardown
from infrastructure.utils.FileHandler import FileHandler
from infrastructure.utils.ObjectRepository import ObjectRepository
from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.utils.Singleton import Singleton

MILLISECONDS = 1000
GRANULARITY = 100
UME_MASTER_VERSION = "UME R18 16.21.20"


class WebSocketClient(object):

    def __init__(self, deviceAttrDict, interfaceFolder, timeout=5):
        self._sessionKeys = []
        self._deviceIp = deviceAttrDict.get("ip")
        self._devicePort = deviceAttrDict.get("port")
        self._cookie = deviceAttrDict.get("Cookie")
        self._version = deviceAttrDict.get("version", "")
        self._forgerydefense = deviceAttrDict.get("forgerydefense")
        self._basicUrl = "wss://%(ip)s:%(port)s" % deviceAttrDict
        self._interfacesDict = FileHandler.load_json_file(interfaceFolder)
        self._timeout = timeout
        self._redis = redisClient()
        atexit.register(self.close)

    def __del__(self):
        try:
            self.close()
        except Exception as _:
            pass

    def login(self, username, password):
        pass

    def execute_websocket_cmd(self, interfaceKey, attr, recvNum=1, key='1111111111', secWebSocketProtocol='-',
                              isSendCmd=True):
        @retries_on_exception(3, self._close_session_by_key, key, exceptions=(Exception))
        def _execute_websocket_cmd(interfaceKey, attr, recvNum, key):
            interfacesDict = self._get_interface_dict(interfaceKey)
            if isinstance(attr, dict):
                interfaceStr = self._handle_struct_variable(json.dumps(interfacesDict) % attr, attr)
                try:
                    cmd, url = json.dumps(json.loads(interfaceStr).get('body')), json.loads(interfaceStr).get("uri")
                except:
                    cmd = interfacesDict.get("body") % attr
                    url = interfacesDict.get("uri") % attr
            else:
                cmd, url = attr, interfacesDict.get("uri")
            ws = self._send('{0}{1}'.format(self._basicUrl, url), cmd, key, secWebSocketProtocol, isSendCmd)
            return self._receive(ws, recvNum, key)

        return _execute_websocket_cmd(interfaceKey, attr, recvNum, key)

    def _send(self, url, content, key, secWebSocketProtocol='-', isSendCmd=True):
        sessionKey = key

        @retries_on_exception(3, self._close_session_by_key, sessionKey, exceptions=(Exception))
        def _send_message(url, content):
            if not SessionRepository().find(sessionKey):
                session = create_connection(url, timeout=self._timeout, header={"Cookie": "{0}".format(self._cookie),
                                                                                "Sec-WebSocket-Protocol": secWebSocketProtocol},
                                            sslopt={"cert_reqs": ssl.CERT_NONE})
                SessionRepository().add(sessionKey, session)
                self._sessionKeys.append(sessionKey)
            else:
                session = SessionRepository().find(sessionKey)
            if isSendCmd:
                session.send(content)
                logger.debug("websocket client sent:'{0}'".format(content))
            return session

        return _send_message(url, content)

    def _receive(self, ws, receivedCount, key):
        thread, receivedData = None, []
        if self._is_continuous(receivedCount):
            thread = threading.Thread(target=self.receive_continuous, args=(ws, receivedCount, key))
            thread.daemon = True
            thread.start()
        else:
            self.receive_once(ws, receivedData)
        return receivedData, thread

    def receive_continuous(self, ws, receivedCount, key):

        @teardown(ws.close)
        def _receive_business_data():
            self._redis.rpush(key, ws.recv())
            self._redis.expire(key, 3 * 86400)
            for _ in range(1, receivedCount):
                try:
                    self._redis.rpush(key, ws.recv())
                except Exception as _:
                    pass

        return _receive_business_data()

    def receive_once(self, ws, receivedData):
        def _receive_server_cmd():
            sleep(1)
            data = ws.recv()
            receivedData.append(data)

        return _receive_server_cmd()

    def close(self):
        for key in self._sessionKeys:
            self._close_session(SessionRepository().find(key))
        self._sessionKeys = []
        SessionRepository().clear()

    def _handle_struct_variable(self, interfaceStr, attrDict):
        try:
            interfaceDict = json.loads(interfaceStr)
        except:
            return interfaceStr
        if ',' in interfaceDict.get('uri'):
            interfaceDict.update({'uri': interfaceDict.get('uri').replace(',', '%2C')})
            interfaceStr = json.dumps(interfaceDict)
        structVariables = re.findall("#(.+?)#", interfaceStr)
        for variable in structVariables:
            if variable not in attrDict.keys():
                logger.error("Variable {0} Not Found".format(variable))
            interfaceStr = interfaceStr.replace('\"#{0}#\"'.format(variable), json.dumps(attrDict.get(variable, None)))
        return interfaceStr

    def is_exist(self, interfaceKey):
        return interfaceKey in self._interfacesDict.keys()

    def _close_session_by_key(self, key):
        session = SessionRepository().find(key)
        SessionRepository().delete(key)
        self._close_session(session)
        if key in self._sessionKeys:
            self._sessionKeys.remove(key)

    def _close_session(self, session):
        try:
            session.close()
        except Exception as _:
            pass

    def _get_interface_dict(self, interfaceKey):
        return self._interfacesDict.get(interfaceKey)

    def _is_continuous(self, receivedCount):
        return receivedCount > 1

    def connect_websocket_and_receive(self, interfaceKey, time=1, key='1111111111'):
        interfacesDict = self._get_interface_dict(interfaceKey)
        url = '{0}{1}'.format(self._basicUrl, interfacesDict.get("uri"))
        sessionKey = url + key

        @retries_on_exception(3, self._close_session_by_key, sessionKey, exceptions=(Exception))
        def _create_session(url):
            if not SessionRepository().find(sessionKey):
                session = create_connection(url, timeout=self._timeout, header={"Cookie": "{0}".format(self._cookie),
                                                                                "Sec-WebSocket-Protocol": "-"},
                                            sslopt={"cert_reqs": ssl.CERT_NONE})
                SessionRepository().add(sessionKey, session)
                self._sessionKeys.append(sessionKey)
            else:
                session = SessionRepository().find(sessionKey)
            return session

        session = _create_session(url)
        return session.recv()


@Singleton
class SessionRepository(ObjectRepository):

    def __init__(self):
        ObjectRepository.__init__(self)
        atexit.register(self._clear)

    def _clear(self):
        for session in self.values():
            try:
                session.close()
            except Exception as _:
                pass
