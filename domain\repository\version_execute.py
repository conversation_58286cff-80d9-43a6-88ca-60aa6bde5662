from infrastructure.Singleton import Singleton
from infrastructure.db.elastic_search.elastic_search import Document
from elasticsearch import Elasticsearch
from datetime import datetime
from domain.model.dto.test_record import VersionExecuteInfo
from infrastructure.utils.json2 import marshal
@Singleton
class EsVersionExecute(Document):

    def __init__(self):
        Document.__init__(self, 'version_execute', 'version_execute')

    def save_version_execute_task(self, document: VersionExecuteInfo):
        return self.index(id=datetime.now().isoformat(), body=eval(marshal(document)))

    def get_execute_info(self,task_id):
        result = self.query_by_filter_with_sort({"task_id":task_id},"create_time")
        return result[1]

if __name__ == "__main__":
    from infrastructure.utils.Env import Env
    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)
    # body = VersionExecuteInfo(env_id= "1014",task_id="2025-04-10T17-07-21",version="NR-V4.20.20.20MainR106_2504031435",test_result="fail")
    task = EsVersionExecute()
    # task.save_version_execute_task(body)
    result = task.query_by_filter_with_sort({"task_id":"2025-04-10T17-07-21"},"create_time")
    print(result)
    # result = task.query_by_filter_without_time({"state": ""})
    # print(result)