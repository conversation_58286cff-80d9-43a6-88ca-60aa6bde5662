# -*- coding:utf-8 -*
import telnetlib
import time
import re
import struct

Fmtdict = {'padbyte': 'x',  # pad byte no value
           'CHAR': 'c',  # char bytes of length 1 1
           'UCHAR': 'b',  # signed char integer 1 (1),(3)
           'BYTE': 'B',  # unsigned char integer 1 (3)
           'SWORD': 'h',  # short integer 2 (3)
           'WORD': 'H',  # unsigned short integer 2 (3)
           'WORD16': 'H',  # unsigned short integer 2 (3)
           'SDWORD': 'i',  # int integer 4 (3)
           'DWORD': 'I',  # unsigned int integer 4 (3)
           'LONG': 'l',  # long integer 4 (3)
           'ULONG': 'L',  # unsigned long integer 4 (3)
           'LONGLONG': 'q',  # long long integer 8 (2), (3)
           'ULONGLONG': 'Q',  # unsigned long long integer 8 (2), (3)
           'FLOAT': 'f',  # float float 4 (4)
           'DOUBLE': 'd',  # double float 8 (4)
           'STR': 's',  # char[] bytes
           'BYTES': 'p',  # char[] bytes
           'LITTLE': '<',
           'BIG': '>',
           1: 'B',
           2: 'H',
           4: 'I',
           8: 'Q',          
           }

class Data2Bin:
    def __init__(self, datatxt, **dictParams):
        self.Data = datatxt
        self.StartAddr = dictParams["StartAddr"] if "StartAddr" in list(dictParams.keys()) else  0
        # self.Size =  dictParams["Size"]   if "Size"  in dictParams.keys() else 0
        self.endian = dictParams["endian"] if "endian" in list(dictParams.keys()) else  "BIG"
        self.ChildDataType = Fmtdict["DWORD"]
        self.Buf = b""
        self.fmt8 = "%s%s" % (Fmtdict[self.endian], Fmtdict[1])
        self.fmt16 = "%s%s" % (Fmtdict[self.endian], Fmtdict[2])
        self.fmt32 = "%s%s" % (Fmtdict[self.endian], Fmtdict[4])
        self.fmt64 = "%s%s" % (Fmtdict[self.endian], Fmtdict[8])
        self.bitField = lambda value, begin, end: ((value & ((1 << (end + 1)) - (1 << begin))) >> begin)
        self.lineFmt = r"^(?P<Addr>\w{8,16}):\s+(?P<Data>(\w{1,8}\s+)+).*?$"
        self.dwordfmt = r"\w{2,8}"

        self.dwordp = re.compile(self.dwordfmt)
        self.linep = re.compile(self.lineFmt)
        self.__Convert2Bin()

    def GetBufLen(self):
        return len(self.Buf)

    def __isValidAddr(self, Addr, readlen=0):
        return True if (Addr + readlen - self.StartAddr) <= len(self.Buf) else False

    def __bitFieldEx(self, value, begin, end):
        mask = (1 << (end + 1)) - (1 << begin)
        return (value & mask) >> begin

    def __readvalue(self, Address, bytenum, fmt, **dictparams):
        if not self.__isValidAddr(Address, bytenum):
            return
        begin = dictparams["Begin"] if "Begin" in list(dictparams.keys()) else 0
        end = dictparams["End"] if "End" in list(dictparams.keys()) else  bytenum * 8 - 1
        value = struct.unpack(fmt, self.Buf[(Address - self.StartAddr): (Address - self.StartAddr) + bytenum])[0]
        return self.bitField(value, begin, end)

    def __Convert2Bin(self):
        self.__AddMemBlock(self.Data)

    def __GetDataList(self, DataContent):
        #print DataContent
        lines = DataContent.split("\n")
        vars = []
        for line in lines:
            line =  line.lstrip("\r\n ")
            line = line.rstrip("\r\n ")
            line = line + " "
            m = self.linep.search(line)
            if m:
                addr = m.groupdict()["Addr"]
                data = m.groupdict()["Data"]
                dws = [int(item, 16) for item in self.dwordp.findall(data)]
                vars += dws
        return vars

    def __AddMemBlock(self, DataContent):
        Varlist = self.__GetDataList(DataContent)
        if not Varlist:
            return
        DataNum = len(Varlist)
        Fmt = "%s%d%s" % (Fmtdict[self.endian], DataNum, self.ChildDataType)
        self.Buf += struct.pack(Fmt, *Varlist)

    def read1b(self, Address, **dictparams):
        return self.__readvalue(Address, 1, self.fmt8, **dictparams)

    def read2b(self, Address, **dictparams):
        return self.__readvalue(Address, 2, self.fmt16, **dictparams)

    def read4b(self, Address, **dictparams):
        return self.__readvalue(Address, 4, self.fmt32, **dictparams)

    def read8b(self, Address, **dictparams):
        return self.__readvalue(Address, 8, self.fmt64, **dictparams)

    def getbuf(self, addr, len):
        offset = (addr - self.StartAddr)
        if (offset > self.GetBufLen()) or (offset + len > self.GetBufLen()):
            return None
        return self.Buf[offset:offset + len]
