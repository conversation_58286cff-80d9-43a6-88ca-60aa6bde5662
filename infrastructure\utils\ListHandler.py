import re


def get_list_subtraction(list1, list2):
    if set(list2).issubset(set(list1)):
        return list(set(list1) - set(list2))
    else:
        raise Exception("please check two lists. List1 must contains all elements in list2.")


def is_exist_in_list(pattern, aimList):
    for e in aimList:
        if re.search(pattern, e):
            return True
    return False


class ListHandler(object):

    @staticmethod
    def get_list_subtraction(list1, list2):
        if set(list2).issubset(set(list1)):
            return list(set(list1) - set(list2))
        else:
            raise Exception("please check two lists. List1 must contains all elements in list2.")

    @staticmethod
    def get_matched_str(regex, strList, ignoreCase=False):
        for testStr in strList:
            matchedUri = re.findall(regex, testStr, re.I) if ignoreCase else re.findall(regex, testStr)
            if matchedUri:
                return matchedUri[0]
        raise Exception("{0} not contains {1}".format(str(strList), regex))
