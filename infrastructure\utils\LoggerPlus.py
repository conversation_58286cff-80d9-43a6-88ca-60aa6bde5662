# -*- coding: utf-8 -*-
"""
Created on 2023-07-04 09:50

@author: 10333573
"""
import datetime

from GlobalVars import loggerId, logDataP, startTime
from infrastructure.logger.logger import logger


def _get_logger_id():
    return loggerId.get()


def post_logger_info_to_logger_server(msg):
    print(f"{logger.debug(f'post  {msg}')}=====12334")
    pass


def log_and_send_message(level, msg):
    logId = loggerId.get()
    nowTime = datetime.datetime.now()
    if (nowTime - startTime.get()).total_seconds() >= 1.0:
        try:
            send_and_clear_cur_log_data(logId, level)
        except Exception as e:
            logger.error(f"send or clear log error: {e}, log {level}: {msg}")
        startTime.set(nowTime)
    return getattr(logger, level)(f"log-id({logId}) - {msg}")


def send_and_clear_cur_log_data(logId, level="info", isLast=False):
    logs = logDataP.pop(logId) if isLast else logDataP.get(logId)
    if logs:
        body = {logId: logs}
        try:
            post_logger_info_to_logger_server(body)
        except Exception as e:
            logger.error(f"Post log error: {e}, log {level}: {body}")
    if not isLast:
        logDataP.get(logId).clear()


def info(msg):
    # lock.acquire()
    try:
        logDataP.get(loggerId.get()).append(msg)
        print(f"wudi:{logDataP}")
    finally:
        pass
        # lock.release()
    return log_and_send_message("info", msg)


def warn(msg):
    return log_and_send_message("warn", msg)


def debug(msg):
    return log_and_send_message("debug", msg)


def error(msg):
    return log_and_send_message("error", msg)
