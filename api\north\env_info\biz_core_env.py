from fastapi import APIRouter, Body, Query, Path, Header
from typing import Dict

from domain.model.dto.env_info import BizCoreEnv
from service.env_info_service import BizCoreEnvInfoService

router = APIRouter(prefix="/env_info/biz_core_env")
service = BizCoreEnvInfoService()

@router.get("/")
async def get_all_biz_core_envs(
    from_id: int = Query(0, description="Starting index for pagination"),
    size: int = Query(50, description="Number of items to return")
):
    """Get all BizCore environments"""
    return service.get_all(from_id=from_id, size=size)

@router.get("/{env_id}")
async def get_biz_core_env_by_id(
    env_id: str = Path(..., description="Environment ID")
):
    """Get BizCore environment by ID"""
    return service.get_by_id(env_id)

@router.post("/")
async def create_biz_core_env(
    env_info: BizCoreEnv = Body(...),
    x_operator: str = Header(..., description="User performing the operation")
):
    """Create a new BizCore environment"""
    return service.create(env_info.model_dump(), x_operator)

@router.put("/{env_id}/{doc_id}")
async def update_biz_core_env(
    env_id: str = Path(..., description="Environment ID"),
    doc_id: str = Path(..., description="Document ID"),
    env_info: Dict = Body(...),
    x_operator: str = Header(..., description="User performing the operation")
):
    """Update a BizCore environment"""
    return service.update(env_id, doc_id, env_info, x_operator)

@router.delete("/{env_id}/{doc_id}")
async def delete_biz_core_env(
    env_id: str = Path(..., description="Environment ID"),
    doc_id: str = Path(..., description="Document ID"),
    x_operator: str = Header(..., description="User performing the operation")
):
    """Delete a BizCore environment"""
    return service.delete(env_id, doc_id, x_operator)
