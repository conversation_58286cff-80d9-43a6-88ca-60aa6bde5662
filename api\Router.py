from api.north.jenkins_job import router as jenkins_job
from api.north.env_check.Router import router as env_check
from api.routers.loganalysishandler import log_analysis_router
from api.north.tools.Router import router as tools
from api.north.agents_log import router as agents_log
from api.north.business_process_stats import router as business_process_stats

ROUTERS = [
    jenkins_job,
    log_analysis_router,
    env_check,
    tools,
    agents_log,
    business_process_stats
]


def init_router(app):
    for router in ROUTERS:
        app.include_router(router)