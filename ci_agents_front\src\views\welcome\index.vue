<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import jobApi from "@/api/jobApi";
import type { JenkinsJobWithStatus } from "@/api/jobApi";
import { message } from "@/utils/message";

defineOptions({
  name: "Welcome"
});



// 添加日志分析相关的变量和方法
const logAnalysisData = ref({});
const logAnalysisLoading = ref({});
const logAnalysisError = ref({});
const showFullContentDialog = ref(false);
const currentFullContentItem = ref(null);

// 获取日志智能分析数据
const fetchLogAnalysisForTask = async (taskId) => {
  // console.log("获取日志分析数据")
  if (!logAnalysisData.value[taskId]) {
    logAnalysisLoading.value[taskId] = true;
    logAnalysisError.value[taskId] = null;
    
    try {
      const result = await jobApi.queryTestcaseLogAnalysis(taskId);
      if (result.success) {
        logAnalysisData.value[taskId] = result.data;
      } else {
        logAnalysisError.value[taskId] = result.message || '获取日志分析失败';
      }
    } catch (error) {
      logAnalysisError.value[taskId] = '获取日志分析数据失败，请稍后重试';
    } finally {
      logAnalysisLoading.value[taskId] = false;
    }
  }
};

// 显示日志分析原始数据弹窗
const showFullContent = (item) => {
  // console.log("显示日志分析原始数据")
  currentFullContentItem.value = item;
  showFullContentDialog.value = true;
};

// 关闭日志分析原始数据弹窗
const closeFullContentDialog = () => {
  // console.log("关闭日志分析原始数据")
  showFullContentDialog.value = false;
  currentFullContentItem.value = null;
};


// 添加人工分析页面相关的状态
const showEditDialog = ref(false);
const editForm = ref({
  testcase_name: '',
  manualClassification: '',
  manualAnalysisResult: ''
});
const currentEditItem = ref(null);

// 打开人工分析页面
const openEditDialog = (item) => {
  console.log("打开人工分析页面")
  showEditDialog.value = true;
  currentEditItem.value = item;
  editForm.value = {
    testcase_name: item.testcase_name,
    manualClassification: item.manualClassification || '',
    manualAnalysisResult: item.manualAnalysisResult || ''
  };
};

// 关闭人工分析页面
const closeEditDialog = () => {
  console.log("关闭人工分析页面")
  showEditDialog.value = false;
  currentEditItem.value = null;
  editForm.value = {
    testcase_name: '',
    manualClassification: '',
    manualAnalysisResult: ''
  };
};

// 添加人工分析结果和手动分类的变量
const editedAnalysisResults = ref({});
const editedClassifications = ref({});

// 提交人工分析结果和手动分类数据
const saveEdit = async () => {
  if (!currentEditItem.value) return;
  
  try {
    console.log("提交人工分析结果和手动分类数据")
    const result = await jobApi.updateTestcaseLogAnalysis({
      taskId: currentEditItem.value.task_id,
      testcaseName: currentEditItem.value.testcase_name,
      classification: editForm.value.manualClassification,
      analysisResult: editForm.value.manualAnalysisResult
    });
    
    if (result.success) {
      // 保存成功后，更新本地缓存的编辑结果
      if (!editedAnalysisResults.value[currentEditItem.value.task_id]) {
        editedAnalysisResults.value[currentEditItem.value.task_id] = {};
        editedClassifications.value[currentEditItem.value.task_id] = {};
      }
      
      // 更新分析结果和分类
      editedAnalysisResults.value[currentEditItem.value.task_id][currentEditItem.value.testcase_name] = editForm.value.manualAnalysisResult;
      editedClassifications.value[currentEditItem.value.task_id][currentEditItem.value.testcase_name] = editForm.value.manualClassification;
      
      // 更新当前项的显示值
      currentEditItem.value.editedAnalysisResult = editForm.value.manualAnalysisResult;
      currentEditItem.value.editedClassification = editForm.value.manualClassification;
      
      // 重新获取日志分析数据
      await fetchLogAnalysisForTask(currentEditItem.value.task_id);
      closeEditDialog();
    } else {
      console.error('更新失败:', result.message);
    }
  } catch (error) {
    console.error('更新日志分析出错:', error);
  }
};

const jobs = ref<JenkinsJobWithStatus[]>([]);
const fulljobs = ref<JenkinsJobWithStatus[]>([]);
const loading = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const expandedRowActiveTab = ref({});  // 存储每行展开后的活动标签

// 添加筛选相关的变量
const filterEnvId = ref('');
const filterBranch = ref('');
const filterPrincipal = ref(''); 
const dateFilter = ref(null);
// 新增筛选变量
const filterAction = ref([]);
const filterStatus = ref('');

// 页面初始化或刷新时，发送请求查询jenkinsjob表中的数据
const getJobs = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true;
  try {
    // const response = await jobApi.getAllJobs();
    const jobsinfo = await jobApi.getAllJobs(true);
    jobs.value = jobsinfo.data.map(job => ({
      ...job,
      loadingAction: false,
      latestAction: null,
      loadingStatus: false,
      latestStatus: null,
      loadingPrincipal: false,
      principalName: null,
      statusRadio: job.current_state || ""
    }));
    const fulljobsinfo = await jobApi.getAllJobs();
    fulljobs.value = fulljobsinfo.data.map(job => ({
      ...job,
      loadingAction: false,
      latestAction: null,
      loadingStatus: false,
      latestStatus: null,
      loadingPrincipal: false,
      principalName: null,
      statusRadio: job.current_state || ""
    }));
    
    // 如果 job.fail_reason 的值不为空或不为空字符串，则把对应的 job 加入到 submittedTasks 集合中，
    // submittedTasks 集合代表已提交过失败原因分析的 job 集合。
    jobsinfo.data.forEach(job => {
      if (job.fail_reason && job.fail_reason.trim() !== '') {
        submittedTasks.value.add(job.task_id);
        // 将失败原因内容保存到 failureAnalysisContent 中，以便显示和编辑
        failureAnalysisContent.value[job.task_id] = job.fail_reason;
        // 初始化人工处理时长
        manualProcessingHours.value[job.task_id] = job.manual_processing_hours || 0;
        // 确保初始状态下不在编辑状态
        editingTasks.value.delete(job.task_id);
      } else {
        // 为没有失败原因的任务初始化默认值
        manualProcessingHours.value[job.task_id] = 0;
      }
    });

    // 获取job总数，total显示在分页组件中
    total.value = jobsinfo.data.length;
    loading.value = false;
  } catch (error) {
    console.error("Failed to fetch Jenkins jobs:", error);
    loading.value = false;
  }
};

// 根据筛选项和分页设置，从jobs中筛选出当前页需要展示的数据
// computed 代表 currentTableData 是一个计算属性，当 currentTableData 依赖的数据发生变化时，会自动重新计算。
const currentTableData = computed(() => {
  // 先筛选
  const filteredData = jobs.value.filter(job => {
    const matchEnvId = !filterEnvId.value || job.env_id.includes(filterEnvId.value);
    const matchBranch = !filterBranch.value || job.version_branch.includes(filterBranch.value);
    const matchPrincipal = !filterPrincipal.value || 
                          (job.principalName && job.principalName.includes(filterPrincipal.value));
    // 修改动作筛选为多选逻辑
    const matchAction = filterAction.value.length === 0 || 
                        (job.latestAction && filterAction.value.includes(job.latestAction));
    const matchStatus = !filterStatus.value || 
                        (job.latestStatus && job.latestStatus.includes(filterStatus.value));
    
    // 添加日期筛选
    let matchDate = true;
    if (dateFilter.value) {
      const jobDate = new Date(job.timestamp);
      const selectedDate = new Date(dateFilter.value);
      matchDate = jobDate.getFullYear() === selectedDate.getFullYear() &&
                 jobDate.getMonth() === selectedDate.getMonth() &&
                 jobDate.getDate() === selectedDate.getDate();
    }
    
    return matchEnvId && matchBranch && matchPrincipal && matchAction && matchStatus && matchDate;
  });
  
  // 再分页
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return filteredData.slice(startIndex, endIndex);
});

// 处理页码改变
const handleCurrentChange = (val: number) => {
  // console.log("页码改变")
  currentPage.value = val;
  fetchCurrentPageData();
};

// 处理每页条数改变
const handleSizeChange = (val: number) => {
  // console.log("每页显示数据条数改变")
  pageSize.value = val;
  currentPage.value = 1;
  fetchCurrentPageData();
};

// 获取当前页数据的负责人、最新执行动作和执行状态的详细信息
const fetchCurrentPageData = async () => {
  // 设置所有任务的加载状态
  currentTableData.value.forEach(job => {
    job.loadingAction = true;
    job.loadingStatus = true;
    job.loadingPrincipal = true;
  });
  
  try {
    // 1. 批量获取环境负责人信息
    const envIds = [
      "RAN3-上海高频CI团队-VAT1001",
      "RAN3-上海高频CI团队-VAT1002",
      "RAN3-上海高频CI团队-VAT1003",
      "RAN3-上海高频CI团队-VAT1004",
      "RAN3-上海高频CI团队-VAT1005",
      "RAN3-上海高频CI团队-VAT1006",
      "RAN3-上海高频CI团队-VAT1007",
      "RAN3-上海高频CI团队-VAT1008",
      "RAN3-上海高频CI团队-VAT1009",
      "RAN3-上海高频CI团队-VAT1010",
      "RAN3-上海高频CI团队-VAT1011",
      "RAN3-上海高频CI团队-VAT1012",
      "RAN3-上海高频CI团队-VAT1013",
      "RAN3-上海高频CI团队-VAT1014",
      "RAN3-上海高频CI团队-VAT1015",
      "RAN3-上海高频CI团队-VAT1016",
      "RAN3-上海高频CI团队-VAT1017",
      "RAN3-上海高频CI团队-VAT1018",
      "RAN3-上海高频CI团队-VAT1021"
    ];
    
    // 先检查缓存中是否已有环境负责人数据且未过期
    const envPrincipalCacheKey = 'envPrincipalData';
    const cachedPrincipalData = localStorage.getItem(envPrincipalCacheKey);
    let principalResponse;

    if (cachedPrincipalData && !isCacheExpired(envPrincipalCacheKey)) {
      // 如果缓存中有数据且未过期，直接使用缓存数据
      principalResponse = {
        success: true,
        data: JSON.parse(cachedPrincipalData)
      };
    } else {
      // 如果缓存中没有数据或已过期，则发送请求获取
      principalResponse = await jobApi.getBatchEnvPrincipal(envIds);
      
      // 将环境负责人信息存储到localStorage中，避免切换页面时重复请求
      if (principalResponse.success && principalResponse.data) {
        localStorage.setItem(envPrincipalCacheKey, JSON.stringify(principalResponse.data));
        // 设置缓存时间戳
        setCacheTimestamp(envPrincipalCacheKey);
      }
    }
    
    // 如果成功获取负责人信息，更新任务的负责人字段
    if (principalResponse.success && principalResponse.data) {
      // 遍历当前页的任务，设置负责人信息
      currentTableData.value.forEach(job => {
        if (job.env_id && principalResponse.data[job.env_id]) {
          job.principalName = principalResponse.data[job.env_id];
          job.loadingPrincipal = false;
        } else {
          job.principalName = '-';
          job.loadingPrincipal = false;
        }
      });
    } else {
      // 如果获取失败，设置默认值
      currentTableData.value.forEach(job => {
        job.principalName = '-';
        job.loadingPrincipal = false;
      });
    }
    
    // 2. 获取每个任务的日志信息（执行动作和状态）
    // 使用 Promise.all 并行获取所有任务的日志
    try {
      const cacheKey = `AllTaskLogs`;
      if (localStorage.getItem(cacheKey) && !isCacheExpired(cacheKey)) {
        // console.log("有缓存")
        const cachedData = JSON.parse(localStorage.getItem(cacheKey));
        
        for (const job of currentTableData.value) {
          const logData = cachedData.find(log => Object.keys(log)[0] === job.task_id)
          if(logData === undefined){
            console.log("日志信息缺失，重新查询")
            const logsResponse = await jobApi.getAllTaskLogs();
            const logsData = logsResponse.data;
            if (logsResponse.code.code === "0000") {
              // 将日志数据存储到localStorage中
              localStorage.setItem(`AllTaskLogs`, JSON.stringify(logsData));
              // 设置缓存时间戳
              setCacheTimestamp(`AllTaskLogs`);
            }
            const cachedData = JSON.parse(localStorage.getItem(cacheKey));
            const logData = cachedData.find(log => Object.keys(log)[0] === job.task_id)
          }
          
          if(logData && logData[job.task_id]){
            processServicePriority(logData[job.task_id], job);
          }
          job.loadingAction = false;
          job.loadingStatus = false;
        }
      } else {
        console.log("没有缓存")
        // 如果没有缓存或缓存已过期，重新获取数据
        const logsResponse = await jobApi.getAllTaskLogs();
        const logsData = logsResponse.data;
        
        if (logsResponse.code.code === "0000") {
          // 将日志数据存储到localStorage中
          localStorage.setItem(`AllTaskLogs`, JSON.stringify(logsData));
          // 设置缓存时间戳
          setCacheTimestamp(`AllTaskLogs`);

          // 获取所有任务的ID
          const allTaskIds = jobs.value.map(job => job.task_id);
          const cachedData = JSON.parse(localStorage.getItem(cacheKey));

          for (const job of currentTableData.value) {
            const logData = cachedData.find(log => Object.keys(log)[0] === job.task_id)
            // console.log("logData:",logData)
            if(logData){
              processServicePriority(logData[job.task_id], job);
            }
            job.loadingAction = false;
            job.loadingStatus = false;
          }
        }
      }
    } catch (error) {
      console.error("获取或处理任务日志数据时出错:", error);
      // 出错时重置所有加载状态
      currentTableData.value.forEach(job => {
        job.loadingAction = false;
        job.loadingStatus = false;
      });
    }
    // console.log("currentTableData.value:",currentTableData.value)


    // const logPromises = currentTableData.value.map(async (job) => {
    //   try {
    //     const cacheKey = `taskLogData_${job.task_id}`;
        
    //     // 检查是否已经缓存且缓存未过期
    //     if (localStorage.getItem(cacheKey) && !isCacheExpired(cacheKey)) {
    //       // 如果缓存中有数据且未过期，直接使用缓存数据
    //       const cachedData = JSON.parse(localStorage.getItem(cacheKey));
    //       if (cachedData) {
    //         processServicePriority(cachedData, job);
    //       }
    //     } else {
    //       // 如果缓存中没有数据或已过期，则发送请求获取
    //       const logsResponse = await jobApi.getTaskLogs(job.task_id);
          
    //       if (logsResponse.code.code === "0000") {
    //         // 将日志数据存储到localStorage中
    //         localStorage.setItem(cacheKey, JSON.stringify(logsResponse.data));
    //         // 设置缓存时间戳
    //         setCacheTimestamp(cacheKey);
            
    //         processServicePriority(logsResponse.data, job);
    //       }
    //     }
    //   } catch (error) {
    //     console.error(`Failed to fetch logs for task ${job.task_id}:`, error);
    //   } finally {
    //     job.loadingAction = false;
    //     job.loadingStatus = false;
    //   }
    // });
    
    // // 等待所有日志请求完成
    // await Promise.all(logPromises);
    
  } catch (error) {
    console.error("Failed to fetch data:", error);
    // 出错时重置所有加载状态
    currentTableData.value.forEach(job => {
      job.loadingAction = false;
      job.loadingStatus = false;
      job.loadingPrincipal = false;
    });
  }
};

// 根据给定的服务优先级，提取出最新执行动作和该动作的最新状态
const processServicePriority = (data, job) => {
  // 定义服务类型优先级
  const servicePriority = [
    'jenkins_job_save',
    'log_analysis',
    'env_check',
    'online_test',
    'env_backtrack'
  ];
  
  // 检查哪些服务类型存在于数据中
  const availableServices = Object.keys(data).filter(service => 
    servicePriority.includes(service) && 
    Array.isArray(data[service]) && 
    data[service].length > 0
  );
  
  if (availableServices.length > 0) {
    // 按优先级排序并获取优先级最高的服务
    const highestPriorityService = availableServices.sort((a, b) => 
      servicePriority.indexOf(b) - servicePriority.indexOf(a)
    )[0];
    
    // 设置最新动作
    job.latestAction = highestPriorityService;
    
    // 获取该动作的最新状态
    const latestRecord = data[highestPriorityService].sort((a, b) => 
      new Date(b.时间 || b.timestamp).getTime() - new Date(a.时间 || a.timestamp).getTime()
    )[0];
    
    // 设置最新状态
    job.latestStatus = latestRecord.状态 || latestRecord.status;

    // console.log("Latest Action:", job.latestAction, "Latest Status:", job.latestStatus);
    
    return true; // 返回处理成功标志
  }
  
  return false; // 返回处理失败标志
};

// 添加缓存过期时间常量（30分钟）
const CACHE_EXPIRY_TIME = 30 * 60 * 1000; // 30分钟，单位毫秒

// 检查缓存是否过期
const isCacheExpired = (cacheKey) => {
  const cacheTimestampKey = `${cacheKey}_timestamp`;
  const timestamp = localStorage.getItem(cacheTimestampKey);
  
  if (!timestamp) return true;
  
  const cacheTime = parseInt(timestamp);
  const currentTime = new Date().getTime();
  
  return (currentTime - cacheTime) > CACHE_EXPIRY_TIME;
};

// 给缓存数据设置缓存时间戳
const setCacheTimestamp = (cacheKey) => {
  const cacheTimestampKey = `${cacheKey}_timestamp`;
  localStorage.setItem(cacheTimestampKey, new Date().getTime().toString());
};

// 在组件挂载时预加载所有任务的日志数据
const preloadInitialLogsData = async () => {
  try {
    // 获取所有任务的ID
    const allTaskIds = jobs.value.map(job => job.task_id);
    // console.log(allTaskIds)
    const cacheKey = `AllTaskLogs`;
    // 检查是否已经缓存且缓存未过期
    if (localStorage.getItem(cacheKey) && !isCacheExpired(cacheKey)) {
      // 如果已经有缓存且未过期，从缓存中获取数据并更新任务信息
      const cachedData = JSON.parse(localStorage.getItem(cacheKey));
      // console.log("cachedData:",cachedData)
      allTaskIds.forEach(taskId => {
        const job = jobs.value.find(j => j.task_id === taskId);
        const logData = cachedData.find(log => Object.keys(log)[0] === taskId)
        if (job && logData) {
          processServicePriority(logData, job);
        }
      });
    } else {
      // 如果没有缓存或缓存已过期，重新获取数据
      const logsResponse = await jobApi.getAllTaskLogs();
      const logsData = logsResponse.data;
      // console.log('logsData:', logsData); 
      if (logsResponse.code.code === "0000") {
        // 将日志数据存储到localStorage中
        localStorage.setItem(`AllTaskLogs`, JSON.stringify(logsData));
        // 设置缓存时间戳
        setCacheTimestamp(`AllTaskLogs`);

        const logPromises = allTaskIds.map(async (taskId) => {
          const job = jobs.value.find(j => j.task_id === taskId);
          const logData = logsData.find(log => Object.keys(log)[0] === taskId)
          if (job && logData) {
            processServicePriority(logData, job);
          }
        });

        // 等待所有预加载请求完成
        await Promise.all(logPromises);
      }
    }
  } catch (error) {
    console.error("Failed to preload initial logs data:", error);
  }
};

// 添加定时清理缓存的功能
const setupCacheCleanupInterval = () => {
  // 每30分钟检查一次缓存
  const interval = setInterval(() => {
    console.log('检查缓存状态（每30分钟检查一次）');
    
    // 获取所有localStorage的键
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      
      // 只处理任务日志数据和环境负责人数据
      if (key.startsWith('taskLogData_') || key === 'envPrincipalData') {
        if (isCacheExpired(key)) {
          console.log(`清除缓存 ${key}, removing...`);
          localStorage.removeItem(key);
          localStorage.removeItem(`${key}_timestamp`);
        }
      }
    }
  }, CACHE_EXPIRY_TIME / 2); // 每15分钟检查一次
  
  // 在组件卸载时清除定时器
  onBeforeUnmount(() => {
    clearInterval(interval);
  });
};

// 在组件挂载时启动缓存清理定时器
onMounted(() => {
  getJobs().then(() => {
    // 先预加载前30条任务的日志数据
    preloadInitialLogsData().then(() => {
      // 然后获取当前页数据的详细信息
      fetchCurrentPageData();
    });
  });
  startAutoRefresh(); // 启动定时刷新
  setupCacheCleanupInterval(); // 启动缓存清理定时器
});

// 更新人工确认测试结果
const updateStatus = async (taskId: string, status: string) => {
  try {
    const result = await jobApi.updateTaskState(taskId, status);
    if (result.success) {
      // 找到对应的任务并更新其状态
      const job = jobs.value.find(j => j.task_id === taskId);
      if (job) {
        job.current_state = status;
        job.statusRadio = status;
      }
    } else {
      console.error('状态更新失败:', result.message);
    }
  } catch (error) {
    console.error('更新状态时发生错误:', error);
  }
};

// 添加筛选方法
const handleFilter = async () => {
  // 如果有负责人筛选条件，确保先加载负责人信息
  if (filterPrincipal.value && jobs.value.some(job => !job.principalName)) {
    await loadPrincipalInfo();
  }
  
  // 只在必要时加载日志数据，并限制并发请求数量
  if ((filterAction.value.length > 0 || filterStatus.value) && 
      jobs.value.some(job => !job.latestAction || !job.latestStatus)) {
    
    // 获取缓存的日志数据
    const allTaskLogs = localStorage.getItem(`AllTaskLogs`);
    if (allTaskLogs) {
      try {
        const parseData = JSON.parse(allTaskLogs);
        
        // 只处理当前页面可能显示的任务，而不是所有任务
        const jobsToProcess = jobs.value
          .filter(job => !job.latestAction || !job.latestStatus)
          .slice(0, 100); // 限制处理数量
          
        // 批量处理而不是使用Promise.all
        for (const job of jobsToProcess) {
          const taskLog = parseData.find(log => Object.keys(log)[0] === job.task_id);
          if (taskLog) {
            processServicePriority(taskLog[job.task_id], job);
          }
        }
      } catch (error) {
        console.error("Failed to parse cached log data:", error);
      }
    }
  }
  
  // 直接应用筛选，不等待所有数据加载完成
  applyFilters();
};

// 将筛选逻辑抽取为单独函数
const applyFilters = () => {
  // 计算符合筛选项的数据总数
  total.value = jobs.value.filter(job => {
    const matchEnvId = !filterEnvId.value || job.env_id.includes(filterEnvId.value);
    const matchBranch = !filterBranch.value || job.version_branch.includes(filterBranch.value);
    const matchPrincipal = !filterPrincipal.value || 
                          (job.principalName && job.principalName.includes(filterPrincipal.value));
    
    const matchAction = filterAction.value.length === 0 || 
                        (job.latestAction && filterAction.value.includes(job.latestAction));
    const matchStatus = !filterStatus.value || 
                        (job.latestStatus && job.latestStatus.includes(filterStatus.value));
    
    let matchDate = true;
    if (dateFilter.value) {
      const jobDate = new Date(job.timestamp);
      const selectedDate = new Date(dateFilter.value);
      matchDate = jobDate.getFullYear() === selectedDate.getFullYear() &&
                 jobDate.getMonth() === selectedDate.getMonth() &&
                 jobDate.getDate() === selectedDate.getDate();
    }
    
    return matchEnvId && matchBranch && matchPrincipal && matchDate && matchAction && matchStatus;
  }).length;
  
  if (Math.ceil(total.value / pageSize.value) < currentPage.value) {
    currentPage.value = 1;
  }
  
  fetchCurrentPageData();
};

// 重置筛选
const resetFilter = () => {
  filterEnvId.value = '';
  filterBranch.value = '';
  filterPrincipal.value = '';
  dateFilter.value = null;
  filterAction.value = []; // 修改为空数组
  filterStatus.value = '';
  handleFilter();
};

// 切换标签页方法
const switchExpandedTab = (taskId, tabName) => {
  if (!expandedRowActiveTab.value[taskId]) {
    expandedRowActiveTab.value[taskId] = 'pipeline';
  }
  expandedRowActiveTab.value[taskId] = tabName;
  
  // 如果切换到日志分析标签，加载日志分析数据 
  if (tabName === 'logAnalysis') {
    fetchLogAnalysisForTask(taskId);
  }
};

// 获取当前行的活动标签
const getActiveTab = (taskId) => {
  return expandedRowActiveTab.value[taskId] || 'pipeline';
};

// 添加行展开事件处理
const handleExpandChange = (row, expanded) => {
  if (expanded) {
    // 记录展开的行
    expandedRows.value.add(row.task_id);
    
    currentTaskId.value = row.task_id;
    // 从localStorage获取任务数据
    // const taskLogData = localStorage.getItem(`taskLogData_${row.task_id}`);
    const allTaskLogsData = JSON.parse(localStorage.getItem(`AllTaskLogs`));
    const taskLogData = allTaskLogsData.find(log => Object.keys(log)[0] === row.task_id)[row.task_id];
    if (taskLogData) {
      // console.log("taskLogData:",taskLogData)
      // 将对象格式转换为数组格式
      if (taskLogData && typeof taskLogData === 'object' && !Array.isArray(taskLogData)) {
        // 如果是对象格式，将每个服务类型的数组合并成一个大数组
        const dataArray = [];
        Object.keys(taskLogData).forEach(serviceKey => {
          if (Array.isArray(taskLogData[serviceKey])) {
            // 为每个项添加service_key属性
            const itemsWithServiceKey = taskLogData[serviceKey].map(item => ({
              ...item,
              service_key: serviceKey
            }));
            dataArray.push(...itemsWithServiceKey);
          }
        });
        taskData.value[row.task_id] = dataArray;
      } else if (Array.isArray(taskLogData)) {
        // 如果已经是数组格式，直接使用
        taskData.value[row.task_id] = taskLogData;
      } else {
        // 如果既不是对象也不是数组，初始化为空数组
        taskData.value[row.task_id] = [];
      }
      
      // 设置jobState
      jobState.value[row.task_id] = row.current_state || "";
      // 初始化流水线步骤
      initializePipelineSteps(row.task_id);
    }
  } else {
    // 移除已折叠的行
    expandedRows.value.delete(row.task_id);
  }
};

// 添加流水线所需的状态变量
const taskData = ref({});
const jobState = ref({});
const pipelineSteps = ref({});
const showStatusHistoryDialog = ref(false);
const currentServiceKey = ref('');
const statusHistory = ref([]);
const currentTaskId = ref(null); // 添加当前任务ID变量
const expandedRows = ref(new Set()); // 添加一个变量来跟踪当前展开的行

// 计算属性：按时间排序的状态历史
const sortedStatusHistory = computed(() => {
  return [...statusHistory.value].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
});

// 获取步骤状态类
const getStepClassSortByTimeStamp = (serviceKey, taskId) => {
  // 检查taskData中是否存在该taskId的数据
  if (!taskData.value[taskId] || !Array.isArray(taskData.value[taskId])) {
    return ''; // 如果数据不存在或不是数组，返回默认样式
  }

  // 获取该serviceKey对应的所有记录并按时间戳排序
  const records = taskData.value[taskId]
    .filter(item => item.service_key === serviceKey)
    .sort((a, b) => {
      // 使用"时间"字段而不是timestamp
      const dateA = new Date(b.时间 || b.timestamp).getTime();
      const dateB = new Date(a.时间 || a.timestamp).getTime();
      return dateA - dateB;
    });


  // 获取最新的状态（如果有记录的话）
  // 使用"状态"字段而不是status
  const latestStatus = records.length > 0 ? (records[0].状态 || records[0].status) : '';

  // 根据最新状态返回对应的样式类
  switch (latestStatus) {
    case 'success':
      return 'success';
    case 'in_progress':
      return 'warning';
    case 'failure':
      return 'error';
    default:
      return ''; // 默认无样式
  }
};

// 获取线上复测结果
const getOnlineTestStatus = (taskId) => {
  // 找出所有匹配taskId的job记录
  const matchingJobs = fulljobs.value.filter(fulljob => fulljob.task_id === taskId);
  // console.log(matchingJobs)
  // console.log("--------------------------")

  // 如果没有匹配的记录，返回空字符串
  if (!matchingJobs.length) return '';
  
  // 如果只有一条记录，直接返回其version_test_result的值
  if (matchingJobs.length === 1) {
    return matchingJobs[0].version_test_result === 'True' ? 'success' : 'error';
  }
  
  // 如果有多条记录，按时间排序（升序）
  const sortedJobs = [...matchingJobs].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
  
  // 取第二条记录的version_test_result值
  if (sortedJobs.length >= 2) {
    // console.log(sortedJobs[1].version_test_result)
    return sortedJobs[1].version_test_result === 'True' ? 'success' : 'error';
  }
};

// 检查步骤是否存在
const hasStep = (stepKey, taskId) => {
  return pipelineSteps.value[taskId] && pipelineSteps.value[taskId].some(step => step.serviceKey === stepKey);
};

// 显示状态历史对话框
const showStatusHistory = (serviceKey, taskId) => {
  currentTaskId.value = taskId;
  currentServiceKey.value = serviceKey;
  statusHistory.value = taskData.value[taskId].filter(item => item.service_key === serviceKey);
  // console.log(statusHistory.value)
  showStatusHistoryDialog.value = true;
};

// 关闭状态历史对话框
const closeStatusHistory = () => {
  showStatusHistoryDialog.value = false;
  currentServiceKey.value = '';
  statusHistory.value = [];
};

// 格式化时间戳
const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 格式化状态
const formatStatus = (status: string) => {
  switch (status) {
    case 'success':
      return '成功';
    case 'in_progress':
      return '进行中';
    case 'failure':
      return '失败';
    default:
      return status;
  }
};

// 初始化流水线步骤
const initializePipelineSteps = (taskId) => {
  // 定义期望的步骤顺序
  const expectedOrder = [
    'jenkins_job_save',
    'log_analysis',
    'env_check',
    'online_test',
    'version_rollback',
    'env_backtrack'
  ];
  
  // 检查taskData中是否存在该taskId的数据
  if (taskData.value[taskId] && Array.isArray(taskData.value[taskId])) {
    // 从数组中提取所有唯一的service_key
    const uniqueServiceKeys = new Set();
    taskData.value[taskId].forEach(item => {
      if (item.service_key) {
        uniqueServiceKeys.add(item.service_key);
      }
    });
    
    // 按照期望的顺序创建步骤数组
    pipelineSteps.value[taskId] = expectedOrder
      .filter(key => uniqueServiceKeys.has(key))
      .map(serviceKey => ({ serviceKey }));
  } else {
    // 如果没有数据或不是数组，设置为空数组
    pipelineSteps.value[taskId] = [];
  }
};

// 监听日期选择器变化
watch(dateFilter, () => {
  handleFilter();
});

// 显示环境检测详情
const showEnvCheckDetails = (serviceKey, taskId) => {
  // console.log(taskData.value[taskId])
  // 查找operation为check_log的记录
  const checkLogRecord = taskData.value[taskId].find(item => 
    item.service_key === serviceKey && 
    (item.操作 === 'check_log' || item.操作 === 'log_save')
  );
  // console.log(checkLogRecord)
  
  if (checkLogRecord && checkLogRecord.details) {
    // 创建一个新对象用于显示
    const detailsItem = {
      env_id: checkLogRecord.details.message.env_id,
      // service_type: checkLogRecord.service_type,
      // status: checkLogRecord.status,
      checktime: checkLogRecord.时间,
      details: checkLogRecord.details.results,
    };
    
    // 使用现有的showFullContent方法显示详情
    showFullContent(detailsItem);
  } else {
    // 如果没有找到相关记录，可以显示一个提示
    console.log('未找到环境检测详情数据');
  }
};

// 定义一个兼容的定时器类型
type TimerType = number | NodeJS.Timeout;

// 添加定时刷新相关变量
const refreshInterval = ref<TimerType | null>(null);

// 开始定时刷新
const startAutoRefresh = () => {
  // 清除可能存在的旧定时器
  stopAutoRefresh();
  
  // 设置数据刷新定时器
  refreshInterval.value = window.setInterval(() => {
    getJobs().then(async () => {
      // 如果有筛选条件，特别是负责人筛选，确保先加载负责人信息
      if (filterPrincipal.value) {
        // 先确保所有任务的负责人信息已加载
        await loadPrincipalInfo();
      }
      
      // 保存当前筛选条件的状态
      const hasFilters = filterEnvId.value || filterBranch.value || filterPrincipal.value || 
                         dateFilter.value || filterAction.value || filterStatus.value;
      
      // 刷新数据后重新应用筛选条件
      if (hasFilters) {
        // 确保在筛选前已加载所有必要的数据
        await Promise.all([
          // 确保加载了所有任务的动作和状态信息
          ...jobs.value.map(async (job) => {
            if (!job.latestAction || !job.latestStatus) {
              try {
                const cachedLogData = localStorage.getItem(`taskLogData_${job.task_id}`);
                if (cachedLogData) {
                  const logData = JSON.parse(cachedLogData);
                  if (logData) {
                    processServicePriority(logData, job);
                  }
                }
              } catch (error) {
                console.error(`Failed to load cached log data for task ${job.task_id}:`, error);
              }
            }
          })
        ]);
        
        handleFilter(); // 重新应用筛选
      } else {
        fetchCurrentPageData(); // 如果没有筛选条件，直接获取当前页数据
      }
    });
  }, 300 * 1000); // 300秒刷新一次
};

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value as number);
    refreshInterval.value = null;
  }
  // 移除对 refreshCountdownTimer 的清理
};

// 添加一个方法专门用于加载所有任务的负责人信息
const loadPrincipalInfo = async () => {
  // 获取所有需要的环境ID
  const envIds = [
    "RAN3-上海高频CI团队-VAT1001",
    "RAN3-上海高频CI团队-VAT1002",
    "RAN3-上海高频CI团队-VAT1003",
    "RAN3-上海高频CI团队-VAT1004",
    "RAN3-上海高频CI团队-VAT1005",
    "RAN3-上海高频CI团队-VAT1006",
    "RAN3-上海高频CI团队-VAT1007",
    "RAN3-上海高频CI团队-VAT1008",
    "RAN3-上海高频CI团队-VAT1009",
    "RAN3-上海高频CI团队-VAT1010",
    "RAN3-上海高频CI团队-VAT1011",
    "RAN3-上海高频CI团队-VAT1012",
    "RAN3-上海高频CI团队-VAT1013",
    "RAN3-上海高频CI团队-VAT1014",
    "RAN3-上海高频CI团队-VAT1015",
    "RAN3-上海高频CI团队-VAT1016",
    "RAN3-上海高频CI团队-VAT1017",
    "RAN3-上海高频CI团队-VAT1018",
    "RAN3-上海高频CI团队-VAT1021"
  ];
  
  // 先检查缓存中是否已有环境负责人数据
  const cachedPrincipalData = localStorage.getItem('envPrincipalData');
  let principalResponse;
  
  if (cachedPrincipalData) {
    // 如果缓存中有数据，直接使用缓存数据
    principalResponse = {
      success: true,
      data: JSON.parse(cachedPrincipalData)
    };
  } else {
    // 如果缓存中没有数据，则发送请求获取
    principalResponse = await jobApi.getBatchEnvPrincipal(envIds);
    
    // 将环境负责人信息存储到localStorage中，避免切换页面时重复请求
    if (principalResponse.success && principalResponse.data) {
      localStorage.setItem('envPrincipalData', JSON.stringify(principalResponse.data));
    }
  }
  
  // 如果成功获取负责人信息，更新任务的负责人字段
  if (principalResponse.success && principalResponse.data) {
    // 遍历所有任务，设置负责人信息
    jobs.value.forEach(job => {
      if (job.env_id && principalResponse.data[job.env_id]) {
        job.principalName = principalResponse.data[job.env_id];
      } else {
        job.principalName = '-';
      }
    });
  }
  
  return principalResponse;
};

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  stopAutoRefresh();
});

// 添加失败原因分析相关的状态
const failureAnalysisContent = ref({});
const isSubmittingAnalysis = ref(false);
// 修改已提交状态记录，不再从localStorage初始化
const submittedTasks = ref(new Set());
// 添加编辑状态管理
const editingTasks = ref(new Set());
// 添加人工处理时长状态
const manualProcessingHours = ref({});

// 添加动作映射函数
const mapActionToLabel = (action) => {
  const actionMap = {
    'jenkins_job_save': '日志保存',
    'log_analysis': '智能分析',
    'env_check': '环境检测',
    'online_test': '复测',
    'version_rollback': '回溯',
    'env_backtrack': '回溯'
  };
  return actionMap[action] || action || '-';
};

// 启用重新提交功能
const enableResubmit = (taskId) => {
  // 如果之前没有保存失败原因内容，则从 jobs 中获取
  if (!failureAnalysisContent.value[taskId]) {
    const job = jobs.value.find(j => j.task_id === taskId);
    if (job && job.fail_reason) {
      failureAnalysisContent.value[taskId] = job.fail_reason;
    }
  }

  // 从已提交集合中移除，允许重新提交
  submittedTasks.value.delete(taskId);
  // 进入编辑状态
  editingTasks.value.add(taskId);

  // 不需要做其他操作，保持行展开状态
};

// 取消编辑功能
const cancelEdit = (taskId) => {
  // 清空当前编辑内容
  failureAnalysisContent.value[taskId] = '';
  manualProcessingHours.value[taskId] = 0;
  // 退出编辑状态
  editingTasks.value.delete(taskId);
  // 如果之前有提交过，恢复到已提交状态
  const job = jobs.value.find(j => j.task_id === taskId);
  if (job && job.fail_reason && job.fail_reason.trim() !== '') {
    submittedTasks.value.add(taskId);
    failureAnalysisContent.value[taskId] = job.fail_reason;
    manualProcessingHours.value[taskId] = job.manual_processing_hours || 0;
  }
};

// 清除失败原因数据
const clearFailureAnalysis = async (taskId) => {
  // 显示确认对话框
  if (!confirm('确定要清除失败原因分析数据吗？此操作不可撤销。')) {
    return;
  }

  try {
    // 调用API清除服务器端数据（传入空字符串和0小时）
    const result = await jobApi.updateTaskFailReason(taskId, '', 0);
    if (result.success) {
      // 清除本地数据
      failureAnalysisContent.value[taskId] = '';
      manualProcessingHours.value[taskId] = 0;
      submittedTasks.value.delete(taskId);
      editingTasks.value.delete(taskId);

      // 更新任务数据
      const job = jobs.value.find(j => j.task_id === taskId);
      if (job) {
        job.fail_reason = '';
        job.manual_processing_hours = 0;
      }

      message("失败原因数据已清除", { type: "success" });
    } else {
      message("清除失败原因数据失败: " + result.message, { type: "error" });
    }
  } catch (error) {
    message("清除失败原因数据出错，请稍后重试", { type: "error" });
    console.error("清除失败原因数据出错:", error);
  }
};

// 提交失败原因分析
const submitFailureAnalysis = async (taskId) => {
  if (!failureAnalysisContent.value[taskId]) {
    return;
  }

  isSubmittingAnalysis.value = true;
  try {
    // 获取人工处理时长，默认为0
    const processingHours = manualProcessingHours.value[taskId] || 0;

    const result = await jobApi.updateTaskFailReason(
      taskId,
      failureAnalysisContent.value[taskId],
      processingHours
    );
    if (result.success) {
      // 提交成功后，更新本地任务数据中的失败原因和处理时长
      const job = jobs.value.find(j => j.task_id === taskId);
      if (job) {
        job.fail_reason = failureAnalysisContent.value[taskId];
        job.manual_processing_hours = processingHours;
      }

      // 记录已提交状态，退出编辑状态
      submittedTasks.value.add(taskId);
      editingTasks.value.delete(taskId);

      // 使用消息提示组件显示成功信息
      message("失败原因提交成功", { type: "success" });

      // 不需要做其他操作，保持行展开状态
    } else {
      message("提交失败原因失败: " + result.message, { type: "error" });
      console.error("提交失败原因失败:", result.message);
    }
  } catch (error) {
    message("提交失败原因出错，请稍后重试", { type: "error" });
    console.error("提交失败原因出错:", error);
  } finally {
    isSubmittingAnalysis.value = false;
  }
};

// 优化获取已提交的失败原因方法
const getSubmittedFailReason = (taskId) => {
  // 首先从任务列表中查找最新的失败原因
  const job = jobs.value.find(j => j.task_id === taskId);
  if (job && job.fail_reason) {
    return job.fail_reason;
  }

  // 如果任务列表中没有，再检查缓存中是否有
  if (failureAnalysisContent.value[taskId]) {
    return failureAnalysisContent.value[taskId];
  }

  return '无详细内容';
};

// 获取已提交的人工处理时长
const getSubmittedProcessingHours = (taskId) => {
  // 首先从任务列表中查找
  const job = jobs.value.find(j => j.task_id === taskId);
  if (job && typeof job.manual_processing_hours !== 'undefined') {
    return job.manual_processing_hours;
  }

  // 如果任务列表中没有，再检查缓存中是否有
  if (typeof manualProcessingHours.value[taskId] !== 'undefined') {
    return manualProcessingHours.value[taskId];
  }

  return 0;
};

</script>

<template>
  <div class="welcome-container">
    
    <el-table
      v-loading="loading"
      :data="currentTableData"
      stripe
      border
      style="width: 100%; margin-top: 20px;"
      @expand-change="handleExpandChange"
    >

      <el-table-column type="expand">
        <template #default="props">
          <el-main>
            <!-- 标签页切换组件 -->
            <div class="tab-container">
              <div class="tabs">
                <div
                  :class="['tab', { active: getActiveTab(props.row.task_id) === 'pipeline' }]"
                  @click="switchExpandedTab(props.row.task_id, 'pipeline')"
                >流水线</div>
                <div
                  :class="['tab', { active: getActiveTab(props.row.task_id) === 'logAnalysis' }]"
                  @click="switchExpandedTab(props.row.task_id, 'logAnalysis')"
                >日志分析</div>
                <div
                  :class="['tab', { active: getActiveTab(props.row.task_id) === 'failureAnalysis' }]"
                  @click="switchExpandedTab(props.row.task_id, 'failureAnalysis')"
                >失败分析</div>
              </div>
            </div>
            
            <!-- 流水线组件 -->
            <div v-if="getActiveTab(props.row.task_id) === 'pipeline'" class="pipeline-content">
              <div class="progress-bar">
                <div class="step-container">
                  <!-- 成功步骤：jenkins_job_save -->
                  <div v-if="hasStep('jenkins_job_save', props.row.task_id)"
                      class="step" :class="{
                        'success': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'error'
                      }">
                    <div class="step-dot" @click="showStatusHistory('jenkins_job_save', props.row.task_id)">
                      <div v-if="getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'success'" class="check-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'warning'" class="refresh-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'error'" class="cross-icon"/>
                    </div>
                    <div class="step-text">日志保存</div>
                  </div>

                  <div v-if="hasStep('jenkins_job_save', props.row.task_id)"
                      class="arrow" :class="{
                        'success': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('jenkins_job_save', props.row.task_id) === 'error'
                    }"/>

                  <!-- 成功步骤：log_analysis -->
                  <div v-if="hasStep('log_analysis', props.row.task_id)"
                      class="step" :class="{
                        'success': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'error'
                      }">
                    <div class="step-dot" @click="showStatusHistory('log_analysis', props.row.task_id)">
                      <div v-if="getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'success'" class="check-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'warning'" class="refresh-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'error'" class="cross-icon"/>
                    </div>
                    <div class="step-text">智能分析</div>
                  </div>

                  <div v-if="hasStep('log_analysis', props.row.task_id)"
                      class="arrow" :class="{
                        'success': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('log_analysis', props.row.task_id) === 'error'
                      }"/>

                  <!-- 环境检查步骤：env_check -->
                  <div v-if="hasStep('env_check', props.row.task_id)"
                      class="step" :class="{
                        'success': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'error'
                      }">
                    <div class="step-dot" @click="showEnvCheckDetails('env_check', props.row.task_id)">
                      <div v-if="getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'success'" class="check-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'warning'" class="refresh-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'error'" class="cross-icon"/>
                    </div>
                    <div class="step-text">环境检测</div>
                  </div>

                  <div v-if="hasStep('env_check', props.row.task_id)"
                      class="arrow" :class="{
                        'success': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('env_check', props.row.task_id) === 'error'
                      }"/>

                  <!-- 在线测试步骤：online_test -->
                  <div v-if="hasStep('online_test', props.row.task_id)"
                      class="step" :class="{
                        'success': getOnlineTestStatus(props.row.task_id) === 'success',
                        'error': getOnlineTestStatus(props.row.task_id) === 'error'
                      }">
                    <div class="step-dot" @click="showStatusHistory('online_test', props.row.task_id)">
                      <div v-if="getOnlineTestStatus(props.row.task_id) === 'success'" class="check-icon"/>
                      <div v-if="getOnlineTestStatus(props.row.task_id) === 'error'" class="cross-icon"/>
                    </div>
                    <div class="step-text">复测</div>
                  </div>
                  <div v-if="hasStep('online_test', props.row.task_id)" class="arrow success"/>

                  <!-- 版本回滚步骤：env_backtrack -->
                  <div v-if="hasStep('env_backtrack', props.row.task_id)"
                      class="step" :class="{
                        'success': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'success',
                        'warning': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'warning',
                        'error': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'error'
                      }">
                    <div class="step-dot" @click="showStatusHistory('env_backtrack', props.row.task_id)">
                      <div v-if="getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'success'" class="check-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'warning'" class="refresh-icon"/>
                      <div v-if="getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'error'" class="cross-icon"/>
                    </div>
                    <div class="step-text">回溯</div>
                  </div>
                  
                  <div v-if="hasStep('env_backtrack', props.row.task_id)" 
                    class="arrow" :class="{
                      'success': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'success',
                      'warning': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'warning',
                      'error': getStepClassSortByTimeStamp('env_backtrack', props.row.task_id) === 'error'
                    }"/>

                  <!-- 结束步骤 -->
                  <div class="step" :class="{
                    'success': jobState[props.row.task_id] === 'PASS',
                    'error': jobState[props.row.task_id] === 'FAIL'
                  }">
                    <div class="step-dot">
                      <div v-if="jobState[props.row.task_id] === 'PASS'" class="check-icon"/>
                      <div v-if="jobState[props.row.task_id] === 'FAIL'" class="cross-icon"/>
                    </div>
                    <div class="step-text">流程结束</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 日志分析组件 -->
            <div v-else-if="getActiveTab(props.row.task_id) === 'logAnalysis'" class="log-analysis-content">
              <div class="log-content" style="margin-top: 15px;">
                <!-- 添加加载状态显示 -->
                <div v-if="logAnalysisLoading[props.row.task_id]" class="loading-text">加载中...</div>
                <!-- 添加错误信息显示 -->
                <div v-if="logAnalysisError[props.row.task_id]" class="error-text">{{ logAnalysisError[props.row.task_id] }}</div>
                <!-- 显示日志分析数据 -->
                <div v-if="logAnalysisData[props.row.task_id]" class="log-data">
                  <table class="analysis-table">
                    <thead class="centered-header">
                      <tr>
                        <th>用例名称</th>
                        <th>状态</th>
                        <th>详细信息</th>
                        <th>生成时间</th>
                        <th>分析结果</th>
                        <th>手动分类</th>
                        <th>人工分析</th>
                        <th>操作</th>
                        <th>原始数据</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="item in logAnalysisData[props.row.task_id].data" :key="item._id">
                        <td>{{ item.testcase_name }}</td>
                        <td>{{ item.status }}</td>
                        <td>{{ item.detail_msg }}</td>
                        <td>{{ item.createdTime }}</td>
                        <td>{{ item.result }}</td>
                        <td>{{ item.editedClassification || item.manualClassification }}</td>
                        <td>{{ item.editedAnalysisResult || item.manualAnalysisResult }}</td>
                        <td>
                          <button class="edit-btn" @click="openEditDialog(item)">修改</button>
                        </td>
                        <td>
                          <button class="view-full-btn" @click="showFullContent(item)">显示</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="table-footer">
                    总计: {{ logAnalysisData[props.row.task_id].total }} 条记录
                  </div>
                </div>
                <el-empty v-else-if="!logAnalysisLoading[props.row.task_id] && !logAnalysisError[props.row.task_id]" description="暂无日志分析数据" />
              </div>
            </div>
            
            <!-- 失败分析组件-->
            <div v-if="getActiveTab(props.row.task_id) === 'failureAnalysis'" class="failure-analysis-container">
              <div class="failure-analysis-content">
                <!-- 已提交状态且未在编辑 -->
                <div v-if="submittedTasks.has(props.row.task_id) && !editingTasks.has(props.row.task_id)" class="submitted-notice">
                  <el-alert
                    title="已提交失败原因分析"
                    type="success"
                    :closable="false"
                    show-icon>
                    <div class="submitted-content">
                      <div class="submitted-reason">
                        <strong>失败原因：</strong>{{ getSubmittedFailReason(props.row.task_id) }}
                      </div>
                      <div class="submitted-hours">
                        <strong>人工处理时长：</strong>{{ getSubmittedProcessingHours(props.row.task_id) }} 小时
                      </div>
                    </div>
                  </el-alert>
                  <div class="edit-actions">
                    <button
                      class="edit-submit-btn"
                      @click="enableResubmit(props.row.task_id)"
                    >
                      更新失败原因
                    </button>
                    <button
                      class="clear-data-btn"
                      @click="clearFailureAnalysis(props.row.task_id)"
                    >
                      清除数据
                    </button>
                  </div>
                </div>
                <!-- 编辑状态（包括新建和修改） -->
                <div v-else class="analysis-form">
                  <div class="form-group">
                    <label>请输入失败原因：</label>
                    <textarea
                      v-model="failureAnalysisContent[props.row.task_id]"
                      class="analysis-textarea"
                      placeholder="请详细描述用例失败的原因..."
                      rows="6"
                    />
                  </div>
                  <div class="form-group">
                    <label>人工处理时长（小时）：</label>
                    <el-input-number
                      v-model="manualProcessingHours[props.row.task_id]"
                      :min="0"
                      :max="999"
                      :precision="1"
                      :step="0.5"
                      placeholder="请输入处理时长"
                      style="width: 200px;"
                    />
                    <span class="time-hint">（默认为0小时，支持小数）</span>
                  </div>
                  <div class="form-actions">
                    <button
                      class="submit-btn"
                      :disabled="isSubmittingAnalysis || !failureAnalysisContent[props.row.task_id]"
                      @click="submitFailureAnalysis(props.row.task_id)"
                    >
                      {{ isSubmittingAnalysis ? '提交中...' : '提交' }}
                    </button>
                    <button
                      class="cancel-btn"
                      :disabled="isSubmittingAnalysis"
                      @click="cancelEdit(props.row.task_id)"
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </el-main>
        </template>
      </el-table-column>

      <!-- 主表格 -->
      <el-table-column prop="task_id" label="任务ID" header-align="center" align="center">
        <template #default="{ row }">
          <el-tooltip :content="row.task_id" placement="top">
            <span class="task-id">{{ row.task_id }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="env_id" label="环境ID" width="260" header-align="center" align="center">
        <template #header>
          <div>
            <el-input
              v-model="filterEnvId"
              placeholder="环境ID"
              clearable
              size="small"
              style="width: 200px; margin-top: 5px;"
              @input="handleFilter"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="负责人" width="160" header-align="center" align="center">
        <template #header>
          <div>
            <el-input
              v-model="filterPrincipal"
              placeholder="负责人"
              clearable
              size="small"
              style="width: 90%; margin-top: 5px;"
              @input="handleFilter"
            />
          </div>
        </template>
        <template #default="{ row }">
          <span v-if="row.principal">{{ row.principal }}</span>
          <el-text v-else v-loading="row.loadingPrincipal">
            {{ row.principalName || '-' }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column prop="jenkins_job_name" label="Jenkins任务名" width="170" header-align="center" align="center"/>
      <el-table-column prop="version_branch" label="分支名" width="180" header-align="center" align="center">
        <template #header>
          <div>
            <el-input
              v-model="filterBranch"
              placeholder="分支名"
              clearable
              size="small"
              style="width: 90%; margin-top: 5px;"
              @input="handleFilter"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="timestamp" label="开始时间" width="170" header-align="center" align="center">
        <template #header>
          <div>
            <div class="block">
              <el-date-picker
                v-model="dateFilter"
                type="date"
                placeholder="开始时间"
                size="small"
                style="width: 130px;"
              />
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ new Date(row.timestamp).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="当前执行动作" width="160" header-align="center" align="center">
        <template #header>
          <div>
            <el-select
              v-model="filterAction"
              placeholder="当前执行动作"
              clearable
              multiple
              collapse-tags
              size="small"
              style="width: 90%; margin-top: 5px;"
              @change="handleFilter"
            >
              <el-option value="jenkins_job_save" label="日志保存" />
              <el-option value="log_analysis" label="智能分析" />
              <el-option value="env_check" label="环境检测" />
              <el-option value="online_test" label="复测" />
              <el-option value="env_backtrack" label="回溯" />
            </el-select>
          </div>
        </template>
        <template #default="{ row }">
          <el-text v-loading="row.loadingAction">
            {{ mapActionToLabel(row.latestAction) }}
          </el-text>
        </template>
      </el-table-column>
      <!-- <el-table-column label="执行状态" width="130" header-align="center" align="center">
        <template #header>
          <div>
            <el-select
              v-model="filterStatus"
              placeholder="执行状态"
              clearable
              size="small"
              style="width: 90%; margin-top: 5px;"
              @change="handleFilter"
            >
              <el-option value="success" label="success" />
              <el-option value="failure" label="failure" />
            </el-select>
          </div>
        </template>
        <template #default="{ row }">
          <el-text v-loading="row.loadingStatus">
            {{ row.latestStatus || '-' }}
          </el-text>
        </template>
      </el-table-column> -->
      <el-table-column label="人工确认测试结果" width="190" header-align="center" align="center">
        <template #default="{ row }">
          <div>
            <el-select 
              v-model="row.statusRadio" 
              placeholder="未确认" 
              style="width: 90px"
              @change="(value) => updateStatus(row.task_id, value)"
            >
              <el-option
                v-for="item in [
                  { value: 'PASS', label: '通过' },
                  { value: 'FAIL', label: '失败' }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>


  <!-- 日志分析原始数据显示组件 -->
  <div v-if="showFullContentDialog" class="full-content-dialog-overlay" @click="closeFullContentDialog">
    <div class="full-content-dialog" @click.stop>
      <div class="full-content-body">
        <div v-for="(value, key) in currentFullContentItem" :key="key" class="content-item">
          <strong>{{ key }}:</strong>
          <span>{{ value }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 人工修改分类组件 -->
  <div v-if="showEditDialog" class="dialog-overlay" @click="closeEditDialog">
    <div class="dialog-content" @click.stop>
      <div class="form-group">
        <label>手动分类:</label>
        <select v-model="editForm.manualClassification" class="select-input">
          <option value="">请选择分类</option>
          <option value="升级用例失败">升级用例失败</option>
          <option value="接入用例失败">接入用例失败</option>
          <option value="ping包用例失败">ping包用例失败</option>
          <option value="灌包流量为0">灌包流量为0</option>
          <option value="灌包流量不足">灌包流量不足</option>
          <option value="other">other</option>
        </select>
      </div>
      <div class="form-group">
        <label>人工分析结果:</label>
        <input v-model="editForm.manualAnalysisResult" placeholder="请输入人工分析结果"/>
      </div>
      <div class="dialog-footer">
        <button class="save-btn" @click="saveEdit">保存</button>
        <button class="cancel-btn" @click="closeEditDialog">取消</button>
      </div>
    </div>
  </div>

  <!-- 流水线节点历史打印组件 -->
  <div v-if="showStatusHistoryDialog" class="status-history-dialog-overlay" @click="closeStatusHistory">
    <div class="status-history-dialog" @click.stop>
      <h3>历史打印 - {{ currentServiceKey }}</h3>
      <div class="status-history-content">
        <table class="status-history-table">
          <thead>
            <tr>
              <th>时间</th>
              <th>操作</th>
              <th>步骤</th>
              <th>状态</th>
              <th>details</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(record, index) in sortedStatusHistory" :key="index"
                :class="{ 'success': record.status === 'success',
                         'warning': record.status === 'in_progress',
                         'error': record.status === 'failure' }">
              <td>{{ formatTimestamp(record.时间) }}</td>
              <td>{{ record.操作 || '-' }}</td>
              <td>{{ record.步骤 || '-' }}</td>
              <td>{{ formatStatus(record.状态) }}</td>
              <td class="details-column">{{ record.details?.trace_info || '-' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome-container {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.task-id {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  color: #606266;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-btn {
  background-color: #1890ff;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.arrow-down {
  display: inline-block;
  margin-left: 5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid white;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #0a0a0a;
  min-width: 100px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  z-index: 1000;
}

.dropdown-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.dropdown-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 成功和失败状态的样式 */
.dropdown-item[data-status="PASS"] {
  color: #52c41a;
}

.dropdown-item[data-status="FAIL"] {
  color: #f56c6c;
}

.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.filter-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.tab-container {
  margin-bottom: 15px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #EBEEF5;
}

.tab {
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab.active {
  color: #409EFF;
  border-bottom-color: #409EFF;
}

.log-analysis-content {
  padding: 10px 0;
}

/* 在样式部分添加 el-main 的高度限制 */
:deep(.el-main) {
  max-height: 1000px;
  overflow-y: auto;
}

.log-content {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
}

.log-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
}

/* 添加日志分析相关样式 */
.analysis-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  table-layout: fixed;  /* 固定表格布局，使列宽设置生效 */
}

.analysis-table th,
.analysis-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  max-width: 200px;        /* 设置最大列宽 */
  white-space: nowrap;     /* 防止文本换行 */
  overflow: hidden;        /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

/* 添加悬停提示 */
.analysis-table td:hover {
  overflow: visible;
  white-space: normal;
  word-break: break-all;
  position: relative;
  z-index: 1;
  background-color: #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.analysis-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.analysis-table tr:nth-child(even) {
  background-color: #fafafa;
}

.analysis-table tr:hover {
  background-color: #f0f0f0;
}

.table-footer {
  text-align: right;
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

.loading-text, .error-text {
  padding: 20px;
  text-align: center;
}

.error-text {
  color: #F56C6C;
}

.log-data {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  white-space: pre-wrap;
}

/* 对话框样式 */
.full-content-dialog-overlay,
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 500px;
  max-width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.full-content-dialog {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 800px; /* 固定宽度为800px */
  max-width: 90%; /* 在小屏幕上最大宽度为90% */
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.full-content-body {
  margin-top: 15px;
}

.content-item {
  margin-bottom: 10px;
  word-break: break-all;
}

.content-item strong {
  display: inline-block;
  min-width: 120px;
  font-weight: bold;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.select-input,
.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.save-btn,
.cancel-btn,
.edit-btn,
.view-full-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  margin-left: 10px;
}

.save-btn {
  background-color: #409EFF;
  color: white;
}

.cancel-btn {
  background-color: #909399;
  color: white;
}

.edit-btn {
  background-color: #409EFF;
  color: white;
}

.view-full-btn {
  background-color: #67C23A;
  color: white;
}

/* 流水线样式 */
.progress-bar {
  margin-bottom: 30px;
}

.step-container {
  display: flex;
  position: relative;
  margin-left: 100px;
  margin-top: 60px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #F2F6FC;
  border: 2px solid #DCDFE6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.step.success .step-dot {
  background-color: #67C23A;
  border-color: #67C23A;
}

.step.warning .step-dot {
  background-color: #E6A23C;
  border-color: #E6A23C;
}

.step.error .step-dot {
  background-color: #F56C6C;
  border-color: #F56C6C;
}

.check-icon:after {
  content: "✓";
  color: white;
  font-size: 14px;
}

.refresh-icon:after {
  content: "⟳";
  color: white;
  font-size: 14px;
}

.cross-icon:after {
  content: "✕";
  color: white;
  font-size: 14px;
}

.step-text {
  font-size: 12px;
  color: #606266;
}

.arrow {
  position: relative;
  width: 150px;
  height: 3.5px;
  background-color: #d9d9d9;
  margin: 0 5px;
  top: 10px;
  z-index: 2;
}

.arrow.warning {
  background-color: #faad14;
}

.arrow.success {
  background-color: #52c41a;
}

.arrow.error {
  background-color: #f5222d;
}

.arrow::after {
  content: '';
  position: absolute;
  right: -5px;
  top: -5.5px;
  width: 0;
  height: 0;
  border-left: 12px solid #d9d9d9;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

.arrow.warning::after {
  border-left-color: #faad14;
}

.arrow.success::after {
  border-left-color: #52c41a;
}

.arrow.error::after {
  border-left-color: #f5222d;
}

/* 状态历史对话框样式 */
.status-history-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.status-history-dialog {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 1200px;
  max-width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.status-history-content {
  margin: 20px 0;
}

.status-history-table {
  width: 100%;
  border-collapse: collapse;
}

.status-history-table th,
.status-history-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.status-history-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.status-history-table td {
  word-break: break-word;
}

/* 状态历史表格列宽设置 */
.status-history-table th:nth-child(1),
.status-history-table td:nth-child(1) {
  width: 25%;  /* 时间列宽度 */
  text-align: center;
}

.status-history-table th:nth-child(2),
.status-history-table td:nth-child(2) {
  width: 25%;  /* 操作列宽度 */
  text-align: center;
}

.status-history-table th:nth-child(3),
.status-history-table td:nth-child(3) {
  width: 15%;  /* 状态列宽度 */
  text-align: center;
}

/* 时间列 */
.status-history-table th:nth-child(1),
.status-history-table td:nth-child(1) {
  width: 15%;
  text-align: center;
}

/* 操作列 */
.status-history-table th:nth-child(2),
.status-history-table td:nth-child(2) {
  width: 12%;
  text-align: center;
}

/* 步骤列 */
.status-history-table th:nth-child(3),
.status-history-table td:nth-child(3) {
  width: 15%;
  text-align: center;
}

/* 状态列 */
.status-history-table th:nth-child(4),
.status-history-table td:nth-child(4) {
  width: 10%;
  text-align: center;
}

/* details列 - 增加宽度 */
.status-history-table th:nth-child(5),
.status-history-table td:nth-child(5) {
  width: 48%;  /* 大幅增加details列宽度 */
  text-align: left;
}

/* 为details列添加特殊样式，处理可能的长文本 */
.details-column {
  white-space: pre-wrap;  /* 保持换行和空格 */
  word-break: break-word;  /* 长单词自动换行 */
  overflow-wrap: break-word;  /* 兼容性更好的换行 */
  max-height: 120px;  /* 限制最大高度 */
  overflow-y: auto;  /* 超出时显示滚动条 */
  padding: 8px;
  line-height: 1.4;
  font-size: 13px;
  background-color: #fafafa;
  border-radius: 4px;
}

/* 悬停时高亮显示 */
.details-column:hover {
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.button-pass{
  --el-radio-button-checked-bg-color:#67C23A;
  --el-radio-button-checked-text-color:var(--el-color-white);
  --el-radio-button-checked-border-color:#67C23A;
  --el-radio-button-disabled-checked-fill:var(--el-border-color-extra-light);
}

.button-fail{
  --el-radio-button-checked-bg-color:#F56C6C;
  --el-radio-button-checked-text-color:var(--el-color-white);
  --el-radio-button-checked-border-color:#F56C6C;
  --el-radio-button-disabled-checked-fill:var(--el-border-color-extra-light);
}

.centered-header th {
  text-align: center;
}

/* 失败原因分析样式 */
.failure-analysis-container {
  padding: 15px;
}

.failure-analysis-content {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.analysis-form {
  margin-top: 15px;
}

.analysis-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  box-sizing: border-box;
}

.form-actions {
  margin-top: 15px;
  text-align: right;
}

.submit-btn {
  padding: 8px 20px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.submit-btn:hover {
  background-color: #66B1FF;
}

.submit-btn:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.submitted-notice {
  margin-bottom: 15px;
}

.edit-actions {
  margin-top: 15px;
  text-align: right;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.edit-submit-btn {
  padding: 8px 20px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.edit-submit-btn:hover {
  background-color: #66b1ff;
}

.clear-data-btn {
  padding: 8px 20px;
  background-color: #F56C6C;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.clear-data-btn:hover {
  background-color: #f78989;
}

.cancel-btn {
  padding: 8px 20px;
  background-color: #909399;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-left: 10px;
}

.cancel-btn:hover {
  background-color: #a6a9ad;
}

.cancel-btn:disabled {
  background-color: #c0c4cc;
  cursor: not-allowed;
}

.time-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/* 添加已提交失败原因的样式 */
.submitted-content {
  margin-top: 8px;
}

.submitted-reason {
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 3px solid #67C23A;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  color: #606266;
  max-height: 150px;
  overflow-y: auto;
  margin-bottom: 8px;
}

.submitted-hours {
  padding: 8px;
  background-color: #f6ffed;
  border-radius: 4px;
  border-left: 3px solid #52c41a;
  font-size: 14px;
  color: #606266;
}
</style>
