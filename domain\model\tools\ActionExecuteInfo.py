# -*-coding:utf-8-*-
from pydantic import BaseModel, Field
from enum import Enum
from typing import Optional
from domain.model.tools.Action import TdlResponse

class ActionExecuteStatusEnum(str, Enum):
    UNSTARTED = "未开始"
    RUNNING = "执行中"
    SUCCESS = "成功"
    FAIL = "失败"
    ABNORMAL ="异常"


class ActionExecuteInfo(BaseModel):
    status: ActionExecuteStatusEnum = Field(description="action的执行状态,包括五种状态：未开始、执行中、成功、失败、异常")
    output: Optional[dict] = Field(description="action执行的输出信息")


class ActionExecuteInfoResponse(TdlResponse):
    data: ActionExecuteInfo = Field(description="执行结果数据")

    @property
    def execute_info(self):
        return self.data.model_dump()


