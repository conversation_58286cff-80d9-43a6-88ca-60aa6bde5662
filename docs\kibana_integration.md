# Kibana Integration

## Overview

This document describes the integration of Kibana with the CI Pipeline Visualization application. The integration provides a convenient way for users to access Kibana dashboards and logs from different environments (production, development, and debug) directly from the application.

## Features

1. **Environment-Based Navigation**
   - Production Environment (http://*************:5601)
   - Development Environment (http://************:5601)
   - Debug Environment (http://*************:9100)
   - Automatic selection based on current application environment

2. **Direct Access**
   - One-click access to Kibana from the main navigation menu
   - Opens in a new browser tab for convenience
   - Preserves the current application state

3. **Preconfigured Settings**
   - Default time range set to last 30 days
   - Discover view automatically loaded
   - Refresh interval paused for better performance

## Implementation Details

### Direct Navigation

The Kibana integration is implemented as a direct navigation link in the main menu that opens the appropriate Kibana URL based on the current environment:

- When clicked, it automatically determines whether to open the Production or Development Kibana URL
- The URL includes the appropriate time range parameter (default: last 30 days)
- The link opens in a new browser tab to maintain the current application state

### URL Construction

The Kibana URL is constructed dynamically based on the selected environment and time range:

```
{baseUrl}/app/kibana#/discover?_g=(refreshInterval:(pause:!t,value:0),time:(from:{timeRange},mode:quick,to:now))
```

Where:
- `{baseUrl}` is the base URL of the selected Kibana environment
- `{timeRange}` is the selected time range (e.g., now-30d for the last 30 days)

### Environment Detection

The system automatically detects your current environment setting and directs you to the appropriate Kibana URL:

- Production environment: http://*************:5601
- Development environment: http://************:5601
- Debug environment: http://*************:9100 (with Elasticsearch connection to http://10.230.217.245:9200)

## Usage

1. **Accessing Kibana**
   - Click on the "Kibana" link in the main navigation menu
   - The system will automatically determine which Kibana URL to open based on your current environment setting
   - Kibana will open in a new browser tab
   - Use the provided credentials to log in if prompted (Username: elastic, Password: Zenap_123)

2. **Environment-Based Navigation**
   - If your current environment is set to Production, you'll be directed to the Production Kibana URL
   - If your current environment is set to Development, you'll be directed to the Development Kibana URL
   - If your current environment is set to Debug, you'll be directed to the Debug Kibana URL with pre-configured Elasticsearch connection
   - For Production and Development, the time range is automatically set to the last 30 days

## Security Considerations

1. **Credentials Handling**
   - Production/Development environment credentials: Username: elastic, Password: Zenap_123
   - Debug environment credentials: Username: elastic, Password: Ran_test@1
   - Credentials are embedded in the code for convenience
   - Consider implementing a more secure credential management system for production use

2. **Cross-Origin Requests**
   - The integration uses direct URL navigation to avoid CORS issues
   - No direct API calls are made to Kibana from the application

## Future Enhancements

1. **Time Range Selection**
   - Add a dropdown in the application to select different time ranges before opening Kibana

2. **Saved Searches Integration**
   - Add quick links to commonly used saved searches in Kibana

3. **Dashboard Integration**
   - Add direct links to specific Kibana dashboards

4. **Authentication Integration**
   - Implement single sign-on between the application and Kibana

## Troubleshooting

1. **Kibana Not Loading**
   - Verify network connectivity to the Kibana server
   - Check if the Kibana URL is correct
   - Ensure you have the necessary network access permissions

2. **Authentication Issues**
   - For Production/Development: Verify you're using the correct credentials (Username: elastic, Password: Zenap_123)
   - For Debug environment: Verify you're using the correct credentials (Username: elastic, Password: Ran_test@1)
   - Check if your account has the necessary permissions
   - Note that the Debug environment uses a different Kibana interface that requires the Elasticsearch connection details

3. **No Data Displayed**
   - Try expanding the time range to see if data exists in a different time period
   - Verify that the Elasticsearch indices contain data for the selected time range
