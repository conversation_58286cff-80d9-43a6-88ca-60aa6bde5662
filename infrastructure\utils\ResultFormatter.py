"""
@File: ResultFormatter.py
@Author: 许王禾子10333721
@Time: 2024/3/4 11:22
@License: Copyright 2022-2030
@Desc: None
"""
import traceback


def format_query_mo_result(result: list) -> dict:
    """
    将
    [
     [{"ManagedElement": "1", "SubNetwork": "11", "ldn": "a=1;b=2;c=3,d=4", "k1": "v1", "k2": "v21"},
      {"ManagedElement": "1", "SubNetwork": "11", "ldn": "a=1;b=2;c=3,d=5", "k1": "v2", "k2": "v22"}],
     [{"ManagedElement": "2", "SubNetwork": "22", "ldn": "a=1;b=2;c=3,d=6", "k1": "v3", "k2": "v23"},
      {"ManagedElement": "2", "SubNetwork": "22", "ldn": "a=1;b=2;c=3,d=7", "k1": "v4", "k2": "v24"}]
    ]
    转化为
    {
     "1": [
        {"ManagedElement": "1", "SubNetwork": "11", "ldn": "a=1;b=2;c=3,d=4", "k1": "v1", "k2": "v21"},
        {"ManagedElement": "1", "SubNetwork": "11", "ldn": "a=1;b=2;c=3,d=5", "k1": "v2", "k2": "v22"}],
     "2": [
        {"ManagedElement": "2", "SubNetwork": "22", "ldn": "a=1;b=2;c=3,d=6", "k1": "v3", "k2": "v23"},
        {"ManagedElement": "2", "SubNetwork": "22", "ldn": "a=1;b=2;c=3,d=7", "k1": "v4", "k2": "v24"}]
    }
    """
    if len(result) < 1:
        return result
    formatted_res = {}
    try:
        for res in result:
            if len(res) == 0:
                continue
            me_id = res[0].get("ManagedElement") or res[0].get("meId")
            if me_id not in formatted_res:
                formatted_res[me_id] = []
            formatted_res[me_id].extend(res)
    except Exception:
        print(traceback.format_exc(limit=10))
    return formatted_res






