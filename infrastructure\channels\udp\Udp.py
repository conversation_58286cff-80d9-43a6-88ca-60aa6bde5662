# coding=utf-8
'''
Created on 2023-08-28

@author: 10156505
'''
import asyncio
import re
import socket
import time

from infrastructure.logger.logger import logger
from infrastructure.channels.Channel import Channel
from infrastructure.device.DeviceCmd import DeviceCmd
from infrastructure.utils.TimeHandler import time_limit


class Udp(Channel):

    def __init__(self, host, port, timeout=15):
        super(Udp, self).__init__(host, port, "UDP")
        self.timeout = timeout
        self._bufSize = 10240

    def __del__(self):
        if self._tn is not None:
            self._tn.close()
            self._tn = None

    def get_status(self):
        if self._tn is not None:
            return True
        else:
            return False

    def connect(self, *args):
        try:
            self._tn = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        except Exception as e:
            logger.error(f'Udp host: {repr(self._host)},port: {repr(self._port)}, error: {repr(e)}')

    def disconnect(self, *args):
        if self._tn is not None:
            self._tn.close()
            self._tn = None

    def bind_local_ip_port(self, localIP, localPort):
        if self._tn is not None:
            localPort = int(localPort)
            self._tn.bind((localIP, localPort))

    async def execute_cmd(self, cmdObj):
        if cmdObj.command is not None:
            cmdObj = await self.write(cmdObj)

        if cmdObj.cmd_result.result:
            cmdObj = await self.read(cmdObj)
        return cmdObj.cmd_result

    async def clear_buff(self, expectedValue):
        try:
            await self._find_expected_buff(expectedValue)
        except Exception:
            logger.debug("clear buff timeout")

    async def read(self, cmdObj):
        try:
            self._tn.settimeout(cmdObj.timeout)
            self._recvTimeout = cmdObj.timeout
            cmdObj = await self._recv_result(cmdObj)
        except Exception as e:
            logger.error(f'Udp host: {repr(self._host)},port: {repr(self._port)}, error: {repr(e)}')
            cmdObj.fail_to_excute(f'Udp host: {repr(self._host)},port: {repr(self._port)}, error: {repr(e)}')
        return cmdObj

    async def write(self, cmdObj):
        cmdtype = isinstance(cmdObj.command,str)
        if cmdtype:
            cmdObj.command = cmdObj.command.encode('utf-8')
        try:
            self._tn.sendto(cmdObj.command, (self._host, self._port))
        except Exception as e:
            logger.error(f'Udp host: {repr(self._host)},port: {repr(self._port)}, error: {repr(e)}')
            cmdObj.fail_to_excute(f'Udp host: {repr(self._host)},port: {repr(self._port)}, error: {repr(e)}')
        return cmdObj

    async def _recv_result(self, cmdObj):
        buff, isExpectedBuff = await self._find_expected_buff(cmdObj.expected)
        if isExpectedBuff:
            cmdObj.success_to_excute(buff)
        else:
            cmdObj.fail_to_excute(
                'Udp recv failed:expected {0},but not found, buff is {1}'.format(cmdObj.expected, buff))
        return cmdObj

    async def _find_expected_buff(self, expectedValue):
        buff, _ = self._recvfrom()
        if expectedValue in ['', None]:
            return buff.decode(), True
        return await self._recv_total_buff(expectedValue, buff)

    async def _recv_total_buff(self, expectedValue, buff):
        totalBuff = b""
        initTime = time.time()
        while True:
            await asyncio.sleep(0)
            totalBuff = totalBuff + buff
            expectmessage = re.findall(expectedValue, totalBuff.decode())
            if len(expectmessage) != 0:
                return totalBuff.decode(), True
            if (time.time() - initTime) > int(self._recvTimeout):
                return totalBuff.decode(), False
            buff, _ = self._recvfrom()

    def _recvfrom(self):
        @time_limit(self._recvTimeout)
        def __recvfrom(self):
            return self._tn.recvfrom(self._bufSize)
        return __recvfrom(self)

if __name__ == '__main__':
    import time
    i = 0
    while i < 2:
        print("times : {0}".format(i))
        udp1 = Udp("127.0.0.1", 9933)
        udp1.connect()
        cmdObj = DeviceCmd("query -qt 1".encode('utf-8'), '')  # write and read
        cmd_result = udp1.excute_cmd(cmdObj)
        print(cmd_result.return_string)
        time.sleep(5)
        i = i + 1
