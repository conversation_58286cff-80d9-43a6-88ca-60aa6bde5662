# -*- coding = utf-8 -*- #
# @Time : 2024/4/25 17:12
# <AUTHOR> 10333573

import datetime
import os

import asyncio
import traceback

from infrastructure.logger.logger import logger


async def _exec(script, timeout=5, outputs=None):
    if outputs is None:
        outputs = {"stdout": b'', "stderr": b''}
    process = await create_subprocess(script)

    logger.info(f"The maximum allowable execution time for scripts is {timeout} seconds")
    start = datetime.datetime.now()

    try:
        tasks = [
            asyncio.create_task(read_stream(process.stdout, "stdout", outputs), name="out"),
            asyncio.create_task(read_stream(process.stderr, "stderr", outputs), name="err")
        ]
        rlt = await asyncio.wait(tasks, timeout=timeout, return_when=asyncio.FIRST_COMPLETED)
        await terminate(process)

        cancel(rlt[0], rlt[1])
        logger.info('Tasks completed')

        end = datetime.datetime.now()
        logger.info(f"run time: {end - start}")

    except Exception as e:
        logger.error(f'Unexpected error: {e}')
        logger.error(f"{traceback.format_exc()}")
    finally:
        if process.returncode is None:
            logger.info("Subprocess didn't terminate, killing it...")
            process.kill()

    return outputs


async def read_stream(stream, name, outputs):
    try:
        while not stream.at_eof():
            line = await stream.readline()
            if line:
                outputs[name] += line
        return outputs[name]
    except asyncio.CancelledError:
        logger.info(f'Task {name} has been cancelled')
        return outputs[name]


async def create_subprocess(script):
    return await asyncio.create_subprocess_exec(
        *script,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
    )


async def terminate(process):
    if process.returncode is None:
        process.terminate()
        await asyncio.sleep(0.1)


def cancel(done, pending):
    if pending:
        logger.info("pending tasks:")
        for task in pending:
            logger.info(f">> {task.get_name()}")
            task.cancel()
    if done:
        for task in done:
            task.cancel()


def t_ok(cmd, to):
    a = loop.run_until_complete(_exec(cmd, to))
    logger.info(f"out:")
    logger.info(a["stdout"].decode("GBK"))
    logger.info(f"err:")
    logger.info(a["stderr"].decode("GBK"))


if __name__ == '__main__':
    # Create an asyncio event loop
    loop = asyncio.get_event_loop()
    pathBat = os.path.normpath(r'C:\Users\<USER>\Documents\学习分享\myScripts\wudi2.bat')
    # Use the event loop to run the function
    cmd = ['cmd.exe', '/c', pathBat]
    pathSh = os.path.normpath(r'C:\Users\<USER>\Documents\学习分享\myScripts\aiyo.sh')
    executor = os.path.normpath(r"D:\Program Files (x86)\Git\bin\bash.exe")
    cmdSh = [executor, pathSh]

    # t_ok(cmdSh, 5)
    logger.info(f"==================")
    t_ok(cmd, 10)
