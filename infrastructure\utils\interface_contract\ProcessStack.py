# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import logging
import re
DOCKER = 'docker'
PAD = 'pad'


class ProcessStack(object):

    '''
    classdocs
    '''

    def __init__(self):
        '''
        Constructor
        '''
        self._items = []

    def is_empty(self):
        return self._items == []

    def operate_stack(self, cmd):
        self.push(cmd)
        self.pop(cmd)

    def push(self, cmd):
        if re.search('^docker\s+exec\s+-it', cmd):
            self._items.append(DOCKER)
        if re.search('^pad\s+', cmd) and self.peek() != PAD:
            self._items.append(PAD)

    def pop(self, cmd):
        if re.search('^exit', cmd) and not self.is_empty():
            self._items.pop()

    def peek(self):
        try:
            return self._items[len(self._items) - 1]
        except:
            return None

    def size(self):
        return len(self._items)

    def pop_all(self):
        self._items = []
