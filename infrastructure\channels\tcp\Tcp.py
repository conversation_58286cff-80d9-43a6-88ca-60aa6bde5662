# coding=utf-8
import asyncio
import logging
import re
import socket
import time

from infrastructure.channels.Channel import Channel
from infrastructure.logger.logger import logger


class Tcp(Channel):

    '''
        Tcp channel
    '''

    def __init__(self, host, port, timeout=60):
        super(Tcp, self).__init__(host, port, 'Tcp')
        self._connTimeout = timeout
        self._bufSize = 8192
        self._recvTimeout = 60

    def __del__(self):
        if self._tn is not None:
            self._tn.close()
            self._tn = None

    def connect(self, *args):
        try:
            ADDR = (self._host, int(self._port))
            self._tn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._tn.connect(ADDR)
            self._tn.settimeout(self._connTimeout)
        except socket.error as e:
            logger.debug("create tcp link failed,{0}".format(e))
        else:
            return self._tn

    def get_status(self):
        if self._tn is None:
            return False
        else:
            return True

    async def clear_buff(self, buffSize=2048, timeout=0.3):
        return

    def disconnect(self, *args):
        self._tn.close()

    async def _recv_result(self, cmdObj):
        buff, isExpectedBuff = await self._find_expected_buff(cmdObj.expected)
        if isExpectedBuff:
            cmdObj.success_to_excute(buff)
        else:
            cmdObj.fail_to_excute('TCP recv failed:expected {0},but not found, buff is {1}'.format(cmdObj.expected, buff))
        return cmdObj

    async def _find_expected_buff(self, expectedValue):
        try:
            await asyncio.sleep(0.5)
            buff = self._tn.recv(self._bufSize)
        except:
            await asyncio.sleep(10)
            buff = self._tn.recv(self._bufSize)
        return await self._recv_total_buff(expectedValue, buff)

    async def _recv_total_buff(self, expectedValue, buff):
        totalBuff = ""
        initTime = time.time()
        while True:
            totalBuff = totalBuff + buff.decode('utf8', errors='ignore')
            expectmessage = re.findall(expectedValue, buff.decode())
            if expectedValue == '':
                return totalBuff, True
            if len(expectmessage) != 0:
                return totalBuff, True
            if (time.time() - initTime) > int(self._recvTimeout):
                return totalBuff, False
            await asyncio.sleep(0.01)
            buff = self._tn.recv(self._bufSize)

    async def clear_cache(self):
        self._tn.settimeout(1)
        try:
            cache = self._tn.recv(self._bufSize)
            while len(cache) == self._bufSize:
                await asyncio.sleep(0)
                cache = self._tn.recv(self._bufSize)
        except Exception as e:
            logger.error('Tcp read : {0}'.format(e))

    async def read(self, cmdObj):
        self._recvTimeout = cmdObj.timeout
        if self._tn is None:
            logging.error('Tcp link is not create')
            cmdObj.fail_to_excute('Tcp link is not create')
        else:
            self._recvTimeout = cmdObj.timeout
            self._tn.settimeout(self._connTimeout)
            try:
                cmdObj = await self._recv_result(cmdObj)
            except AttributeError as e0:
                logger.debug('Tcp read: {0}'.format(e0))
                cmdObj.fail_to_excute('Tcp read failed: {0}'.format(e0))
            except socket.timeout as e1:
                logger.debug('Tcp read : {0}'.format(e1))
                cmdObj.fail_to_excute('Tcp read failed: {0}'.format(e1))
            except socket.error as e2:
                logger.debug('Tcp read : {0}'.format(e2))
                cmdObj.fail_to_excute('Tcp read failed: {0}'.format(e2))
        return cmdObj

    async def write(self, cmdObj):
        if self._tn is None:
            logging.error('Tcp link is not create')
            cmdObj.fail_to_excute('Tcp link is not create')
        else:
            try:
                self._tn.send('{0}'.format(cmdObj.command).encode())
            except AttributeError as e0:
                logger.error('Tcp write : {0}'.format(e0))
                cmdObj.fail_to_excute('Tcp write error: {0}'.format(e0))
            except socket.error as e1:
                logger.error('Tcp write error : {0}'.format(e1))
                cmdObj.fail_to_excute('Tcp write error : {0}'.format(e1))
            except Exception as e2:
                logger.error('Tcp write error : {0}'.format(e2))
                cmdObj.fail_to_excute('Tcp write error : {0}'.format(e2))
        return cmdObj


if __name__ == '__main__':
    clint = Tcp("10.226.198.156", '4000')
    # clint = Tcp("127.0.0.1", "3389")
    clint.connect()
    #print(clnt.execute_cmd(DeviceCmd('111', '111')))
