# Jenkins任务聚合报表前端使用说明

## 概述

Jenkins任务聚合报表是一个强大的数据分析工具，集成在现有的开发报表页面中，提供多维度的Jenkins任务统计和分析功能。

## 功能特性

### 🎯 核心功能
- **时间范围查询**: 支持自定义时间范围的任务统计
- **多维度过滤**: 支持按环境ID、负责人、任务ID进行精确过滤
- **人工处理时间统计**: 自动计算manual_processing_hours总和
- **多维度分组**: 支持按环境、负责人、任务ID分组查看详细统计

### 📊 统计指标
- **总任务数**: 指定条件下的所有任务数量
- **唯一任务数**: 去重后的任务数量
- **总人工处理时间**: 所有任务的人工处理时间总和
- **分组详情**: 各个分组维度的详细统计信息

## 使用方法

### 1. 访问报表
1. 打开开发报表页面 (`/dev/table`)
2. 在页面右下角找到"Jenkins任务聚合报表"卡片
3. 点击"查看报表"按钮打开聚合报表对话框

### 2. 设置查询条件

#### 基本条件
- **开始日期**: 必填，选择查询的开始日期
- **结束日期**: 必填，选择查询的结束日期
- **环境ID**: 可选，过滤特定环境，如：RAN3-T17-10466
- **负责人**: 可选，过滤特定负责人的任务
- **任务ID**: 可选，查询特定任务的详情

#### 分组选项
- **按环境分组**: 开启后显示每个环境的详细统计
- **按负责人分组**: 开启后显示每个负责人的详细统计
- **按任务ID分组**: 开启后显示每个任务的详细信息

### 3. 执行查询
1. 设置完查询条件后，点击"查询聚合"按钮
2. 系统将显示加载状态，请耐心等待
3. 查询完成后，结果将显示在下方的结果区域

### 4. 查看结果

#### 总体统计
在结果区域顶部，显示四个关键指标：
- 总任务数
- 唯一任务数  
- 总人工处理时间（自动格式化为小时/分钟）
- 查询时间范围

#### 分组统计表格
根据选择的分组选项，显示相应的分组统计表格：

**按环境分组表格**:
- 环境ID
- 总任务数
- 唯一任务数
- 人工处理时间

**按负责人分组表格**:
- 负责人
- 总任务数
- 唯一任务数
- 人工处理时间
- 负责环境（标签形式显示）

**按任务ID分组表格**:
- 任务ID（支持悬停查看完整ID）
- 任务数
- 人工处理时间
- 环境ID
- 负责人

## 使用场景

### 📈 日常统计
- 查看特定时间段内的任务执行情况
- 统计团队的工作量和人工处理时间
- 分析不同环境的任务分布

### 🔍 问题分析
- 按负责人分组，分析个人工作负载
- 按环境分组，识别问题环境
- 按任务ID查看特定任务的详细信息

### 📊 报告生成
- 生成周报、月报的数据支撑
- 为管理层提供量化的工作统计
- 支持多维度的数据分析需求

## 操作技巧

### 💡 高效使用
1. **预设时间范围**: 系统默认设置最近7天的时间范围
2. **组合查询**: 可以同时使用多个过滤条件和分组选项
3. **重置功能**: 使用"重置"按钮快速清空所有条件
4. **表格滚动**: 任务ID分组表格支持垂直滚动，方便查看大量数据

### ⚠️ 注意事项
1. **时间范围**: 开始日期和结束日期为必填项
2. **数据量**: 查询大时间范围时可能需要较长时间
3. **分组选择**: 建议根据实际需求选择分组选项，避免不必要的数据加载
4. **网络状况**: 确保网络连接稳定，避免查询中断

## 数据说明

### 📋 字段含义
- **manual_processing_hours**: 人工处理时间，单位为小时
- **total_jobs**: 包含重复任务的总数量
- **unique_task_count**: 去重后的唯一任务数量
- **env_id**: 环境标识符
- **principal**: 环境维护负责人
- **task_id**: 任务唯一标识符

### 🔄 数据更新
- 数据来源于实时的Jenkins任务数据库
- 统计结果反映查询时刻的最新数据状态
- 建议定期刷新获取最新统计结果

## 技术支持

如遇到问题或需要功能改进，请联系开发团队。

### 常见问题
1. **查询超时**: 尝试缩小时间范围或减少分组选项
2. **数据为空**: 检查查询条件是否过于严格
3. **加载缓慢**: 确认网络连接和服务器状态

---

*最后更新: 2024年*
