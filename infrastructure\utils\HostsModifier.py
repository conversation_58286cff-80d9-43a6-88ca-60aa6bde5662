"""
@File: HostsModifier.py
@Author: 许王禾子10333721
@Time: 2025/1/15 16:30
@License: Copyright 2022-2030
@Desc: None
"""

import os
import shutil
import platform


class HostsModifier:
    def __init__(self, dns_entry: str):
        self._dns_entry = dns_entry
        self._hosts_path = ""
        self._backup_path = ""

    def modify_hosts(self):
        """
        执行 hosts 文件的备份和修改操作。
        """
        try:
            self._hosts_path = self._get_hosts_path()
            self._backup_path = self._hosts_path + '.bak'
            self._backup_hosts_file()
            self._add_entry()
        except Exception as err:
            print(err)

    def _get_hosts_path(self) -> str:
        """
        根据当前操作系统返回 hosts 文件路径。
        """
        current_platform = platform.system()
        if current_platform == 'Windows':
            return r'C:\Windows\System32\drivers\etc\hosts'
        elif current_platform in ['Linux', 'Darwin']:  # Linux 和 macOS 支持
            return '/etc/hosts'
        else:
            raise Exception("Unsupported operating system")

    def _backup_hosts_file(self):
        """
        备份原始的 hosts 文件。
        """
        if not os.path.exists(self._backup_path):
            shutil.copyfile(self._hosts_path, self._backup_path)
            print(f"Backup created at: {self._backup_path}")
        else:
            print(f"Backup already exists at: {self._backup_path}")

    def _entry_exists(self, entry: str) -> bool:
        """
        检查待添加的 DNS 语句是否已经存在于 hosts 文件中。
        """
        with open(self._hosts_path, 'r') as hosts_file:
            hosts_content = hosts_file.readlines()

        for line in hosts_content:
            if entry.strip() == line.strip():
                return True
        return False

    def _add_entry(self):
        """
        添加 DNS 语句到 hosts 文件中，确保不重复。
        """
        for entry in self._dns_entry.splitlines():
            if entry and not self._entry_exists(entry):
                with open(self._hosts_path, 'a') as hosts_file:
                    hosts_file.write(f"\n{entry}")
                print(f"New entry added: {entry.strip()}")


if __name__ == '__main__':
    # 示例：为 example.com 添加 DNS 记录
    dns_entry = "\n10.2.71.207    mongo1.yiyi.com\n10.2.70.202    mongo2.yiyi.com\n10.2.70.236    mongo3.yiyi.com\n"
    hosts_modifier = HostsModifier(dns_entry)
    hosts_modifier.modify_hosts()
