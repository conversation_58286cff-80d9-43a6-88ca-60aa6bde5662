# coding=utf-8

import logging
import select
import socket

from infrastructure.channels.Channel import Channel
from infrastructure.channels.telnet.Telnetlib import Telnetlib


class Telnet(Channel):

    '''
    Telnet
    '''

    def __init__(self, host, port, timeout=5):
        '''
        Constructor
        '''
        super(Telnet, self).__init__(host, port, 'Telnet')
        self._timeout = timeout
        self.matchIndex = -1

    def get_status(self):
        if self._tn is None or not self._tn.get_socket() or self._tn.eof:
            return False
        else:
            return True

    def connect(self, *args):
        try:
            logging.info('ip:{0},port:{1},time:{2}'.format(self._host, self._port, self._timeout))
            #             self._tn = telnetlib.Telnet(self._host, self._port, self._timeout)
            self._tn = Telnetlib(self._host, self._port, self._timeout)
        except socket.error as e1:
            logging.error('Telnet can not be create, please check the param or network status! err=%s' % (e1))
        else:
            #           self._tn.open(self.address, self.port, self._timeout)
            return self._tn

    def disconnect(self, *args):
        if self._tn is not None:
            try:
                self._tn.execute_cmd("exit", None)
            except AttributeError:
                pass
            except socket.error:
                pass
            else:
                self._tn.close()
        else:
            pass

    async def execute_cmd(self, cmdObj):
        self._tn._readLock.acquire()
        if cmdObj.command is not None:
            cmdObj = await self.write(cmdObj)
        if cmdObj.expected is not None and cmdObj.cmd_result.result:
            cmdObj = await self.read(cmdObj)
        self._tn._readLock.release()
        return cmdObj.cmd_result

    async def read(self, cmdObj):
        if self._tn is None:
            logging.error(u'Telnet通道不存在')
            cmdObj.fail_to_excute('Telnet通道不存在')
        else:
            try:
                if isinstance(cmdObj.expected, (str)):
                    self._read_expect_str(cmdObj)
                elif isinstance(cmdObj.expected, (list)):
                    self._read_expect_list(cmdObj)
                else:
                    logging.error(u'Telnet read()的 参数需为字符串或者列表')
                    cmdObj.fail_to_excute('Telnet read()的 参数需为字符串或者列表')
                    self.matchIndex = -1
            except AttributeError as e1:
                logging.error(u'Telnet通道异常, err={0}'.format(e1))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e1))
            except socket.error as e2:
                logging.error(u'Telnet通道异常, err={0}'.format(e2))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e2))
            except EOFError as e3:
                logging.error(u'Telnet通道异常, err={0}'.format(e3))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e3))
        return cmdObj

    async def write(self, cmdObj):
        if self._tn is None:
            logging.error(u'Telnet通道不存在')
            cmdObj.fail_to_excute('Telnet通道不存在')
        else:
            try:
                self.send_cmd(cmdObj)
            except AttributeError as e1:
                logging.error(u'Telnet通道异常, err={0}'.format(e1))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e1))
            except socket.error as e2:
                logging.error(u'Telnet通道异常, err={0}'.format(e2))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e2))
            except EOFError as e3:
                logging.error(u'Telnet通道异常, err={0}'.format(e3))
                cmdObj.fail_to_excute('Telnet通道异常, err={0}'.format(e3))
        return cmdObj

    def send_cmd(self, cmdObj):
        self._tn.read_very_eager()
        self._tn.write('{0}\n'.format(cmdObj.command).encode('utf-8'))

    def disconnect_sepc(self, args):
        if self._tn is not None:
            try:
                self._tn.execute_cmd(args, None)
            except AttributeError:
                pass
            except socket.error:
                pass
            else:
                self._tn.close()
        else:
            pass

    async def clear_buff(self, buffSize=2048, timeout=0.3):
        sock = self._tn.get_socket()
        revData, _, _ = select.select([sock], [], [], timeout)
        if len(revData) > 0:
            sock.recv(buffSize)

    def _read_expect_str(self, cmdObj):
        tp = self._tn.expect([cmdObj.expected.encode()], cmdObj.timeout)
        if tp[0] == -1:
            logging.warning("get expected result {} fail ！！！".format(cmdObj.expected))
        return self._record_cmd_result(cmdObj, tp)

    def _read_expect_list(self, cmdObj):
        tp = self._tn.expect([expected.encode() for expected in cmdObj.expected], cmdObj.timeout)
        return self._record_cmd_result(cmdObj, tp)

    def _record_cmd_result(self, cmdObj, tp):
        if tp[0] == -1:
            cmdObj.fail_to_excute(tp[2].decode())
        else:
            cmdObj.success_to_excute(tp[2].decode())
        self.matchIndex = tp[0]
        return cmdObj


if __name__ == '__main__':

    i = 'sdfsdf'
    j = []
    tn = Telnet('222.2.2.2', '10023')
    tn.connect()
    l = ['DM->', '~End~']
    ll = 'DM->'
