# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import logging

from infrastructure.logger.logger import logger
from infrastructure.utils.ObjectRepository import ObjectRepository
from infrastructure.utils.Singleton import Singleton


@Singleton
class ContractResultRepo(ObjectRepository):

    def __init__(self):
        ObjectRepository.__init__(self)
        
    def add(self, key, obj):
        if self._has_key(key):
            logger.warning('key: "{}" is existed!'.format(key))
        self._objDict[key] = obj
        return True