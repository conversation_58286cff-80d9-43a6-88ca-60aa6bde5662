import importlib.util
import inspect
import os

ROUTES = 'Routes.py'
ROUTER = 'router'
separator = os.path.sep


# traverse_all_sub_routers 传参 rootRouter:APIRouter, 将rootPath下的所有叶子目录下的Routes.py中的router，添加到路由中
def traverse_all_sub_routers(rootRouter, rootPath):
    for curPath, dirs, files in os.walk(rootPath):
        if len(dirs) == 0 or dirs == ['__pycache__']:
            if ROUTES in files:
                modulePath = os.path.join(curPath, ROUTES)
                moduleName = os.path.splitext(ROUTES)[0]
                spec = importlib.util.spec_from_file_location(moduleName, modulePath)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                if hasattr(module, ROUTER):
                    subRouter = getattr(module, ROUTER)
                    prefix = separator + os.path.relpath(curPath, rootPath)
                    formatPrefix = prefix.replace("\\", "/")
                    tag = formatPrefix.split("/")[-1]
                    rootRouter.include_router(router=subRouter, prefix=formatPrefix, tags=[tag])


# get_prefix 传入根文件夹路径，建议用 os.path.dirname(os.path.abspath(__file__)) 获取，返回调用者的路径对 rootPath 的相对路径
# 如 /home/<USER>/f1/f2/f3 下的函数调用 get_relative_path("/home/<USER>") 返回 "/f1/f2/f3"
def get_relative_path(rootPath):
    currentFile = inspect.getfile(inspect.currentframe().f_back)
    currentPath = os.path.dirname(currentFile)
    return separator + os.path.relpath(currentPath, rootPath)
