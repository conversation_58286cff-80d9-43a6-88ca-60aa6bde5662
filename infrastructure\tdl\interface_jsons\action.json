{"execute": {"uri": "/tdl/pipeline/action/execute/third_party", "method": "POST", "bodyNeedDump": true, "header": {"Content-Type": "application/json"}, "body": "#body#"}, "get_execution_info": {"uri": "/tdl/pipeline/action/execution_info?pipelineUuid=%(pipelineUuid)s&pipelineStageId=%(pipelineStageId)s&recordId=%(recordId)s", "method": "GET", "bodyNeedDump": true, "header": {"Content-Type": "application/json"}, "body": {}}, "get_execution_logs": {"uri": "/tdl/pipeline/action/execution_logs?pipelineStageId=%(pipelineStageId)s&type=%(type)s&recordId=%(recordId)s", "method": "GET", "bodyNeedDump": true, "header": {"Content-Type": "application/json"}, "body": {}}}