import socket
import os
import tornado.locale
from infrastructure.const.common import EN_US, ZH_CN

VALIDATED_FAILED = "ValidatedFailed"


def get_hostname():
    return socket.gethostname()

def get_user_locale(lang):
    if lang == EN_US:
        return tornado.locale.get(EN_US)
    else:
        return tornado.locale.get(ZH_CN)

def get_project_root():
    return os.path.abspath("%s/../../.." % (__file__,))

def get_host():
    return socket.gethostbyname(get_hostname())