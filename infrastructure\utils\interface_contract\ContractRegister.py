# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import logging
from infrastructure.utils.Singleton import Singleton


@Singleton
class ContractRegister(object):

    '''
    classdocs
    '''

    def __init__(self):
        '''
        Constructor
        '''
        self._objDict = {}

    def register(self, domainObjAlias, toolType, toolObj):
        self.add(domainObjAlias + ':' + toolType, toolObj)

    def find(self, key):
        obj = self._objDict.get(key)
        if obj is None:
            raise Exception(key + ' :<PERSON><PERSON> has not register')
        return obj

    def _has_key(self, key):
        return self._objDict.has_key(key)

    def add(self, key, obj):
        if key:
            logging.warning('ContractRegister-key: "%s" is existed!', key)
        self._objDict[key] = obj
        return True

    def items(self):
        return self._objDict.itervalues()

    def values(self):
        return self._objDict.values()

    def keys(self):
        return self._objDict.keys()

    def update(self, key, obj):
        self._objDict.update({key: obj})
