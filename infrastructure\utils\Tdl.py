'''
Created on 2023年7月13日

@author: 10111815
'''
import json
import requests
from infrastructure.logger.logger import logger


class TdlApi(object):

    def __init__(self, name=None):
        self.tdl_url = "https://zxmte.zte.com.cn:3303"

    def get_workitem_para(self, workitem_id, para):
        url = "/rdc/workitem/{0}/en".format(workitem_id)
        url = self._format_url(url)
        response = requests.get(url, verify=False, timeout=10)
        if response.status_code != 200:
            logger.error(response.text)
            return None
        data_list = json.loads(response.text)
        if data_list:
            return data_list[0].get(para)
        return None

    def _send_rdc_cmd(self, method, url, body, timeout=60):
        url = self._format_url(url)
        try:
            response = requests.request(method, url, data=body, timeout=timeout, verify=False)
        except Exception as e:
            logger.error(e)
            return False, str(e)
        return self._parse_response(response)

    def _format_url(self, url):
        return self.tdl_url + url

    def _composite_header(self):
        header = {'Content-Type': "application/json"}
        return header

    def _parse_response(self, response):
        logger.info(response.text)
        if response.status_code != 200:
            logger.error(response.text)
            return False, response.text
        response_json = json.loads(response.text)
        if not response_json.get("result"):
            logger.error(response_json.get("failReason"))
            return False, response_json.get("failReason")
        return True, response_json.get("data")


if __name__ == "__main__":
    a = TdlApi()
    print (a.get_workitem_para("RAN-962839", "RAN_configurationparameter"))
