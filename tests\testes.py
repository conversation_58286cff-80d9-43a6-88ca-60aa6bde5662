from service.logging_service import LoggingService

aa = {'details': {'query_ume_alarm': {'alarmcode': [200201067], 'additionaltext': ['小区类型: NR感知小区，小区ID: 460-11-9143-1，失败类型：逻辑接口异常，失败原因：Ns断链，错误码: 5-500。Location：rack=1,shelf=1,board=1。'], 'reasonname': ['感知资源准备中'], 'codename': ['NR感知小区退服'], 'alarmraisedtime': ['2025-06-10T12:24:19'], 'position': ['NRSensingFunction=1,NRSensingCell=1'], 'aax_DiagnosisResultStatus': ['']}}}

# LoggingService.log_execution(
#     service_type="log_save",
#     operation="log_save",
#     status="failure",
#     details={"message": {}, "results": aa},
#     task_id="testES",
#     env_id="test1",
#     service_key="env_check"
# )


# LoggingService.log_execution(
#     service_type="log_save",
#     operation="log_save",
#     status="failure",
#     details={"message": {}, "results":{ 'query_ume_alarm': {}, 'query_me_connect': None}},
#     task_id="testES",
#     env_id="test",
#     service_key="env_check"
# )

e = "1111"
bbb = {"result": False, "details": {e}}
print(type(bbb.get("details")))