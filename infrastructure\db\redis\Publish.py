'''
Created on 2020年9月3日

@author: 10263601
'''
# -*- coding: utf-8 -*-
import json

'''
Created on 2018年12月17日

@author: 10111815

'''
import os
import sys
sys.path.append(os.path.abspath('%s/..' % sys.path[0]))
from config.RedisConfig import REDIS_DB
from infrastructure.db.redis.Redisdb import RedisClient
import logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")


class RedisService(object):

    def __init__(self):
        self._redis = RedisClient(REDIS_DB).get_instance()

    def publish(self, channel, data):
        self._redis.publish(channel, data)


if __name__ == '__main__':
    server = RedisService()
    message1 = {"_id": "634fa866dee01d33870876dd", "product": "5G"}
    server.publish("write_tfs_workitem", json.dumps(message1))

