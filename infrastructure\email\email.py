class Email():
    def __init__(self, subject: str, content: str, recipients: list[str], cc: list[str]):
        self._subject = subject
        self._content = content
        self._recipients = recipients
        self._cc = cc

    def send_email(self):
        from service.tools.EmailService import EmailService
        return EmailService.email_send(self)

    @property
    def subject(self):
        return self._subject

    @property
    def content(self):
        return self._content

    @property
    def recipients(self):
        return self._recipients

    @property
    def cc(self):
        return self._cc


if __name__=="__main__":
    Email.send_email()
