from domain.model.dto.env_info import JenkinsServerEnvInfo, PcEnvInfo, VersionEnvInfo
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from domain.repository.es_env import EsJenkinsServerEnv, EsPcEnv, EsVersionEnv
from domain.repository.es_jenkins_job import EsJenkinsJob
from domain.repository.version_execute import EsVersionExecute
from domain.model.dto.test_record import VersionExecuteInfo
from infrastructure.logger.logger import logger
from infrastructure.utils.json2 import marshal
from datetime import datetime
from domain.service.arifactory import ArtifactDao
from config.ArtifactConfig import *
import re
import logging
import ast


class JenkinsServerEnvService:
    def __init__(self):
        self._es = EsJenkinsServerEnv()

    def save_jenkins_server(self, document: JenkinsServerEnvInfo):
        return self._es.index(id=datetime.now().isoformat(), body=eval(marshal(document)))


class PcEnvService:
    def __init__(self):
        self._es = EsPcEnv()

    def save_pc_info(self, document: PcEnvInfo):
        return self._es.index(id=datetime.now().isoformat(), body=eval(marshal(document)))


class VersionEnvService:
    def __init__(self):
        self._es = EsVersionEnv()
        self._es_version_execute = EsVersionExecute()
        self._es_jenkins_job = EsJenkinsJob()
    def initialize_version_task_dict(self, version_list: str, last_success_version: str, curr_version: str,
                                     version_test_result: bool) -> dict:
        """
        初始化version_task_dict，设置last_success_version和curr_version的状态

        Args:
            version_list: 版本列表字符串
            last_success_version: 最后成功的版本
            curr_version: 当前版本

        Returns:
            dict: 初始化后的version_task_dict
        """
        try:
            # 将字符串转换为列表
            version_list = ast.literal_eval(version_list) if isinstance(version_list, str) else version_list

            # 初始化字典
            version_task_dict = {}
            for version in version_list:
                version_task_dict[version] = {
                    "status": "未执行",
                    "result": None
                }

            # 设置last_success_version的状态
            if last_success_version in version_task_dict:
                version_task_dict[last_success_version] = {
                    "status": "已完成",
                    "result": "success"
                }
            if version_test_result:
                if curr_version in version_task_dict:
                    version_task_dict[curr_version] = {
                        "status": "已完成",
                        "result": "success"
                    }
            else:
                # 设置curr_version的状态
                if curr_version in version_task_dict:
                    version_task_dict[curr_version] = {
                        "status": "已完成",
                        "result": "fail"
                    }

            return version_task_dict
        except Exception as e:
            logging.error(f"初始化version_task_dict失败: {str(e)}")
            raise

    def insert_initial_execute_records(self, task_id: str, env_id: str,
                                       last_success_version: str, curr_version: str,
                                       version_task_dict: dict, version_test_result: bool) -> tuple:
        """
        插入初始的执行记录：last_success_version的成功记录和curr_version的失败记录

        Args:
            task_id: 任务ID
            env_id: 环境ID
            last_success_version: 最后成功的版本
            curr_version: 当前版本
            version_task_dict: 版本任务字典

        Returns:
            tuple: (success_record_id, fail_record_id)
        """
        try:
            if version_test_result:
                test_result = "success"
            else:
                test_result = "fail"
            # 创建并保存last_success_version的成功记录
            success_info = VersionExecuteInfo(
                task_id=task_id,
                env_id=env_id,
                version=last_success_version,
                test_result="success",
                version_task_dict=str(version_task_dict)
            )
            success_result = self._es_version_execute.save_version_execute_task(success_info)
            success_record_id = success_result.get("_id")
            print("success_result", success_result)
            # 创建并保存curr_version的失败记录
            execute_info = VersionExecuteInfo(
                task_id=task_id,
                env_id=env_id,
                version=curr_version,
                test_result=test_result,
                version_task_dict=str(version_task_dict)
            )
            fail_result = self._es_version_execute.save_version_execute_task(execute_info)
            fail_record_id = fail_result.get("_id")
            print("fail_result", fail_result)
            logger.info(f"save_version_execute_task: {success_result}")
            return success_record_id, fail_record_id
        except Exception as e:
            logging.error(f"插入初始执行记录失败: {str(e)}")
            raise

    def get_last_success_version(self, full_path):
        base_match = re.search(r"(https://artsz\.zte\.com\.cn/artifactory)", full_path)
        if base_match:
            log_url = base_match.group(1)
        else:
            log_url = "https://artsz.zte.com.cn/artifactory"

        # 提取NFMerged后面的两个部分
        parts = [None, None]
        nfmerged_match = re.search(r"NFMerged/([^/]+)/([^/]+)", full_path)
        if nfmerged_match:
            parts[0] = nfmerged_match.group(1)  # release
            parts[1] = nfmerged_match.group(2)  # V4.20.20.20_Main

        base_url = "g5nrv3-alpha-generic/Daily-Version/" + parts[0]
        art = ArtifactDao(log_url)
        art.login(username, password)
        res = art.get_last_success_version(base_url, parts[1])
        return res

    def get_version_list(self, full_path, curr_version, last_success_version):
        base_match = re.search(r"(https://artsz\.zte\.com\.cn/artifactory)", full_path)
        if base_match:
            log_url = base_match.group(1)
        else:
            log_url = "https://artsz.zte.com.cn/artifactory"

        # 提取NFMerged后面的两个部分
        parts = [None, None]
        nfmerged_match = re.search(r"NFMerged/([^/]+)/([^/]+)", full_path)
        art = ArtifactDao(log_url)
        art.login(username, password)
        if nfmerged_match:
            parts[0] = nfmerged_match.group(1)  # release
            parts[1] = nfmerged_match.group(2)  # V4.20.20.20_Main
        if parts[0] == "master":
            base_url = "g5nrv3-snapshot-generic/aurora_test/NFMerged"
            version_list = art.get_all_branch_version_list(base_url, parts[0])
        else:
            base_url = "g5nrv3-snapshot-generic/aurora_test/NFMerged/" + parts[0]
            version_list = art.get_all_branch_version_list(base_url, parts[1])
        result = art.get_versions_between(version_list, last_success_version,
                                          curr_version)
        return result
    def query_task_is_exit(self,env_id,task_id):
        result = self._es_jenkins_job.query_exit_task(env_id,task_id)
        return len(result) > 1
    def save_version_env_info(self, document: JenkinsJobInfo):
        """
        保存版本环境信息，并初始化相关数据
        """
        full_path = document.full_path
        full_path_list = full_path.split("/")
        last_success_version = self.get_last_success_version(full_path)
        curr_version = full_path_list[-1].replace('.tar', '')
        version_list = []
        # 获取版本列表
        version_dict_list = self.get_version_list(full_path, curr_version, last_success_version)
        for version_dict in version_dict_list:
            for version, full_path in version_dict.items():
                version_list.append(version)
        # 创建环境信息对象
        env_info = VersionEnvInfo(
            task_id=document.task_id,
            full_path=full_path,
            env_id=document.env_id,
            last_success_version=curr_version if bool(eval(document.version_test_result)) else last_success_version,
            curr_version=curr_version,
            jenkins_job_name=document.jenkins_job_name,
            jenkins_build_number=document.jenkins_build_number,
            version_branch=document.version_branch,
            version_test_result=document.version_test_result,
            version_list=str(version_dict_list)
        )

        # 保存环境信息
        result = self._es.index(id=datetime.now().isoformat(), body=eval(marshal(env_info)))
        if self.query_task_is_exit(document.env_id,document.task_id):
            return result
        # 初始化version_task_dict
        version_task_dict = self.initialize_version_task_dict(
            str(version_list),
            last_success_version if not bool(eval(document.version_test_result)) else curr_version,
            curr_version,
            bool(eval(document.version_test_result))
        )
        logger.info(f"version_task_dict saved for job: {version_task_dict}")
        # 插入初始执行记录
        self.insert_initial_execute_records(
            document.task_id,
            document.env_id,
            last_success_version if not bool(eval(document.version_test_result)) else curr_version,
            curr_version,
            version_task_dict,
            bool(eval(document.version_test_result))
        )

        return result


if __name__ == "__main__":
    from infrastructure.Singleton import Singleton
    from infrastructure.utils.Env import Env
    from infrastructure.db.elastic_search.elastic_search import Document
    from elasticsearch import Elasticsearch
    from datetime import datetime
    from domain.model.dto.test_record import VersionExecuteInfo
    from infrastructure.utils.json2 import marshal

    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)
    document = JenkinsJobInfo(
        env_id="RAN3-上海高频CI团队-VAT1016",
        test_uc_tag="UC-CODE-XYZ",
        test_work_dir="/path/to/workdir",
        test_testcase_dir="/path/to/testcases",
        jenkins_log_name="smoke-1016-udp-tcp_1635.html",
        jenkins_job_name="smoke-1016-udp-tcp",
        jenkins_build_number="1635",
        service_type="jenkins_job_save",
        full_path="https://artsz.zte.com.cn/artifactory/g5nrv3-snapshot-generic/aurora_test/NFMerged/master/NR-V4.30.10.00B207-2/NR-V4.30.10.00B207-2_2504250015/01-gNB/litepass/PKG/NR-V4.30.10.00B207-2_2504250015.tar",
        version_test_result="True"
    )
    vs = VersionEnvService()
    vs.save_version_env_info(document)
