"""
Created on Feb 17, 2016

@author: 10154402
"""
import pymongo
from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.utils.Singleton import Singleton


@Singleton
class MongoDbHandler(object):

    def __init__(self, dbcfg):
        self._dbcfg = dbcfg
        self._session = None
        self._reconnection()

    def create_collection(self, collection):
        return self.__db.create_collection(collection)

    def rename_collection(self, oldname, newname):
        self.__db[oldname].rename(newname)

    def copy_collection(self, originalCollName, duplicateCollName):
        return self.__db[originalCollName].aggregate([{'$out': duplicateCollName}])

    def drop_collection(self, collection):
        return self.__db.drop_collection(collection)

    def set_collection(self, collection):
        if collection:
            self.__collection = self.__db[collection]

    def is_table_exist(self, collection):
        if collection in self.__db.collection_names():
            return True
        return False

    def _reconnection(self, collection=None):
        self.__db = self.__init_db()
        if collection:
            self.__collection = self.__db[collection]

    def __init_db(self):
        db = self.__connect_db()
        try:
            db.authenticate(self._dbcfg['user'], self._dbcfg['password'])
        except:
            db = self.__connect_db()
        return db

    def __connect_db(self):
        conn = pymongo.MongoClient(self._dbcfg['db_replset'])
        self._conn = conn
        return conn[self._dbcfg['name']]

    def aggregate(self, pipeline, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _aggregate(self, pipeline):
            cursor = self.__collection.aggregate(pipeline)
            result = []
            for contentDict in cursor:
                if "_id" in contentDict:
                    contentDict['_id'] = str(contentDict['_id'])
                result.append(contentDict)
            return result

        return _aggregate(self, pipeline)

    def query_all(self, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_all(self):
            cursor = self.__collection.find()
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result

        return _query_all(self)

    def query_with_constraint(self, condition, collection, constraint=None, skip=0, limit=0):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_with_constraint(self, condition, constraint, skip, limit):
            cursor = self.__collection.find(condition, constraint, skip, limit)
            result = []
            for contentDict in cursor:
                if "_id" in contentDict:
                    contentDict['_id'] = str(contentDict['_id'])
                result.append(contentDict)
            return result

        return _query_with_constraint(self, condition, constraint, skip, limit)

    def query(self, condition, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query(self, condition):
            cursor = self.__collection.find(condition)
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result

        return _query(self, condition)

    def count(self, condition, collection, constraint=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _count(self, condition, constraint):
            return self.__collection.find(condition, constraint).count()

        return _count(self, condition, constraint)

    def query_and_sort(self, condition, sort_key, direction=1, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query(self, condition):
            cursor = self.__collection.find(condition).sort(sort_key, direction)
            result = []
            for contentDict in cursor:
                if "_id" in contentDict:
                    contentDict['_id'] = str(contentDict['_id'])
                result.append(contentDict)
            return result

        return _query(self, condition)

    def query_without_id(self, condition, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_without_id(self, condition):
            return self.query_with_constraint(condition, collection, {'_id': 0})

        return _query_without_id(self, condition)

    def save(self, json_format_data, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _save(self, json_format_data):
            return self.__collection.insert(json_format_data)

        return _save(self, json_format_data)

    def delete(self, condition, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _delete(self, condition):
            return self.__collection.remove(condition)

        return _delete(self, condition)

    def clear_all(self, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _clear_all(self):
            self.__collection.remove()

        return _clear_all(self)

    def clear(self, condition, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _clear(self, condition):
            self.__collection.remove(condition)

        return _clear(self, condition)

    def update_attr(self, object_id, attr_dict, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update_attr(self, object_id, attr_dict):
            logger.debug('update object id is {0}, update attrDict is {1}'.format(object_id, attr_dict))
            self.update({'_id': object_id}, attr_dict)

        return _update_attr(self, object_id, attr_dict)

    def update(self, criteria, new_obj, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update(self, criteria, new_obj):
            return self.__collection.update(criteria, {'$set': new_obj})

        return _update(self, criteria, new_obj)

    def delete_by_key(self, criteria, key_path, collection):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _delete_by_key(self, criteria, key_path):
            self.__collection.update(criteria, {'$unset': {key_path: ''}}, True)

        return _delete_by_key(self, criteria, key_path)

    def start_session(self):
        session = self._conn.start_session()
        self._session = session
        return session

    def start_transaction(self):
        self._session.start_transaction()

    def abort_transaction(self):
        self._session.abort_transaction()

    def commit_transaction(self):
        self._session.commit_transaction()

    def end_session(self, session):
        self._session.end_session()
        self._session = None


if __name__ == '__main__':

    mgClient = pymongo.MongoClient("10.7.213.102:27017,10.7.213.98:27017,10.7.235.234:27017")
    db = mgClient.get_database("TDL")
    db.authenticate("tdl", "tdl")
    session = mgClient.start_session()
    a_collection = db["DSP1"]
    b_collection = db["DSP2"]
    session.start_transaction()
    try:
        a_collection.insert_one({"world": 2}, session=session)
        b_collection.insert_one({"hello": 2}, session=session)
        # raise Exception("aaaaaaaaaaaa")
    except:
        import traceback

        traceback.print_exc()
        print("111111111111111111")
        session.abort_transaction()
    else:
        print("2222222222222222")
        session.commit_transaction()
    finally:
        print("33333333333333333333")
        session.end_session()
