import os

from infrastructure.utils.FileHandler import FileHandler


def get_target_path(relativePath):
    targetPath = os.path.abspath(__file__)
    for step in relativePath.split("/"):
        if step == "..":
            targetPath = os.path.split(targetPath)[0]
        else:
            targetPath = os.path.join(targetPath, step)
    return targetPath


class PathHandler(object):

    @staticmethod
    def get_abs_path(pathBeginWith5GNR):
        return os.path.abspath(__file__).split("5GNR")[0] + pathBeginWith5GNR

    @staticmethod
    def clear_dir(*dirPaths):
        for dirPath in dirPaths:
            if not os.path.exists(dirPath):
                os.makedirs(dirPath)
            else:
                FileHandler.clear_path(dirPath)

    @staticmethod
    def create_dir(path):
        if os.path.exists(path):
            return
        os.mkdir(path)
