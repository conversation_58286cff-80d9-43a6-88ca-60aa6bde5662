from service.tools.UmeService import UmeService


def get_tar_path(msg_dict):
    # TODO 获取版本tarpath
    return None


class UmeVersionUpgrade:
    def __init__(self, service_msg_dict):
        self.service_msg_dict = service_msg_dict

    def run(self):
        env_id = self.service_msg_dict.get("env_id")
        tar_path = get_tar_path(self.service_msg_dict)
        UmeService.version_upgrade(env_id, tar_path)


class UmeVersionRollback:
    def __init__(self, service_msg_dict):
        self.service_msg_dict = service_msg_dict

    def run(self):
        env_id = self.service_msg_dict.get("env_id")
        tar_path = get_tar_path(self.service_msg_dict)
        UmeService.version_rollback(env_id, tar_path)
