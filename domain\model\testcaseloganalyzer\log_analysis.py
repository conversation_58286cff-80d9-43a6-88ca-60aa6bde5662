import time, re, copy, os, json
from pathlib import Path
from selenium import webdriver
from bs4 import BeautifulSoup
import urllib.parse
from infrastructure.utils.FileHandler import check_file_exist
import csv, shutil
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from infrastructure.logger.logger import logger
from selenium.webdriver.chrome.service import Service

request_header = r"file://"
max_seq_len = 4096

suite_cls_ele = "element-header"
suite_cls_ele_pop = "populated"
suite_child_meta = "metadata"
suite_cls_child = "children"
suite_cls = "suite"

test_cls_pop = "children"
test_cls_ele = "element-header"
test_cls_meta = "metadata"
test_cls = "test"

keyword_cls = "keyword"
keyword_cls_meta = "metadata"
keyword_cls_msg = "messages"
keyword_cls_ele = "element-header"
keyword_cls_ele_left = "element-header-left"
keyword_cls_pop = "children"

ele_header_cls_name = "name"
fail_cls = "fail"

error_html = "<h2>Test Execution Errors</h2>"
valid_name = ["error", "no", "not", "fail", "FAIL", "Arguments", "Traceback (most recent call last)",
              "请求超时"]


def set_driver_conf():
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('blink-settings=imagesEnabled=false')
    options.add_experimental_option('excludeSwitches', ['enable-logging'])
    options.add_argument("--disable-software-rasterizer")  # 降低崩溃风险
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--proxy-server="direct://"')
    options.add_argument('--proxy-bypass-list=*')
    options.add_argument('--disable-blink-features=AutomationControlled')
    service = Service(executable_path=get_chromedriver_path())
    return webdriver.Chrome(options=options, service=service)


def get_chromedriver_path():
    path = shutil.which("chromedriver")
    if not path:
        raise FileNotFoundError("chromedriver not found in PATH. Please install or specify manually.")
    return path


def loadHtml(url):
    driver = set_driver_conf()
    driver.get(url)
    time.sleep(2)
    # WebDriverWait(driver, 5).until(
    #     EC.presence_of_element_located((By.CLASS_NAME, keyword_cls_msg))  # 或者其他特定的元素
    # )
    html = driver.page_source
    driver.quit()
    return html


def get_html_raw_info(url: str, file_name: str):
    check_file_exist(url + file_name)
    name = urllib.parse.quote(file_name)
    file_path = request_header + url + name
    html_content = loadHtml(file_path)
    return html_content


def is_suites_keyword(suite):
    for ch in suite.find_all(recursive=False):
        if suite_cls_child in ch.get('class', []):
            return is_suite_children_has_keyword(ch)
    return False


def is_suite_children_has_keyword(suite_pop_element):
    for ch in suite_pop_element.find_all(recursive=False):
        if keyword_cls in ch.get('class', []):
            return True
    return False


def get_html_testcase(test_log_text):
    suite_test_names = []
    soup = BeautifulSoup(test_log_text, "html.parser")
    tests = soup.find_all('div', attrs={"class": "test"})
    for test in tests:
        test_name = test.find_all('span', attrs={"class": "name"}, limit=1)
        for name in test_name:
            suite_test_names.append(name.get_text())
    return suite_test_names


# all suites
def filter_relevant_info(test_log_text, fail_testcases=[]):
    # fail_testcases = []
    soup = BeautifulSoup(test_log_text, "html.parser")
    suites = soup.find_all('div', attrs={"class": "suite"})
    suites_parent_sibs = {}
    for suite in suites:
        # 遍历所有包含test的suites
        if is_suite_lowest_level(suite):
            process_suite(suite, fail_testcases)
        if is_suites_keyword(suite):
            for ch in suite.find_all(recursive=False):
                if suite_cls_ele in ch.get('class', []):
                    process_suite_element_header(ch)
                elif suite_cls_child in ch.get('class', []):
                    suite_error_key = process_suite_chidren_keyword(ch)
                    if suite_error_key:
                        suites_parent_sibs.update({suite.get("id", []): suite_error_key})
    for test_case in fail_testcases:
        for key, value in suites_parent_sibs.items():
            if key in test_case.get("suite_id"):
                test_case.update(value)


def process_suite_chidren_keyword(suite_children):
    keyword_info = {}
    for ch in suite_children.find_all(recursive=False):
        if keyword_info:
            break
        if suite_child_meta in ch.get('class', []):
            pass
        elif suite_cls in ch.get('class', []):
            pass
        elif keyword_cls in ch.get('class', []):
            fail_keywords = []
            fail_keywords_msg = []
            process_keyword(ch, fail_keywords, fail_keywords_msg)
            if fail_keywords_msg:
                fail_msg = " ".join(fail_keywords_msg)
                keyword_info.update({"detail_msg": fail_msg[:max_seq_len]})
            if fail_keywords:
                keyword_info.update({"fail_keywords": fail_keywords})
        elif test_cls in ch.get('class', []):
            pass
        else:
            raise Exception("suites struct error!")
    return keyword_info


def is_suite_lowest_level(suite_element):
    for ch in suite_element.find_all(recursive=False):
        if suite_cls_child in ch.get('class', []):
            return is_suite_chidren_has_test(ch)
    return False


def is_suite_chidren_has_test(suite_pop_element):
    for ch in suite_pop_element.find_all(recursive=False):
        if test_cls in ch.get('class', []):
            return True
        # if keyword_cls in ch.get('class', []):
        #     return True
    return False


def process_suite(suite, fail_testcases=[]):
    for ch in suite.find_all(recursive=False):
        if suite_cls_ele in ch.get('class', []):
            process_suite_element_header(ch)
        elif suite_cls_child in ch.get('class', []):
            process_suite_children(ch, suite.get("id", ""), fail_testcases)
        else:
            raise Exception("suite struct error!")


def process_suite_children(suite_children, id: str, fail_testcases=[]):
    i = 0
    keyword_info = {}
    first_read_flag = False
    for ch in suite_children.find_all(recursive=False):
        if suite_child_meta in ch.get('class', []):
            pass
        elif suite_cls in ch.get('class', []):
            fail_testcases.append(process_suite(ch, fail_testcases))
        elif keyword_cls in ch.get('class', []):
            pass
        elif test_cls in ch.get('class', []):
            # print(f'--------{i}----------')
            case_fail_info = process_test(ch)
            if case_fail_info:
                case_fail_info.update({"suite_id": id})
                fail_testcases.append(case_fail_info)
                # json_data = json.dumps(case_fail_info, ensure_ascii=False, indent=4)  # `indent=4` 使输出格式化为更易读的方式
                # print(json_data)
            # print(f'--------{i}----------')
            i += 1
        else:
            raise Exception("suites struct error!")


def process_suite_element_header(element_header):
    pass


def process_test(case_or_keyword):
    case_fail_info = {}
    for ch in case_or_keyword.find_all(recursive=False):
        if test_cls_ele in ch.get('class', []):
            title = process_test_element_header_children(ch)
            if title:
                case_fail_info.update({"testcase_name": title})
        elif test_cls_pop in ch.get('class', []):
            testcase_msg = process_testcase_children(ch)
            if testcase_msg:
                case_fail_info.update(testcase_msg)
        else:
            raise Exception("test struct error!")
    return case_fail_info


def process_testcase_children(testcase_child):
    testcase_info = {}
    for ch in testcase_child.find_all(recursive=False):
        if test_cls_meta in ch.get('class', []):
            if "FAIL" in ch.get_text():
                testcase_info.update(process_table_meta(ch.get_text()))
        elif keyword_cls in ch.get('class', []):
            fail_keywords = []
            fail_keywords_msg = []
            process_keyword(ch, fail_keywords, fail_keywords_msg)
            if fail_keywords_msg:
                fail_msg = " ".join(fail_keywords_msg)
                testcase_info.update({"detail_msg": fail_msg[:max_seq_len]})
            if fail_keywords:
                testcase_info.update({"fail_keywords": fail_keywords})
        else:
            raise Exception("testcase children struct error!")
    return testcase_info


def process_test_element_header_children(element_header):
    test_status = "pass"
    for ch in element_header.find_all(recursive=False):
        if keyword_cls_ele_left in ch.get('class', []):
            # 这块需要思考
            if ch.find_all(attrs={"class": "fail"}):
                return ch.get("title", "")
        if fail_cls in ch.get('class', []):
            test_status = fail_cls
        if ele_header_cls_name in ch.get('class', []) and test_status in fail_cls:
            pass
            # print(ch.get_text())
    if fail_cls in test_status:
        return element_header.get('title', [])


def process_keyword(keywords_html, fail_keywords=[], fail_keywords_msg=[]):
    for ch in keywords_html.find_all(recursive=False):
        if keyword_cls_pop in ch.get('class', []):
            process_keyword_children(ch, fail_keywords, fail_keywords_msg)
        elif keyword_cls_ele in ch.get('class', []):
            key_title = process_keyword_element_header_children(ch)
            if key_title:
                fail_keywords.append(key_title)
        else:
            raise Exception("keyword struct error!")


def process_keyword_element_header_children(element_header):
    keyword_status = "pass"
    for ch in element_header.find_all(recursive=False):
        if keyword_cls_ele_left in ch.get('class', []):
            # 这块需要思考
            if ch.find_all(attrs={"class": "fail"}):
                cleaned_text = re.sub(r"\s*\d{2}:\d{2}:\d{2}\.\d{3}\s*", "", ch.get_text())
                # 如果要去除 'KEYWORD'，可以进一步去除
                cleaned_text = re.sub(r"KEYWORD\s+", "", cleaned_text)
                # print(f"caseName:{cleaned_text}")
                return cleaned_text
        elif fail_cls in ch.get('class', []):
            keyword_status = fail_cls
        elif ele_header_cls_name in ch.get('class', []) and keyword_status in fail_cls:
            return ch.get_text()
            # print(ch.get_text())


def process_keyword_children(keyword_children_html, fail_keywords=[], fail_keywords_msg=[]):
    keyword_msg_infos = []
    for ch in keyword_children_html.find_all(recursive=False):
        if keyword_cls_meta in ch.get('class', []):
            if "FAIL" in ch.get_text():
                print(ch.get_text())
        elif keyword_cls in ch.get('class', []):
            process_keyword(ch, fail_keywords, fail_keywords_msg)
        elif keyword_cls_msg in ch.get('class', []):
            for data in valid_name:
                if data in ch.get_text() and "<html>" not in ch.get_text() and \
                        "Traceback (most recent call last):\n  None" not in ch.get_text():
                    item = process_table_content(ch)
                    msg_item = copy.deepcopy(item[-1])
                    if "Arguments" in ch.get_text() and len(msg_item) > 100:
                        continue
                    if "Response" in ch.get_text() and len(msg_item) > 200:
                        continue
                    if msg_item not in keyword_msg_infos:
                        keyword_msg_infos.append(msg_item)
        else:
            raise Exception("keyword children struct error!")
    if keyword_msg_infos:
        fail_keywords_msg.append(" ".join(keyword_msg_infos))


def process_table_meta(meta_text):
    status_pattern = r'Status:\s*(\w+\s*\(\w+\))'
    message_pattern = r'Message:\s*(.*)'

    status_match = re.search(status_pattern, meta_text)
    message_match = re.search(message_pattern, meta_text, re.DOTALL)

    if status_match and message_match:
        status = status_match.group(1).strip()
        message = message_match.group(1).strip()
        result = {
            'status': status,
            'message': message
        }
    else:
        result = {'status': None, 'message': None}
    return result


def process_table_content(table_content):
    rows = table_content.find_all('tr')
    content_list = []
    for row in rows:
        columns = row.find_all(['th', 'td'])
        for col in columns:
            if col.get_text(strip=True):
                content_list.append(col.get_text())
    return content_list


def process_error_format_html(file_path):
    raw_data = [""]
    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()
        raw_data = html_content.split("</script>")
        # print(len(raw_data[-1]))
    if error_html in raw_data[-1]:
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write("</script>".join(raw_data[:-1]))
            file.write("\n</script>\n\n</body>\n</html>")


def write_csv(data: list):
    fieldnames = data[0].keys()
    with open('/home/<USER>/Desktop/20250218/testcase_fail.csv', mode='w',
              newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    print("字典数据已写入output_dict.csv")
