# coding=utf-8
import matplotlib
import os

matplotlib.use('Agg')
import sys
import socket
import logging
from optparse import OptionParser
from logging.handlers import RotatingFileHandler
from infrastructure.utils.common import make_dir
from elasticsearch import Elasticsearch
import redis
from infrastructure.db.elastic_search.elastic_search import Document
from infrastructure.db.redis.Redisdb import RedisDB
from infrastructure.db.redis.CiAgentsQueue import CiAgentsQueue
from infrastructure.utils.Env import Env


def parse_args():
    useage = "python -m ci_agents [-e esIp] [-r redisIp] [-m mailIp]"
    parser = OptionParser(useage)
    parser.add_option('-e', action='store', type='string', dest='esIp')
    parser.add_option('-r', action='store', type='string', dest='redisIp')
    parser.add_option('-m', action='store', type='string', dest='mailIp')
    option, _ = parser.parse_args(sys.argv)
    return option.esIp, option.redisIp, option.mailIp


def init_logging():
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s[line:%(lineno)d] %(levelname)s %(message)s'
                        )
    formatter = logging.Formatter('%(asctime)s[line:%(lineno)d] %(levelname)s %(message)s')
    make_dir('logs')
    Rthandler = RotatingFileHandler('logs/ci_agents_%s.log' % socket.gethostname(), maxBytes=15 * 1024 * 1024,
                                    backupCount=15)
    Rthandler.setLevel(logging.INFO)
    Rthandler.setFormatter(formatter)
    logging.getLogger('').addHandler(Rthandler)
    logging.captureWarnings(True)


class CiAgentsTask(object):
    def __init__(self, esIp=None, redisIp=None, scheme=None):
        self.scheme = scheme or Env.get_config().ES.scheme
        self.esIp = esIp or Env.get_config().ES.ip
        self.redisIp = redisIp or Env.get_config().REDIS.ip
        Document.esConn = Elasticsearch(hosts=[{'host': self.esIp, 'port': Env.get_config().ES.port}], maxsize=1000,
                                        http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                        sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                        max_retries=2, timeout=30, sniff_timeout=60)
        RedisDB.redisConn = redis.Redis(
            connection_pool=redis.ConnectionPool(host=self.redisIp, port=Env.get_config().REDIS.port, password=Env.get_config().REDIS.password))

    async def run(self):
        await CiAgentsQueue().listen()
