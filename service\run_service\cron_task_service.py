from service.run_service.service_factory import ServiceFactory
from elasticsearch import Elasticsearch
from infrastructure.db.elastic_search.elastic_search import Document
from infrastructure.db.redis.Redisdb import RedisDB
import redis
from domain.model.dto.corn_task import *
from domain.repository.cron_task import EsCronTask


class CronTaskService():
    def __init__(self):
        self._es = EsCronTask()

    def get_running_task(self):
        task_list = self._es.query_by_filter_or_without_sort({"state": ["running", "NOTFOUND"]})
        return task_list[1]

    def handle_running_task(self):
        task_list = self._es.query_by_filter_or_without_sort({"state": ["running", "NOTFOUND"]})
        for task in task_list[1]:
            service_type = task['service_type']
            subtype = task['subtype']
            state = task['state']
            needed = task['needed']
            task_id = task['task_id']
            env_id = task['env_id']
            cron_task_id = task['cron_task_id']
            # 创建服务实例
            service = ServiceFactory.create_service(service_type, subtype, state, needed, task_id, env_id, self._es,
                                                    cron_task_id)

            # 将服务的执行任务添加到任务列表
            service.execute()

        # 异步执行所有任务

    def save_local_test_task(self, job_info, service_msg_dict, log_path, log_name):
        task_id = service_msg_dict.get("task_id")
        last_success_version = job_info.get("full_path").split('/')[-1]
        needed = Needed(
            tar_path=last_success_version,
            log_path=log_path,
            log_name=log_name,
            job_name=service_msg_dict.get("job_name"),
            build_number=service_msg_dict.get("build_number")
        )
        corn_task = CornTask(
            task_id=task_id,
            env_id=service_msg_dict.get("env_id"),
            needed=needed,
            service_type="local_test",
            state="running",
            subtype=service_msg_dict.get("subtype")
        )
        self._es.save_corn_task(corn_task)


if __name__ == "__main__":
    from infrastructure.utils.Env import Env

    Document.esConn = Elasticsearch(hosts=[{'host': Env.get_config().ES.ip, 'port': Env.get_config().ES.port}],
                                    maxsize=1000,
                                    http_auth=(Env.get_config().ES.username, Env.get_config().ES.password),
                                    sniff_on_start=False, sniff_on_connection_fail=False, retry_on_timeout=True,
                                    max_retries=2, timeout=30, sniff_timeout=60)
    RedisDB.redisConn = redis.Redis(
        connection_pool=redis.ConnectionPool(host=Env.get_config().REDIS.ip, port=Env.get_config().REDIS.port,
                                             password=Env.get_config().REDIS.password))

    Cr = CronTaskService()
    task_list = Cr.get_running_task()
    print(task_list)
    # Cr.handle_running_task(task_list)
