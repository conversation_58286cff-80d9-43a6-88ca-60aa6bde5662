# -*- encoding: utf-8 -*-
'''
@File    :   CustomList.py
@Time    :   2024/01/13 18:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import threading

from infrastructure.utils.ftp.FtpServer import FtpServer
from infrastructure.utils.Singleton import Singleton
from infrastructure.logger.logger import logger
from GlobalVars import status

@Singleton
class McpFtpServer:

    def __init__(self, ip, homeDir, port=20021):
        self.user = set()
        self.ip = ip
        self.port = port
        self.homeDir = homeDir
        self._ftp = None
        self._lock = threading.Lock()

    def start(self):
        with self._lock:
            self.user_change("add")
            if self._ftp:
                logger.warning(f"在{self.ip}:{self.port}上FTP服务已启动，无需重复启动，当前用户信息:{self.user}")
                return
            self._ftp = FtpServer()
            self._ftp.start(self.ip, self.port, self.homeDir)

    def stop(self):
        with self._lock:
            self.user_change("del")
            if not self.user:
                self._ftp.stop_ftp_server()
                self._ftp = None

    def user_change(self, operate="add"):
        logger.info(f"开始修改user信息操作:'{operate}',当前用户信息:{self.user}")
        if operate == "add":
            self.user.add(status().pipelineStageId)
        else:
            self.user.discard(status().pipelineStageId)
        logger.info(f"结束修改user信息操作:'{operate}',当前用户信息:{self.user}")

