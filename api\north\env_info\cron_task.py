from fastapi import <PERSON><PERSON><PERSON><PERSON>, Query, <PERSON>er, <PERSON>, Body
from typing import Dict, List

from service.cron_task_service import CronTaskService

router = APIRouter(prefix="/env_info/cron_task")
service = CronTaskService()

@router.get("/")
async def get_all_cron_tasks(
    from_id: int = Query(0, description="Starting index for pagination"),
    size: int = Query(50, description="Number of items to return"),
    x_operator: str = Header(None, description="User performing the operation")
):
    """Get all cron tasks"""
    return service.get_all(from_id=from_id, size=size)

@router.get("/{task_id}")
async def get_cron_task_by_id(
    task_id: str,
    x_operator: str = Header(None, description="User performing the operation")
):
    """Get cron task by ID"""
    return service.get_by_id(task_id)

@router.put("/{task_id}/state")
async def update_cron_task_state(
    task_id: str = Path(..., description="Task ID to update"),
    new_state: str = Body(..., embed=True, description="New state for the task"),
    x_operator: str = Header(..., description="User performing the operation")
):
    """Update the state of a cron task"""
    return service.update_state(task_id, new_state, x_operator)

@router.delete("/{task_id}")
async def delete_cron_task(
    task_id: str = Path(..., description="Task ID to delete"),
    x_operator: str = Header(..., description="User performing the operation")
):
    """Delete a cron task"""
    return service.delete(task_id, x_operator)
