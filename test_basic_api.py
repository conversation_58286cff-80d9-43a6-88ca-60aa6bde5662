#!/usr/bin/env python3
"""
Basic test for Jenkins job aggregation API without task_id grouping
"""

import requests
import json
from datetime import datetime, timedelta


def test_basic_aggregation():
    """Test basic aggregation without task_id grouping"""
    
    base_url = "http://localhost:8000"
    api_endpoint = f"{base_url}/jenkins/jobs/aggregate"
    
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    
    test_params = {
        "start_date": week_ago.strftime("%Y-%m-%d"),
        "end_date": today.strftime("%Y-%m-%d")
    }
    
    print("=" * 50)
    print("Testing Basic Jenkins Job Aggregation API")
    print("=" * 50)
    print(f"Test URL: {api_endpoint}")
    print(f"Test params: {test_params}")
    print()
    
    try:
        print("Sending basic API request...")
        response = requests.get(api_endpoint, params=test_params, timeout=30)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Basic API request successful!")
            print()
            
            if result.get("success"):
                data = result.get("data", {})
                print("📊 Basic aggregation summary:")
                print(f"  Total jobs: {data.get('total_jobs', 0)}")
                print(f"  Unique tasks: {data.get('unique_task_count', 0)}")
                print(f"  Total manual hours: {data.get('total_manual_hours', 0)}")
                
                time_range = data.get('time_range', {})
                print(f"  Time range: {time_range.get('start_date')} to {time_range.get('end_date')}")
                
                return True
            else:
                print(f"❌ API returned failure: {result.get('msg', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP request failed: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False


def test_env_grouping():
    """Test environment grouping"""
    
    base_url = "http://localhost:8000"
    api_endpoint = f"{base_url}/jenkins/jobs/aggregate"
    
    today = datetime.now()
    week_ago = today - timedelta(days=7)
    
    test_params = {
        "start_date": week_ago.strftime("%Y-%m-%d"),
        "end_date": today.strftime("%Y-%m-%d"),
        "group_by_env": "true"
    }
    
    print("\n" + "=" * 50)
    print("Testing Environment Grouping")
    print("=" * 50)
    
    try:
        print("Sending environment grouping request...")
        response = requests.get(api_endpoint, params=test_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                data = result.get("data", {})
                print("✅ Environment grouping successful!")
                print(f"  Total jobs: {data.get('total_jobs', 0)}")
                
                env_groups = data.get('env_groups', [])
                if env_groups:
                    print(f"  Environment groups: {len(env_groups)}")
                    for i, env_group in enumerate(env_groups[:3]):  # Show first 3
                        print(f"    {i+1}. {env_group.get('env_id')}: {env_group.get('total_jobs')} jobs")
                else:
                    print("  No environment groups found")
                
                return True
            else:
                print(f"❌ Environment grouping failed: {result.get('msg')}")
                return False
        else:
            print(f"❌ Environment grouping HTTP failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Environment grouping error: {str(e)}")
        return False


if __name__ == "__main__":
    print("Starting Jenkins job aggregation API tests...")
    
    basic_success = test_basic_aggregation()
    env_success = test_env_grouping()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Basic aggregation: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Environment grouping: {'✅ PASS' if env_success else '❌ FAIL'}")
    
    if basic_success and env_success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Some tests failed")
    
    print("=" * 50)
