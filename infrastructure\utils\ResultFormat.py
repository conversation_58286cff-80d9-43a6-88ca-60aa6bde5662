CODE = "code"
MSG = "msg"



class ResultFormat(object):

    def __init__(self):
        self._failReason = ''
        self._result = True
        self._data = None

    def update_return_info(self, updateData):
        self._data = updateData

    @property
    def fail_reason(self):
        return self._failReason

    @fail_reason.setter
    def fail_reason(self, failReason):
        self._failReason = failReason

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, data):
        self._data = data

    @property
    def result(self):
        return self._result

    @result.setter
    def result(self, result):
        self._result = result

    def format(self):
        return {'failReason': self._failReason,
                'result': self._result,
                'data': self._data}


