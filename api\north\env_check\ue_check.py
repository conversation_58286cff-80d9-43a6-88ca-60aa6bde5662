from service.tools.ue_state_service import *
from fastapi import APIRouter, Body
import warnings
warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)

router = APIRouter(prefix="/ue")


@router.post("/ue_ipconfig_check")
async def check_net_state(data: dict = Body(...)):
    return ue_state_service.query_ue_ipconfig(data)


@router.post("/ue_com_check")
async def check_ue_com(data: dict = Body(...)):
    return ue_state_service.query_ue_com(data)
