#!/bin/bash
get_image_name(){
    for file in *.tar; do
        echo "$file"
    done
}
    
    
deploy(){
    #获取镜像名称
    image_name=$(get_image_name)
    echo "image_name: $image_name"

    #截掉文件后缀
    image_name_without_suffix=${image_name%.*}

    #容器名
    container_name=$(echo $image_name_without_suffix | sed 's/:/-/g')

    #停止并删除容器
    docker stop $container_name
    docker rm $container_name
    docker ps -a | grep $container_name

    #删除并加载镜像
    docker rmi -f $image_name_without_suffix || echo "ignore"
    docker images -a | grep $image_name_without_suffix
    docker load -i $image_name
    docker images -a | grep $image_name_without_suffix

    #启动容器
    docker run -d -v /etc/localtime:/etc/localtime -p 8848:8848 -e server_env=$1 --name $container_name $image_name_without_suffix
} 

deploy $1
    