"""
@File: ActionShutdownUtil.py
@Author: 许王禾子10333721
@Time: 2023/8/31 下午4:02
@License: Copyright 2022-2030
@Desc: None
"""
import random
import time
import threading

from infrastructure.db.redis.RedisPool import redisClient
from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.utils.Singleton import Singleton
from infrastructure.utils.BloomFilter import Bloom
from infrastructure.logger.logger import logger

REDIS_STATUS_PREFIX = "biz_core:status:"
ACTIVE = '1'
COMPLETED = '0'
STOPPED = '-1'
EXPIRE_TIME = 5


def check_executed(execId: str) -> bool:
    """
    检查 exec_id 的是否已执行过
    return
        true: 没执行过
        false: 执行过
    """
    if not execId:  # 适配旧业务，没有execId的情况
        return True

    execFilter = ExecFilter()
    return execFilter.check_and_add(execId)


def is_active(identifier: str) -> bool:
    redisKey = REDIS_STATUS_PREFIX + identifier
    try:
        status = redisClient().get(redisKey)
        if status and status == ACTIVE:
            return True
    except Exception as err:
        logger.error(f"get [{identifier}]'s status failed, err: {err}")
        return True
    return False


def is_completed(identifier: str) -> bool:
    redisKey = REDIS_STATUS_PREFIX + identifier
    try:
        status = redisClient().get(redisKey)
        if status and status == COMPLETED:
            return True
    except Exception as err:
        logger.error(f"get [{identifier}]'s status failed, err: {err}")
    return False


def activate(identifier: str):
    redisKey = REDIS_STATUS_PREFIX + identifier

    @retries_on_exception(maxTries=3, everyTryDelaySecs=5)
    def _activate():
        redisClient().set(redisKey, ACTIVE)

    try:
        _activate()
    except Exception as err:
        logger.error(f"set [{identifier}]'s status active failed, err: {err}")


def complete(identifier: str):
    redisKey = REDIS_STATUS_PREFIX + identifier

    @retries_on_exception(maxTries=3, everyTryDelaySecs=5)
    def _complete():
        redisClient().set(redisKey, COMPLETED, ex=EXPIRE_TIME)

    try:
        _complete()
    except Exception as err:
        logger.error(f"set [{identifier}]'s status completed failed, err: {err}")


def clear(identifier: str):
    redisKey = REDIS_STATUS_PREFIX + identifier

    @retries_on_exception(maxTries=3, everyTryDelaySecs=5)
    def _clear():
        redisClient().delete(redisKey)

    try:
        _clear()
    except Exception as err:
        logger.error(f"clear [{identifier}]'s status failed, err: {err}")


@Singleton
class ExecFilter:
    def __init__(self):
        self._reset_time = 5 * 60  # 每5分钟重置
        self._capacity = 100000  # 容量100000
        self._bloom_filter = Bloom(capacity=self._capacity)  # 布隆过滤器
        self._set = set()  # 集合，用于二次检测
        self._start_timer()

    def check_and_add(self, exec_id: str) -> bool:
        if self._bloom_filter.check(exec_id):
            if exec_id in self._set:
                return False
        self._bloom_filter.add(exec_id)
        self._set.add(exec_id)
        return True

    def _start_timer(self):
        """启动一个定时器，定时重置过滤器"""
        self._timer = threading.Timer(self._reset_time, self._reset_and_restart_timer)
        self._timer.daemon = True
        self._timer.start()

    def _reset_and_restart_timer(self):
        """重置过滤器并重新启动定时器"""
        self._reset_filter()
        self._start_timer()

    def _reset_filter(self):
        """重置过滤器"""
        self._bloom_filter = Bloom(capacity=self._capacity)
        self._set.clear()


if __name__ == '__main__':
    execFilter = ExecFilter()
    startTime = time.time() * 1000
    for i in range(100000):
        # id = random.randint(0, 100000)
        execFilter.check_and_add(str(id))

    costTime = time.time() * 1000 - startTime
    print(f"cost time: {costTime} ms")
