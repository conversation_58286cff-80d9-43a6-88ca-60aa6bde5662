import requests
import json


class TdlApi(object):
    def __init__(self):
        self.url = "https://zxmte.zte.com.cn:3303/tdl/pipeline"

    def query_tdl_env_config(self, env_id):
        response = requests.request('GET', self.url + f"/env/historical_config?envId={env_id}", data={}, verify=False,
                                    timeout=180)
        if response.status_code == 200:
            response_json = json.loads(response.text)
            return response_json
        return {}

    def version_task(self, req_map):
        response = requests.request('POST', self.url + "/action/execute/third_party", json=req_map, verify=False,
                                    timeout=180)
        if response.status_code == 200:
            response_json = json.loads(response.text)
            return response_json
        return {}

    def task_status(self, req_map):
        response = requests.request('POST', self.url + "/action/execute/third_party", json=req_map, verify=False,
                                    timeout=180)
        if response.status_code == 200:
            response_json = json.loads(response.text)
            return response_json["result"]
        return False
