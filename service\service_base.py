class ServiceBase:
    """
    Base class for all service classes.
    Provides common functionality for service classes.
    """

    @staticmethod
    def get_service_key_by_type(service_type):
        """
        Get the service key from AGENTS_SERVICE_OBJECT by service type.
        
        Args:
            service_type (str): The service type to look up
            
        Returns:
            str: The service key if found, otherwise None
        """
        from service.agents_service_factory import AGENTS_SERVICE_OBJECT
        for key, service_class in AGENTS_SERVICE_OBJECT.items():
            if service_class and service_class.__name__ == service_type:
                return key
        return None
    
    @classmethod
    def set_service_attributes(cls, instance, service_msg_dict):
        """
        Set common service attributes on an instance.
        
        Args:
            instance: The service instance
            service_msg_dict (dict): The service message dictionary
        """
        # Set service type for logging
        instance.service_type = instance.__class__.__name__
        
        # Set service_key for logging (from AGENTS_SERVICE_OBJECT)
        instance.service_key = cls.get_service_key_by_type(instance.service_type)
        if not instance.service_key and 'service_type' in service_msg_dict:
            instance.service_key = service_msg_dict['service_type']
        
        # Set task_id and env_id for logging if available
        instance.task_id = service_msg_dict.get('task_id')
        instance.env_id = service_msg_dict.get('env_id')
