# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import logging
import time
import traceback

from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_exception
from infrastructure.utils.SetAttrDecorate import set_dict_to_obj_attr
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister


@set_dict_to_obj_attr
class FuncExcutor(object):
    '''
    classdocs
    '''

    def __init__(self, funcExcutorDict):
        self.excuteTool = ContractRegister().find(funcExcutorDict.get('tool'))

    def excute(self, **kwargs):
        cmdResult = self._excute_when_default()
        time.sleep(self.waitSecs)
        return cmdResult

    def _excute_when_default(self):
        @retries_on_exception(self.failedRetried, time.sleep, self.waitSecs)
        def _excute():
            return getattr(self.excuteTool, self.func)(*self.funcParas)
        cmdResult = None
        try:
            logger.debug("contract excute func: {0}".format(self.func))
            logger.debug("contract excute func paras: ")
            logger.debug(self.funcParas)
            cmdResult = _excute()
        except Exception:
            logging.warning(traceback.print_exc())
            if not self.isFailedContinue:
                raise
        return cmdResult
