from fastapi import APIRouter
from domain.model.dto.logdto import LogJobInfo
from infrastructure.utils.ResultFormat import ResultFormat
from service.testcase_log_analysis import LogAnalysisService
from infrastructure.logger.logger import logger
from service.testloganalysis.testcase_log_task_service import TestcaseLogAnalysisService
from service.testloganalysis.testcaselogDto import LogQueryInfo, ManualConfigInfo

log_analysis_router = APIRouter()


@log_analysis_router.post("/biz/ci/v1/log/analysis")
async def analysis_fail_log(log_info: LogJobInfo):
    rf = ResultFormat()
    try:
        rf.update_return_info(await LogAnalysisService(log_info.dict()).run())
    except Exception as e:
        logger.error(f'analysis api error:{e}')
        rf.result, rf.fail_reason = False, f'{e}'
    return rf.format()


@log_analysis_router.post("/biz/ci/v1/testcase/log/analysis/query")
async def query_ci_testcase_log_info(log_info: LogQueryInfo):
    rf = ResultFormat()
    try:
        rf.update_return_info(TestcaseLogAnalysisService().query_testcase_log(log_info))
    except Exception as e:
        logger.error(f'log analysis query api error:{e}')
        rf.result, rf.fail_reason = False, f'{e}'
    return rf.format()


@log_analysis_router.post("/biz/ci/v1/testcase/log/analysis/manual")
async def manual_config_info(manual_info: ManualConfigInfo):
    rf = ResultFormat()
    try:
        rf.update_return_info(TestcaseLogAnalysisService().manual_testcase_analysis_result(manual_info))
    except Exception as e:
        logger.error(f'manual_config_info query api error:{e}')
        rf.result, rf.fail_reason = False, f'{e}'
    return rf.format()
