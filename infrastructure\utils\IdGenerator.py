#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:41
# <AUTHOR> 10263601
import base64
import hashlib
import time
import socket
import os
import struct
import random

from pydantic import BaseModel

from infrastructure.utils.IpHandler import get_host_ip_address


def generate_action_id(method: str, path: str):
    route = method + path
    md5 = hashlib.md5()
    md5.update(route.encode('utf-8'))
    return md5.hexdigest()


def generate_unique_id():
    # 获取当前时间戳（单位：毫秒）
    timestamp = int(time.time() * 1000)

    # 获取本机的IP地址和进程ID, 拿的可能是虚拟网卡的ip
    ip_address = get_host_ip_address()
    process_id = os.getpid()

    # 将IP地址和进程ID进行哈希，得到一个机器ID
    machine_id = hash('{}{}'.format(ip_address, process_id)) % 2 ** 10

    # 生成一个序列号
    sequence = random.randint(0, 2 ** 12 - 1)

    # 将时间戳、机器ID和序列号等信息组合在一起生成唯一ID
    unique_id = ((timestamp << 22) | (machine_id << 12) | sequence)

    # 将唯一ID转换为字节串
    unique_id_bytes = struct.pack('>Q', unique_id)
    unique_id_hex = unique_id_bytes.hex()
    return unique_id_hex


def generate_device_id(data: BaseModel) -> str:
    dataInfo = str(data.dict()).encode('utf-8')
    return hashlib.sha256(dataInfo).hexdigest()[:10]


if __name__ == '__main__':
    print(generate_unique_id())
