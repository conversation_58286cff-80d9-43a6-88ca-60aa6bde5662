#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/6/1 11:34
# <AUTHOR> 10263601
import asyncio

import httpx

# from infrastructure.utils.Repeat import retries_on_exception
from urllib.parse import urlencode


class PublicRepository:

    def __init__(self, collection):
        self.uri = "https://zxmte.zte.com.cn:3303/tdl/common"
        self.collection = collection

    async def query(self, condition):
        conditionStr = urlencode(condition)
        url = f"{self.uri}?dbName={self.collection}&{conditionStr}"
        async with httpx.AsyncClient(timeout=5, proxies={}) as client:
            response = await client.get(url, timeout=5)
            return response.json()

    async def update(self, data):
        url = f"{self.uri}?dbName={self.collection}&all=true"
        async with httpx.AsyncClient(timeout=5, proxies={}) as client:
            response = await client.put(json=data, url=url)
            return response.json()

    async def update_existing_data(self, data, condition):
        ret = await self.query(condition)
        if oldData := ret.get('data'):
            return await self.update({**oldData, **data})
        return await self.update(data)


if __name__ == '__main__':
    result = asyncio.run(PublicRepository("BIZ_CORE_ACTIONS").query({"actionName": "删除实时KPI任务"}))
    # print(PublicRepository("BIZ_CORE_ACTIONS").query_re(1234))
    # result = PublicRepository("BIZ_CORE_ACTIONS").update_re({"actionId": "wk2222"})
    print(result)
    pass
