from fastapi import APIRouter
from api.north.env_check.ue_check import router as ue
from api.north.env_check.pc_check import router as pc
from api.north.env_check.ume_check import router as ume
from api.north.env_check.env_info import router as env_info  # 新增这行

router = APIRouter(prefix="/env_check")
router.include_router(ue)
router.include_router(pc)
router.include_router(ume)
router.include_router(env_info)  # 新增这行