# -*- coding:utf-8 -*
import datetime
import os.path
import sys
from .Data2Bin import *

try:

    import logging

    try:
        dirName = os.path.dirname(os.path.abspath(__file__))
        fileName = os.path.basename(os.path.abspath(__file__))
    except:
        dirName = os.path.dirname(os.path.abspath(sys.argv[0]))
        fileName = os.path.basename(os.path.abspath(sys.argv[0]))

    fmt = '%(name)-12s: %(lineno)d %(levelname)-8s %(message)s'
    datefmt = '%a, %d %b %Y %H:%M:%S',
    formatter = logging.Formatter(fmt, datetime)
    logging.basicConfig(level=logging.INFO,
                        format=fmt,
                        datefmt='%a, %d %b %Y %H:%M:%S',
                        filename=os.path.join(dirName, 'MonitorTelnet.log'),
                        filemode='w'
                        )
    logger = logging.getLogger('')
    console = logging.StreamHandler()
    console.setFormatter(formatter)
    logger.addHandler(console)

except:
    class logging:
        def __init__(self):
            pass

        @staticmethod
        def info(msg):
            print(msg)

        @staticmethod
        def error(msg):
            print(("Error:" + msg))

        @staticmethod
        def warning(msg):
            print(msg)

PROTECT = 1
NOPROTECT = 0

DIRECT = 0
NODIRECT = 1

VARRW = "MemoryRW"
MEMRW = "MemoryRW"
PMU = "Performance"

BIG_END = 1
LITTLE_END = 0

CPU_ARC_64 = 64
CPU_ARC_32 = 32

CPU_AGENT = 0
DIRECTSEND = 1

DEFAULT_LINK_DIRECT = {DIRECTSEND: NODIRECT, CPU_AGENT: DIRECT}

DEFAULT_CORES_LINKTYPE = {
    "6482": DIRECTSEND,
    "8144": DIRECTSEND,
    "6416": DIRECTSEND,
    "6488": DIRECTSEND,
    "8156": DIRECTSEND,
    "X86": DIRECTSEND,
    "DNV": DIRECTSEND,
    "6486": DIRECTSEND,
    "PPC": DIRECTSEND,
    "66x": DIRECTSEND,
    "XC32x": CPU_AGENT,
    "A9": DIRECTSEND,
    "XC4500": CPU_AGENT,
    "XC12": CPU_AGENT,
    "A53(32bit)": DIRECTSEND,
    "A53": DIRECTSEND,
    "Linux_x86": DIRECTSEND,
    "sim_X86": DIRECTSEND,
    "DVN(64bit)": DIRECTSEND,
    "X64": DIRECTSEND,
    "sim_X64": DIRECTSEND,
    "N1": DIRECTSEND,
    "XC16": CPU_AGENT,
    "lwlinux": DIRECTSEND,
    "E500MC": DIRECTSEND,
    "TCI66x": DIRECTSEND,
}
CHIP_TYPE = list(DEFAULT_CORES_LINKTYPE.keys())

SUCCESSFLAG = "~Success~"
SERVER_BEGINTAG = "->"
CLIENT_BEGINTAG = "->"

STATUS_CHECK_COUNT = 30

STATUS_CHECK_WAIT = 1
MONITOR_REPEAT_RUN_COUNT = 3
MONITOR_CMD_WAIT_TIME = 60
MONITOR_RESET_TIME = 20
MONITOR_HOST_IP = "127.0.0.1"
MONITOR_SERVER_PORT = 10043
MONITOR_CLIENTSERVER_PORT = 10042
MONITOR_WEBMNT_CLIENT_PORT = 10044
MONITOR_RESET_COUNT = 10
MONITOR_IP = "MonitorIP"
CORES = "cores"
BOARDS = "chips"
CCIP = "ccip"
EMSIP = "emsip"
OMMBIP = "ommbip"
AAUIP = "aauip"
MONITOR = "DspMonitor"
FUNC_CALL_CONNECTOR = '>>>'
AUTO_CFG_BOARD_SWITCH = "autoCfgBoardSwitch"
MANUAL_LOAD_MAP_SWITCH = "manualLoadMapSwitch"

DEFAULT_CORES_CFG = [(16, "A53(32bit)", BIG_END), (6, "XC4500", BIG_END)]
MCS_1 = [(16, "A53(32bit)", BIG_END), (6, "XC4500", BIG_END)]
MCS_2 = [(16, "A53(32bit)", BIG_END), (6, "XC4500", BIG_END)]

VAR_BASICTYPE_LENGTH = 8  # 变量基本类型所占字节数


class Runer:
    def run(self, cmd, timeout=MONITOR_CMD_WAIT_TIME, waitfor=None):
        pass


class OPAgent:
    def __init__(self, monitor):
        self.monitor = monitor

    def run(self, cmd, timeout=MONITOR_CMD_WAIT_TIME, waitfor=None):
        return self.monitor.run(cmd, timeout, waitfor)

    def looprun(self, cmd, timeout=MONITOR_CMD_WAIT_TIME, waitfor=None):
        return self.monitor.looprun(cmd, timeout, waitfor)


class MonitorReOpenExcption(Exception):
    def __init__(self, value):
        self.value = "<<Monitor ReOpen Excption>> " + value

    def __str__(self):
        return repr(self.value)


class MonitorExcption(Exception):
    def __init__(self, value):
        self.value = "<<cmd run Exception >> " + value

    def __str__(self):
        return repr(self.value)


class MonitorResult:
    def __init__(self, endian, res):
        self.Addr = None  ##变量或者内存的地址
        self.Len = None  ##变量或者内存的长度
        self.Data = None  ##地址读取返回的内存字符串信息
        self.Value = None  ##变量的值，或者内存的读取的前1,2,4,8字节的值
        self.Status = None  ##命令的执行状态
        self.Res = res  ##命令指定的响应内容
        self.endian = endian  ##大小端信息
        self.Buf = None  ##内存的buf
        self.type = None  ##变量类型，主要用于指针读取获取指针的类型
        self.mask = 0xffffffff  ##变量复合类型

    def setData(self, data):
        self.Data = data
        self.BufObj = Data2Bin(data, endian="BIG" if self.endian == BIG_END else "LITTLE")
        self.Buf = self.BufObj.getbuf(0, self.Len)
        if (self.Len == 1): self.Value = self.BufObj.read1b(0)
        if (self.Len == 2):  self.Value = self.BufObj.read2b(0)
        if (self.Len == 4):  self.Value = self.BufObj.read4b(0)
        if (self.Len == 8):  self.Value = self.BufObj.read8b(0)

    def __str__(self):
        return "Addr:{} Len:{} Value:{},buflen:{} Status:{}, type:{}".format(self.Addr, self.Len, self.Value,
                                                                             len(self.Buf) if self.Buf else "None",
                                                                             self.Status, self.type)


class PmuResult:
    def __init__(self, res):
        self.NoneTask = None  # true时表示当前核不存在任务
        self.Value = None  # PMU任务的平均性能数据
        self.Status = None  # 此次查询结果是否对应一个配置任务
        self.Res = res  # 命令指定的响应内容
        self.Completed = None  # 表示已完成，待取的任务数
        self.Uncompleted = None  # 表示总的剩余的未完成任务数
        self.Task = None  # 查询得到的任务参数
        self.Exception = None  # 表示异常信息

    def __str__(self):
        if self.Exception:
            return "Exception: {}, Completed: {}, Uncompleted: {}, Task: {},".format(
                self.Exception, self.Completed, self.Uncompleted, self.Task)
        return "Value: {}, Completed: {}, Uncompleted: {}, Task: {},".format(
            self.Value, self.Completed, self.Uncompleted, self.Task
        )


class FuncCallChainResult:
    def __init__(self, res):
        self.FilterRule = None  # 过滤规则
        self.Chain = {}  # 失败时过滤到的函数调用链，成功为空
        self.Status = 1  # 0:fail  1:success
        self.Res = res  # 命令指定的响应内容

    def parse_chain(self):
        filter_pattern = re.compile("your filter rule is: (.+)", re.M | re.S)
        for line in self.Res.split('\n'):
            search_obj = filter_pattern.search(line)
            if search_obj:
                self.FilterRule = search_obj.group(1)
            if FUNC_CALL_CONNECTOR in line:
                filename = line.split(FUNC_CALL_CONNECTOR)[0]
                if filename not in self.Chain.keys():
                    self.Chain[filename] = []
                self.Chain[filename].append(line)
            if 'Fail' in line:
                self.Status *= 0
            if 'Success' in line:
                self.Status *= 1


class FuncStackSizeResult:
    def __init__(self, res):
        self.Status = 0  # 0:fail  1:success
        self.FunName = None  # 函数名
        self.FunSize = 0  # 函数栈大小
        self.BSP_VINT_ISR_size = 0  # 平台自带中断函数BSP_VINT_ISR栈大小
        self.MaxSize = 0  # 总计可能的最大栈大小
        self.RecursionChain = []  # 可能存在的递归调用链，默认递归10层
        self.Res = res  # 命令指定的响应内容

    def parse_stack(self):
        for line in self.Res.split('\n'):
            if 'fun name' in line:
                self.FunName = line.split(':')[1].strip()
            if 'fun size' in line:
                self.FunSize = int(line.split(':')[1].strip())
            if 'BSP_VINT_ISR' in line:
                self.BSP_VINT_ISR_size = int(line.split(':')[1].strip())
            if 'max size' in line:
                self.MaxSize = int(line.split(':')[1].strip())
            if FUNC_CALL_CONNECTOR in line:
                self.RecursionChain.append(line)
            if 'Fail' in line:
                self.Status = 0
            if 'Success' in line:
                self.Status = 1
