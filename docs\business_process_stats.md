# 业务流程统计功能

## 功能概述

业务流程统计功能用于统计时间段范围内所有环境流水线的执行情况，支持按日期和按环境进行统计分析。

## 业务流程类型

系统支持以下5种业务流程类型：

### 1. 用例通过
- **条件**: `version_test_result` 为 "True"，且 `service_key` 包含 `jenkins_job_save` 并且状态为 `success`
- **描述**: 测试用例执行成功，无需进一步处理

### 2. 智能分析
- **条件**: `version_test_result` 为 "False"，且 `service_key` 只包含 `jenkins_job_save` 和 `log_analysis`
- **描述**: 测试失败后进行智能日志分析

### 3. 环境检测
- **条件**: `version_test_result` 为 "False"，且 `service_key` 只包含 `jenkins_job_save`、`log_analysis` 和 `env_check`
- **描述**: 在智能分析基础上增加环境检测

### 4. 用例复测
- **条件**: `version_test_result` 为 "False"，且 `service_key` 只包含 `jenkins_job_save`、`log_analysis`、`env_check` 和 `online_test`
- **描述**: 在环境检测基础上进行在线测试复测

### 5. 用例回溯
- **条件**: 满足以下任一条件
  - `service_key` 包含 `version_upgrade` 或 `version_rollback`
  - `details.message.subtype` 包含非 "normal" 值
- **描述**: 需要进行版本回溯或特殊处理

## 数据来源

### ES数据表
- **jenkins_job**: Jenkins任务执行记录
- **agents_log**: 代理执行日志
- **biz_core_env**: 业务核心环境信息

### 关键字段
- `task_id`: 任务ID，用于关联不同表的记录
- `timestamp`: 时间戳，用于时间范围筛选和排序
- `version_test_result`: 版本测试结果 ("True"/"False")
- `current_state`: 当前人工确认结果 ("PASS"/"FAIL")
- `manual_processing_hours`: 人工介入时间（小时）
- `service_key`: 服务键，标识执行的服务类型
- `status`: 执行状态 ("success"/"failure"/"in_progress")

## API接口

### 1. 获取业务流程统计数据
```
GET /business_process_stats/
```

**参数**:
- `start_date` (可选): 开始日期，格式 YYYY-MM-DD，默认为当天
- `end_date` (可选): 结束日期，格式 YYYY-MM-DD，默认为开始日期
- `env_id` (可选): 环境ID过滤

**响应**:
```json
{
  "code": {
    "code": "0000",
    "msg": "success"
  },
  "data": {
    "by_date": [
      {
        "date": "2024-01-01",
        "process_counts": {
          "用例通过": 10,
          "智能分析": 5,
          "环境检测": 3,
          "用例复测": 2,
          "用例回溯": 1
        },
        "total_count": 21
      }
    ],
    "by_env": [
      {
        "env_id": "RAN3-T17-10466",
        "process_counts": {
          "用例通过": 8,
          "智能分析": 3
        },
        "total_count": 11
      }
    ],
    "total_stats": {
      "用例通过": 10,
      "智能分析": 5,
      "环境检测": 3,
      "用例复测": 2,
      "用例回溯": 1
    },
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-01"
    }
  }
}
```

### 2. 获取业务流程统计摘要
```
GET /business_process_stats/summary
```

**参数**:
- `start_date` (可选): 开始日期，格式 YYYY-MM-DD
- `end_date` (可选): 结束日期，格式 YYYY-MM-DD

**响应**:
```json
{
  "code": {
    "code": "0000",
    "msg": "success"
  },
  "data": {
    "total_stats": {
      "用例通过": 10,
      "智能分析": 5,
      "环境检测": 3,
      "用例复测": 2,
      "用例回溯": 1
    },
    "date_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-01"
    },
    "total_count": 21
  }
}
```

## 前端展示

### 图表类型

1. **业务流程统计图表** (堆叠柱状图)
   - 显示按日期的各业务流程记录数
   - 支持多日期对比
   - 堆叠显示不同流程类型

2. **业务流程分布图** (饼图)
   - 显示总体业务流程分布比例
   - 直观展示各流程类型占比

### 前端API调用

```typescript
// 获取业务流程统计数据
const result = await jobApi.getBusinessProcessStats(
  '2024-01-01',  // startDate
  '2024-01-07',  // endDate
  'RAN3-T17-10466'  // envId (可选)
);

// 获取业务流程统计摘要
const summary = await jobApi.getBusinessProcessSummary(
  '2024-01-01',  // startDate
  '2024-01-07'   // endDate
);
```

## 处理逻辑

### 1. 数据筛选
- 根据时间范围筛选 `jenkins_job` 表中的记录
- 按 `task_id` 聚合，取时间最早的记录（避免重复统计）

### 2. 日志关联
- 根据 `task_id` 查询对应的 `agents_log` 记录
- 按时间倒序排列，分析执行流程

### 3. 流程判断
- 根据 `version_test_result` 和 `service_key` 序列判断业务流程类型
- 检查 `details.message.subtype` 是否包含特殊标记

### 4. 统计生成
- 按日期统计各业务流程的记录数
- 按环境统计各业务流程的记录数
- 生成总体统计数据

## 测试

运行测试脚本验证功能：

```bash
python test_business_process_stats.py
```

测试内容包括：
- 业务流程类型判断逻辑测试
- 当天统计数据获取测试
- 多日期范围统计测试
- 特定环境统计测试

## 注意事项

1. **时间范围**: 默认统计当天数据，支持自定义时间范围
2. **数据去重**: 同一 `task_id` 的多条记录只取时间最早的一条
3. **流程优先级**: 用例回溯优先级最高，其次按流程复杂度判断
4. **性能考虑**: 大时间范围查询时注意设置合适的 `size` 限制
5. **错误处理**: API调用失败时前端显示默认空数据，不影响页面渲染
