import logging
import sys, os
from logging.handlers import TimedRotatingFileHandler

parent_dir = os.path.abspath(os.path.join(__file__, "../../.."))
log_dir = os.path.join(parent_dir, "logs")
os.makedirs(log_dir, exist_ok=True)
logfile = os.path.join(log_dir, "app.log")


class ColoredFormatter(logging.Formatter):
    # ANSI 转义序列颜色码
    COLOR_MAP = {
        logging.DEBUG: "\033[36m",  # 青色
        logging.INFO: "\033[32m",  # 绿色
        logging.WARNING: "\033[33m",  # 黄色
        logging.ERROR: "\033[31m",  # 红色
        logging.CRITICAL: "\033[41m",  # 红底白字
    }
    RESET = "\033[0m"

    def format(self, record):
        log_fmt = self._style._fmt  # 继承父类格式
        formatter = logging.Formatter(log_fmt)
        output = formatter.format(record)

        color = self.COLOR_MAP.get(record.levelno, self.RESET)
        return f"{color}{output}{self.RESET}"


def set_formatter(color: str = False):
    """设置日志格式化器"""
    fmt = "%(asctime)s | %(levelname)s  |  %(filename)s:%(lineno)d |  %(message)s"
    datefmt = "%Y-%m-%d %H:%M:%S"
    if color:
        return ColoredFormatter(fmt, datefmt=datefmt)
    return logging.Formatter(fmt, datefmt=datefmt)


def set_stream_handler(formatter: logging.Formatter):
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.INFO)
    handler.setFormatter(formatter)
    return handler


def set_timed_rotating_file_handler(formatter: logging.Formatter):
    # 输出到文件的日志处理器, 每天生成一个新文件, 最多保留10个文件
    handler = TimedRotatingFileHandler(logfile, when="midnight", backupCount=10, encoding="utf-8")
    handler.setLevel(logging.INFO)
    handler.setFormatter(formatter)
    handler.suffix = "%Y-%m-%d.log"
    return handler


def close_app_log():
    for logger_name in ("uvicorn", "uvicorn.error", "uvicorn.access"):
        logging.getLogger(logger_name).disabled = True


def get_logger(name: str = "applogger", level: int = logging.INFO):
    logger = logging.getLogger(name)
    logger.setLevel(level)
    formatter = set_formatter()

    stream_handler = set_stream_handler(set_formatter(True))
    file_handler = set_timed_rotating_file_handler(formatter)
    logger.addHandler(stream_handler)
    logger.addHandler(file_handler)
    logger.propagate = False
    return logger


logger = get_logger()

if __name__ == "__main__":
    logger.info(1221212)
