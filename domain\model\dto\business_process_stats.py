from pydantic import BaseModel, Field
from typing import Dict, List, Optional
from datetime import datetime


class BusinessProcessStats(BaseModel):
    """业务流程统计数据模型"""
    date: str = Field(..., description="统计日期")
    process_counts: Dict[str, int] = Field(default_factory=dict, description="各业务流程的记录数")
    total_count: int = Field(0, description="总记录数")


class EnvBusinessProcessStats(BaseModel):
    """环境业务流程统计数据模型"""
    env_id: str = Field(..., description="环境ID")
    process_counts: Dict[str, int] = Field(default_factory=dict, description="各业务流程的记录数")
    total_count: int = Field(0, description="总记录数")


class BusinessProcessStatsRequest(BaseModel):
    """业务流程统计请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD")
    end_date: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD")
    env_id: Optional[str] = Field(None, description="环境ID过滤")


class BusinessProcessStatsResponse(BaseModel):
    """业务流程统计响应模型"""
    by_date: List[BusinessProcessStats] = Field(default_factory=list, description="按日期统计")
    by_env: List[EnvBusinessProcessStats] = Field(default_factory=list, description="按环境统计")
    total_stats: Dict[str, int] = Field(default_factory=dict, description="总体统计")
    date_range: Dict[str, str] = Field(default_factory=dict, description="统计时间范围")


class TaskProcessInfo(BaseModel):
    """任务流程信息模型"""
    task_id: str = Field(..., description="任务ID")
    env_id: str = Field(..., description="环境ID")
    timestamp: str = Field(..., description="时间戳")
    version_test_result: str = Field(..., description="版本测试结果")
    current_state: Optional[str] = Field(None, descrstatusiption="人工确认状态")
    status: Optional[str] = Field(None, description="执行状态")
    manual_processing_hours: str = Field("0", description="人工处理时间")
    fail_reason: str = Field(None, description="人工反馈失败结果")
    service_keys: List[str] = Field(default_factory=list, description="服务键列表")
    process_type: str = Field(..., description="业务流程类型")
    details_subtypes: List[str] = Field(default_factory=list, description="详情子类型列表")
