FROM public-docker-virtual.artnj.zte.com.cn/python:3.11-slim-bullseye
MAINTAINER 10242332

ARG SERVICE_NAME=ci-agents

ENV CHROME_BIN=/usr/bin/google-chrome
ENV CHROMEDRIVER_BIN=/usr/local/bin/chromedriver
ENV PIP_NO_CACHE_DIR=1

ADD ./$SERVICE_NAME.tar /opt

COPY ./google-chrome-stable_132.0.6834.83-1_amd64.deb /tmp/google-chrome.deb
COPY ./chromedriver-linux64.zip /tmp/chromedriver.zip

RUN sh /opt/PIPELINE/docker/update_apt.sh -x && apt-get update && apt-get install -y --no-install-recommends unzip \
    && dpkg -i /tmp/google-chrome.deb || apt-get install -f -y --no-install-recommends \
    && unzip /tmp/chromedriver.zip -d /tmp/ && \
    mv /tmp/chromedriver-linux64/chromedriver /usr/local/bin/chromedriver && \
    chmod +x /usr/local/bin/chromedriver && \
    rm /tmp/chromedriver.zip && \
    rm /tmp/google-chrome.deb && \
    apt-get clean && rm -rf /var/lib/apt/lists/* && rm -rf /opt/PIPELINE/docker/ && \
    pip install --upgrade pip -i https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple \
    && pip install --upgrade setuptools -i https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple \
    && pip install --verbose -r /opt/requirements.txt -i https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple


# RUN apt-get update && apt-get install -y \
#         iputils-ping \
#         net-tools \
#         tcpdump \
#         vim \
#         traceroute 

WORKDIR /opt

CMD python main.py