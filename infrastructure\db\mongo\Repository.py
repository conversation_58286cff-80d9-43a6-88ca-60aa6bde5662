import traceback
from typing import Dict, List

from bson.objectid import ObjectId
from pymongo import UpdateOne, InsertOne, ReplaceOne, DeleteOne

from infrastructure.utils.Env import Env
from infrastructure.db.mongo.MongoDbHandler import MongoDbHandler
from infrastructure.logger.logger import logger


class Repository(object):

    def __init__(self, collection: str, dbCfg=None):
        db_cfg = dbCfg or Env.get_config().MONGO_DB.model_dump()
        self._db: MongoDbHandler = MongoDbHandler(db_cfg)
        self._collection: str = collection
        self._oprMethods = {}

    def create_index(self, keys: any, unique=False):
        return self._db.create_index(keys=keys, unique=unique)

    def update_many(self, condition, newAttrs):
        return self._db.update_many(condition, newAttrs)

    def query(self, condition=None, constraint=None, limit=0, skip=0):
        if condition is None:
            condition = {}
        if '_id' in condition:
            objectId = ObjectId(condition.pop('_id'))
            condition.update({'_id': objectId})
        return self._db.query_with_constraint(condition, collection=self._collection, constraint=constraint,
                                              limit=limit, skip=skip)

    def query_with_constraint(self, condition={}, constraint=None):
        return self._db.query_with_constraint(condition, collection=self._collection, constraint=constraint)

    def query_and_sort(self, condition, sort_key, direction=None, limit=0, skip=0):
        return self._db.query_and_sort(condition, sort_key, direction, self._collection, limit, skip)

    def update(self, attrDict):
        if '_id' in attrDict:
            objectId = ObjectId(attrDict.pop('_id'))
            queryResult = self._db.query_with_constraint({'_id': objectId}, collection=self._collection,
                                                         constraint={"_id": 1})
            if len(queryResult) != 0:
                return self._db.update({'_id': ObjectId(queryResult[0]["_id"])}, attrDict, self._collection)
            else:
                logger.error('ObjectId Not Exist %s: %s' % (self.__class__, attrDict))
                return None
        return self._db.save(attrDict, self._collection)

    def update_with_condition(self, attr_dict: dict, condition: dict):
        if condition is None:
            return self._db.save(attr_dict, self._collection)
        query_result = self._db.query_with_constraint(condition, self._collection)
        if len(query_result) != 0:
            self._db.update(condition, attr_dict, self._collection)
        else:
            self._db.save(attr_dict, self._collection)

    def delete(self, objectId):
        try:
            return self._db.delete({'_id': ObjectId(objectId)}, self._collection)
        except Exception as e:
            logger.warn("delete ObjectId:{0} failed".format(objectId))
            traceback.print_exc()
            raise e

    def delete_many(self, condition):
        try:
            return self._db.delete_many(condition, self._collection)
        except Exception:
            logger.warn("delete data with condition:{0} failed".format(condition))
            traceback.print_exc()

    def aggregate(self, pipeline):
        return self._db.aggregate(pipeline, self._collection)

    def update_menu_data(self, data):
        base_data = self.query({"_id": data.get("_id")})[0]
        for key in base_data:
            if key not in data:
                self._db.delete_by_key({'_id': ObjectId(data.get("_id"))}, key, self._collection)
        return self.update(data)

    def insert_update_data(self, data):
        if '_id' in data:
            base_data = self.query({"_id": data.get("_id")})[0]
            for key in base_data:
                if key not in data:
                    self._db.delete_by_key({'_id': ObjectId(data.get("_id"))}, key, self._collection)
        return self.update(data)

    def insert(self, data):
        # If "_id" is provided in the data, convert it to an ObjectId
        if "_id" in data:
            data.update({'_id': ObjectId(data.get("_id"))})

        # Save the data to the collection in the database
        # Replace self._db and self._collection with the actual variables you are using
        return self._db.save(data, self._collection)

    def insert_one(self, data):

        return self._db.insert_one(data, self._collection)

    def bulk_write(self, operators: List[Dict], ordered: bool = True):
        """
        Perform a bulk write operation on the MongoDB collection using PyMongo.

        :param operators: A list of dictionaries, each representing a bulk write operation.
                          Each dictionary should contain "method" and "content" keys.
        :param ordered: If True (default), execute operations in the order provided.
                        If False, execute operations in arbitrary order.

        methods = {"update": UpdateOne, "insert": InsertOne, "replace": ReplaceOne, "delete": DeleteOne}

        Example:

        >>> _operators = [
        ...     {"method": "update",
        ...      "content": {"_id": "your_id", "$set": {"name": "John", "age": 30}},
        ...      "extraParams": {"upsert": True}},
        ...     {"method": "insert", "content": {"name": "Alice", "age": 25}},
        ...     # More operations...
        ... ]
        >>> _your_instance.bulk_write(_operators)
        """
        if not self._oprMethods:
            self._set_methods_dict()
        requests = []
        extraParas = {}
        try:
            for opr in operators:
                method = self._oprMethods[opr["method"]]
                content = opr["content"]
                self._convert_id_to_objectId(content)
                if "extraParams" in opr:
                    extraParas.update(opr["extraParams"])
                requests.append(method(*content, **extraParas))
            result = self._db.bulk_write(requests, collection=self._collection, ordered=ordered)
            return result
        except Exception as e:
            logger.error(f"Error: {e}")
            raise e

    def _set_methods_dict(self):
        self._oprMethods = {"update": UpdateOne, "insert": InsertOne, "replace": ReplaceOne, "delete": DeleteOne}

    @staticmethod
    def _convert_id_to_objectId(_filter: Dict):
        if '_id' in _filter and type(_filter["_id"] != ObjectId):
            objectId = ObjectId(_filter.get('_id'))
            _filter.update({"_id": objectId})

    def create_ttl_index(self, index, expireAfterSeconds=None):
        if expireAfterSeconds is None:
            self._db.create_ttl_index(index)
        else:
            self._db.create_ttl_index(index, expireAfterSeconds)

    def update_attrs(self, condition, attrDict, upsert=False):
        return self._db.update_attrs(condition, {"$set": attrDict}, self._collection, upsert=upsert)

    def unset_attr(self, filter: dict, unsetAttrs: dict):
        return self._db.update_attrs(filter, {"$unset": unsetAttrs}, self._collection)

    def update_many_with_upsert(self, index_field, data_list):
        """批量插入数据，如果索引值index_field 已经存在，则更新这条数据，如果不存在则插入一条新数据"""
        try:
            operations = []
            for data in data_list:
                operations.append(UpdateOne({index_field: data[index_field]}, {'$set': data}, upsert=True))
            return self._db.bulk_write(operations, collection=self._collection)
        except Exception:
            logger.warn("update_many_with_upsert:{0}, {1} failed".format(index_field, data_list))
            traceback.print_exc()

    def query_advanced(self, condition, proj, **kwargs):
        condition = {} if condition is None else condition
        self._convert_id_to_objectId(condition)
        return self._db.query_advanced(self._collection, condition, proj, **kwargs)
