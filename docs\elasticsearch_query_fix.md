# Elasticsearch Query Fix

## Issue

The application was experiencing the following error when trying to update documents in Elasticsearch:

```
elasticsearch.exceptions.RequestError: RequestError(400, 'parsing_exception', "[match_phrase] query doesn't support multiple fields, found [task_id] and [service_type]")
```

This error occurred because the `update_by_query` method in the `Document` class was incorrectly passing multiple fields to a single `match_phrase` query, which is not supported by Elasticsearch.

## Solution

The `update_by_query` method has been updated to handle multiple fields correctly by creating a separate `match_phrase` query for each field in the query dictionary.

### Changes Made

1. **Updated `update_by_query` Method**:
   - Changed the query construction to iterate through each key-value pair in the query dictionary
   - Created a separate `match_phrase` query for each field
   - Added better error handling and logging
   - Added support for Elasticsearch 7.x's total hits format

2. **Simplified `update_by_task_id` Method**:
   - Refactored to use the more general `update_by_query` method
   - This reduces code duplication and ensures consistent behavior

3. **Fixed `query_all_without_time` Method**:
   - Added missing `fromId` and `size` parameters to the query

## Example

Before:
```python
def update_by_query(self, query_dict:dict, filter:dict, fromId=0, size=50, flag=True):
    try:
        mustMatch = []
        mustMatch.append({"match_phrase": query_dict})  # This was causing the error
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "from": fromId, "size": size}
        # ... rest of the method
    except Exception as e:
        logging.info(f'ES update by task_id failed! Reason is {e}')
        traceback.print_exc()
        return False
    return True
```

After:
```python
def update_by_query(self, query_dict:dict, filter:dict, fromId=0, size=50, flag=True):
    try:
        mustMatch = []
        # Process each field separately
        for key, value in query_dict.items():
            mustMatch.append({"match_phrase": {key: value}})
        queryDsl = {"query": {"bool": {"must": mustMatch}}, "from": fromId, "size": size}
        # ... rest of the method
    except Exception as e:
        logging.error(f'ES update by query failed! Query: {query_dict}, Reason: {e}')
        traceback.print_exc()
        return False
    return True
```

## Testing

The changes have been tested and verified to work correctly. The application can now update documents in Elasticsearch using multiple fields in the query without encountering the parsing exception.

## Impact

This fix ensures that all services that use the `update_by_query` method will work correctly, including:
- Version rollback service
- Version update service
- Local retest service
- And any other service that uses this method to update documents in Elasticsearch

## Note

The unused `flag` parameter in several methods has been identified but not removed to maintain backward compatibility. This could be addressed in a future refactoring if needed.
