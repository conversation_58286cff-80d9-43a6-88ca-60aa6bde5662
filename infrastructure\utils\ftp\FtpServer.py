# -*- encoding: utf-8 -*-
'''
@File    :   CustomList.py
@Time    :   2024/01/13 18:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''

import os
import threading
import socket
import ftplib

from pyftpdlib.authorizers import DummyAuthorizer
from pyftpdlib.handlers import FTPHandler
from pyftpdlib.servers import FTPServer
from infrastructure.logger.logger import logger
from infrastructure.utils.Repeat import retries_on_flag

class FtpServer(object):

    def __init__(self):
        self.authorizer = DummyAuthorizer()
        self.handler = None
        self.server = None
        self.serverThread = None

    def start_ftp_server(self, ip, port, homeDir, user="root", pwd="root"):
        if os.path.exists(homeDir) is False:
            os.makedirs(homeDir)
        self.authorizer.add_user(user, pwd, homeDir, perm='elradfmw', msg_login='Login successsful.', msg_quit='Goodbye')
        self.authorizer.add_anonymous(homeDir)
        self.handler = FTPHandler
        self.handler.authorizer = self.authorizer
        self.handler.banner = 'based ftpd ready'
        self.server = FTPServer((ip, port), self.handler)
        self.server.maxCons = 256
        self.server.maxConsPerIp = 5
        self.server.serve_forever()

    def start(self, ip, port, homeDir, user="root", pwd="root"):
        if not self._check_ftp_port_used(port):
            self.serverThread = threading.Thread(target=self.start_ftp_server, args=(ip, port, homeDir, user, pwd))
            self.serverThread.daemon = True
            self.serverThread.start()

    def stop_ftp_server(self):
        self.server.close_all()
        self.server = None
        del(self.serverThread)

    @retries_on_flag(10, flag=True, isRaiseException=False, everyTryDelaySecs=6)
    def _check_ftp_port_used(self, port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                ftp = ftplib.FTP()
                ftp.connect('localhost', port)
                ftp.quit()
                logger.error(f"bizcore端口:{port},已被占用")
                return True
            except ftplib.all_errors:
                return False

