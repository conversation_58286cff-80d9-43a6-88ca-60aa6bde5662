from infrastructure.Singleton import Singleton
from infrastructure.db.redis.Redisdb import RedisDB
from infrastructure.utils.Env import Env
from service.agents_service_factory import AgentsServiceFactory
from infrastructure.utils.json2 import marshal, unmarshal
from infrastructure.logger.logger import logger
import logging
import json
import traceback
from service.logging_service import LoggingService  # Circular import resolved


@Singleton
class CiAgentsQueue(RedisDB):
    """
    Redis-based queue handler for CI Agents tasks with singleton pattern implementation.
    Handles message processing lifecycle including logging, error handling, and cleanup.
    """

    # Operation constants
    OP_LISTEN = "listen"
    OP_PROCESS = "process_message"
    OP_PUSH = "push_info"

    # Status constants
    STATUS_STARTED = "in_progress"
    STATUS_SUCCESS = "success"
    STATUS_FAILURE = "failure"

    def __init__(self):
        super().__init__()
        self.queue = Env.get_config().REDIS.ci_agent_queue
        self._configure_logging()

    def _configure_logging(self):
        """Configure logging levels for third-party libraries"""
        logging.getLogger("elasticsearch").setLevel(logging.WARNING)

    def _log_execution(self, operation, status, details=None,
                       task_id=None, env_id=None, service_key=None):
        """Centralized execution logging"""
        LoggingService.log_execution(
            service_type=self.__class__.__name__,
            operation=operation,
            status=status,
            details=details or {},
            task_id=task_id,
            env_id=env_id,
            service_key=service_key
        )

    async def listen(self):
        """Start listening and processing messages from Redis queue"""
        self._log_queue_start()

        while True:
            message = self._get_next_message()
            if not message:
                continue

            await self._process_message(message)

    def _log_queue_start(self):
        """Log queue listening startup"""
        logger.info(f"Starting queue listener|queue:{self.queue}|status:{self.STATUS_STARTED}")
        self._log_execution(
            operation=self.OP_LISTEN,
            status=self.STATUS_STARTED,
            details={"queue": self.queue}
        )

    def _get_next_message(self):
        """Retrieve and parse next message from Redis queue"""
        try:
            if message := self.blpop(self.queue, 0)[1]:
                logger.info(f"Message received|content:{message}")
                return json.loads(message)
        except (IndexError, json.JSONDecodeError) as e:
            logger.error(f"Message retrieval failed|error:{str(e)}")
            self._log_error(self.OP_PROCESS, str(e))
        return None

    async def _process_message(self, message_data):
        """Process a single queue message through full lifecycle"""
        service_msg = message_data  # Alias for clarity
        log_context = {
            "task_id": service_msg.get('task_id'),
            "env_id": service_msg.get('env_id'),
            "service_type": service_msg.get('service_type')
        }

        try:
            self._log_message_received(service_msg)
            service = self._create_service(service_msg)

            if not service:
                self._handle_creation_failure(service_msg)
                return

            await self._execute_service(service)
            self._log_success(service_msg)

        except Exception as e:
            self._handle_processing_error(e, service_msg)

    def _log_message_received(self, message):
        """Log message receipt with context"""
        self._log_execution(
            operation=self.OP_PROCESS,
            status=self.STATUS_STARTED,
            details={"message": message},
            **self._extract_message_context(message)
        )

    def _create_service(self, message):
        """Factory method for service creation"""
        return AgentsServiceFactory().create_agents_service(message)

    def _handle_creation_failure(self, message):
        """Handle service creation failure scenario"""
        logger.error(f"Service creation failed|message:{message}")
        self._log_execution(
            operation=self.OP_PROCESS,
            status=self.STATUS_FAILURE,
            details={"error": "Service creation failed", "message": message},
            **self._extract_message_context(message)
        )

    async def _execute_service(self, service):
        """Execute service and handle cleanup"""
        try:
            await service.run()
        finally:
            del service  # Explicit cleanup for resource-heavy services

    def _log_success(self, message):
        """Log successful message processing"""
        self._log_execution(
            operation=self.OP_PROCESS,
            status=self.STATUS_SUCCESS,
            details={"message": message},
            **self._extract_message_context(message)
        )

    def _handle_processing_error(self, error, message=None):
        """Handle processing errors with full traceback"""
        error_msg = f"Processing error: {str(error)}"
        logger.error(f"{error_msg}|traceback:{traceback.format_exc()}")

        self._log_execution(
            operation=self.OP_PROCESS,
            status=self.STATUS_FAILURE,
            details={"message": message,
                     "error": error_msg,
                     "traceback": traceback.format_exc().splitlines()
                     },
            **self._extract_message_context(message) if message else {}
        )

    def _extract_message_context(self, message):
        """Extract common context from message"""
        message = dict(message) if isinstance(message, dict) else unmarshal(message)
        return {
            "task_id": message.get("task_id"),
            "env_id": message.get("env_id"),
            "service_key": message.get("service_type")
        }

    async def push_info(self, msg):
        """Push a message to Redis queue with full logging"""
        log_context = {
            "message_type": type(msg).__name__,
            **{attr: getattr(msg, attr, None)
               for attr in ['task_id', 'env_id', 'service_type']}
        }

        try:
            self._log_push_attempt(msg)
            self._store_message(msg)
            self._log_push_success(msg)

        except Exception as e:
            self._handle_push_error(e, msg)

    def _log_push_attempt(self, msg):
        """Log message push initiation"""
        self._log_execution(
            operation=self.OP_PUSH,
            status=self.STATUS_STARTED,
            details={"message_type": type(msg).__name__},
            **self._extract_message_context(msg)
        )

    def _store_message(self, msg):
        """Core message storage logic"""
        logger.info(f"Pushing message|content:{marshal(msg)}")
        self.lpush(self.queue, marshal(msg))

    def _log_push_success(self, msg):
        """Log successful message push"""
        self._log_execution(
            operation=self.OP_PUSH,
            status=self.STATUS_SUCCESS,
            details={"message_type": type(msg).__name__},
            **self._extract_message_context(msg)
        )

    def _handle_push_error(self, error, msg):
        """Handle push errors with logging"""
        error_msg = f"Push failed: {str(error)}"
        logger.error(f"{error_msg}|message:{marshal(msg)}")

        self._log_execution(
            operation=self.OP_PUSH,
            status=self.STATUS_FAILURE,
            details={
                "error": error_msg,
                "traceback": traceback.format_exc().splitlines(),
                "message_type": type(msg).__name__
            },
            **self._extract_message_context(msg)
        )

    def _log_error(self, operation, error_msg):
        """Generic error logging helper"""
        self._log_execution(
            operation=operation,
            status=self.STATUS_FAILURE,
            details={"error": error_msg}
        )