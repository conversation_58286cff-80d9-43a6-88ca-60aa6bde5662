from typing import List, Optional
from typing import Literal

from pydantic import BaseModel


class TestCaseLog(BaseModel):
    testcase_name: str
    msg: str = ""
    detail_msg: str = ""
    fail_keywords: List[str] = []
    status: str = ""
    env_id: str = ""
    updatedTime: str = ""
    updatedBy: str = ""
    source: str = ""
    vectorDbRef: str = ""
    EC: str = ""
    ai_result: str = ""
    result: str = ""
    manualAnalysisResult: str = ""
    subtype: Optional[Literal['normal', "first_time", 'rollback', 'update']] = "normal"
    createdTime: str = ""
    task_id: str = ""
    manualClassification: str = ""
    parent_suites: str = ""
