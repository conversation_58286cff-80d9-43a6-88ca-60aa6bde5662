# coding=utf-8
OPERATIONS = {
    'reboot': {
        'cmd': 'reboot',
        'expected': None,
        'waitSecs': 10
    },
    'mcp': {
        'cmd': 'SSP_DbgShowState',
        'expected': 'SYSTEM STATE IS:      0',
        'proc': 'MGR',
        'timeout': 5
    },
    'ushell': {
        'cmd': 'ushell',
        'expected': 'password'
    },
    'password': {
        'cmd': 'zte',
        'expected': 'Login success'
    },
    'vgc_power_on': {
        'cmd': 'bsp_x86_power_on 0',
        'expected': None,
        'proc': 'MGR'
    },
    'vgc_force_poweroff': {
        'cmd': 'bsp_x86_force_poweroff 0',
        'expected': None,
        'proc': 'MGR'
    }
}

STUBS = {
    "delVersionFiles": [{
        'cmd': 'rm -rf /root/comm_component/*',
        'expected': '#'
    }, {
        'cmd': 'rm -rf /root/cpf/*',
        'expected': '#'
    }, {
        'cmd': 'rm -rf /root/dpf/*',
        'expected': '#'
    }, {
        'cmd': 'cd /root/image',
        'expected': '#'
    }, {
        'cmd': 'rm -rf !(igb_uio.ko|vm_deploy.json|confsubnet.sh)',
        'expected': '#'
    }],
    "startPlatContainer": [
        {
            'cmd': 'cd /root/image',
            'expected': '#',
        },
        {
            'cmd': 'python envforB03.py',
            'expected': 'Please confirm The state of the container and nf topo',
        }
    ],
    "startCpfContainer": [
        {
            'cmd': 'cd /root/cpf/script',
            'expected': '#',
        },
        {
            'cmd': 'chmod +x initRunForArt.sh',
            'expected': '#',
        },
        {
            'cmd': './initRunForArt.sh',
            'expected': 'CPF services are all running',
        }],
    "configNfOam": [{
        'cmd': 'docker exec -it nf-oam sh',
        'expected': '#'
    }, {
        'cmd': 'cd bin'
    }, {
        'cmd': 'netopeer2-cli.sh',
        'expected': '>'
    }, {
        'cmd': 'connect --port 6001\r',
        'expected': 'password',
        'timeout': 15
    }, {
        'cmd': 'root123\r',
        'expected': '>'
    }, {
        'cmd': 'edit-config --target running --config=/home/<USER>/base_data_S35_vbs1.xml\r',
        'expected': 'OK',
        'timeout': 20
    }, {
        'cmd': 'get-config --source running\r',
        'expected': '/ManagedElement',
        'timeout': 20
    },
    ],
    "configNfOamWithYes": [{
        'cmd': 'docker exec -it nf-oam sh',
        'expected': '#'
    }, {
        'cmd': 'cd bin'
    }, {
        'cmd': 'netopeer2-cli.sh',
        'expected': '>'
    }, {
        'cmd': 'connect --port 6001\r',
        'expected': 'yes',
        'timeout': 15
    }, {
        'cmd': 'yes\r',
        'expected': 'password',
        'timeout': 15
    }, {
        'cmd': 'root123\r',
        'expected': '>'
    }, {
        'cmd': 'edit-config --target running --config=/home/<USER>/base_data_S35_vbs1.xml\r',
        'expected': 'OK',
        'timeout': 20
    }, {
        'cmd': 'get-config --source running\r',
        'expected': '/ManagedElement',
        'timeout': 20
    }],
    "makeInitConfig": [
        {
            'cmd': 'cd /root/cpf/script/nf-oam',
            'expected': '#',
            'timeout': 20
        },
        {
            'cmd': 'python make_init_config.py',
            'expected': 'copy config from running to startup success',
            'timeout': 60
        }
    ],
    "setMcsVlan": [
        {
            'cmd': 'NtlL2ssPortQVlanSet',
            'expected': 'end to excel fun:NtlL2ssPortQVlanSet',
            'timeout': 100,
            'proc': 'PLAT'
        }
    ],
    "bspShowSlaveCoreInfo": [
        {
            'cmd': 'BspShowSlaveCoreInfo',
            'expected': 'end to excel fun:BspShowSlaveCoreInfo',
            'timeout': 100,
            'proc': 'PLAT'
        }
    ],
    "initRunForDpf": [
        {
            'cmd': 'cd /root/dpf/script',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': 'chmod +x ./initRunForDpf.sh',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': './initRunForDpf.sh',
            'expected': '#',
            'timeout': 30
        }
    ],
    "loadNf": [
        {
            'cmd': 'docker rm -f  cos',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': 'docker rmi cos:B03',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': 'cd ~/cpf/bin',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': 'docker load -i <EMAIL>',
            'expected': 'cos:B03',
            'timeout': 120
        },
        {
            'cmd': 'cd /root/cpf/script/cos',
            'expected': '#',
            'timeout': 120
        },
        {
            'cmd': 'python start_cos.py',
            'expected': '/home/<USER>',
            'timeout': 60
        },
        {
            'cmd': 'docker exec -it ccm sh',
            'expected': '#',
            'timeout': 10
        },
        {
            'cmd': 'cellrel 2018,2',
            'expected': 'CCM CELL SETUP ------- FINISH',
            'proc': 'ccm',
            'timeout': 180
        }
    ],
}


if __name__ == "__main__":
    print(STUBS["bspShowSlaveCoreInfo"])
