# Cron Task Display Feature

## Overview

This document describes the implementation of the cron task display feature in the environment information management interface. This feature allows users to view and filter scheduled tasks in the system.

## Components

### Backend

1. **API Endpoint**
   - Created a new endpoint at `/env_info/cron_task` to retrieve cron task data
   - Implemented GET methods for retrieving all tasks and tasks by ID

2. **Service Layer**
   - Created a new service class `CronTaskService` to handle cron task data retrieval
   - Implemented methods for getting all tasks, tasks by ID, and running tasks

3. **Router Configuration**
   - Updated the environment information router to include the cron task endpoint

### Frontend

1. **API Service**
   - Added a new API service `cronTaskApi` in `envInfoApi.js` to interact with the cron task endpoint
   - Implemented methods for retrieving all tasks and tasks by ID

2. **Component**
   - Created a new component `CronTaskTable.vue` to display cron task data
   - Implemented filtering by task ID, state, and service type
   - Added a details dialog to view task details

3. **View Integration**
   - Added a new tab "Cron Tasks" in the `EnvInfoView.vue` component
   - Implemented data fetching and state management for cron tasks

## Features

1. **Task Listing**
   - Display all cron tasks in a table format
   - Show key information: task ID, environment ID, service type, subtype, and state

2. **Filtering**
   - Filter tasks by task ID (search)
   - Filter tasks by state (running, completed, failed, not found)
   - Filter tasks by service type (local test, local retest)

3. **Task Details**
   - View detailed information about a task by clicking "View Details"
   - Display task metadata and needed information in a formatted dialog

4. **State Visualization**
   - Use color-coded tags to indicate task state:
     - Running: Yellow
     - Completed: Green
     - Failed: Red
     - Not Found: Gray

## Data Structure

The cron task data is based on the `CornTask` model in `domain/model/dto/corn_task.py`:

```python
class CornTask(BaseModel):
    env_id: str
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()).replace("-", "")[:32])
    needed: Needed
    service_type: str
    state: str
    subtype: Optional[Literal['normal', "first_time", 'rollback', 'update']] = "normal"
```

Where `Needed` is:

```python
class Needed(BaseModel):
    tar_path: str = ""
    log_path: str = ""
    log_name: str = ""
    job_name: str = ""
    build_number: str = ""
    pipelineUuid: str = ""
    pipelineStageId: str = ""
    recordId: str = ""
```

## Future Enhancements

1. **Server-side Filtering**
   - Implement server-side filtering for better performance with large datasets

2. **Task Management**
   - Add ability to cancel or restart tasks

3. **Task History**
   - Add a history view to see completed tasks and their results

4. **Pagination**
   - Add pagination for better handling of large numbers of tasks

5. **Auto-refresh**
   - Implement automatic refresh to keep the task list up-to-date
