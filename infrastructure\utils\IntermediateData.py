#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/8/2 14:15
# <AUTHOR> 10263601
from pathlib import Path
from infrastructure.logger.logger import logger


class IntermediateData:
    """中间产物
    用处处理中间产物，包括记录到日志，上报到特定服务

    """

    @staticmethod
    def record_log(*paths, tip="IntermediateData", sep="<br/>"):
        urlPrefix = "https://zxmte.zte.com.cn:7776"
        htmlFormat = "<a href = {urlPrefix}{filePath} > {fileName} </a>" + sep
        html = tip + "*-" * 30 + sep
        for path in paths:
            fileName = Path(path).name
            html += htmlFormat.format(urlPrefix=urlPrefix, filePath=path, fileName=fileName)
        logger.info(html)


if __name__ == '__main__':
    # from pathlib import Path
    # a = ["/a/b/c.zip", "/d/e.text"]
    # IntermediateData.record_log(*a, tip="KPI数据")
    pass

