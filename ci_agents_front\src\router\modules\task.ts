const Layout = () => import("@/layout/index.vue");

export default {
  path: "/task",
  name: "Task",
  component: Layout,
  redirect: "/task/list",
  meta: {
    icon: "ep/document",
    title: "任务管理",
    rank: 3 // 调整rank值以控制菜单顺序
  },
  children: [
    {
      path: "/task/detail/:taskId",
      name: "TaskDetail",
      component: () => import("@/views/task/detail.vue"),
      meta: {
        title: "任务详情",
        showLink: false // 不在菜单中显示
      }
    }
  ]
} satisfies RouteConfigsTable;