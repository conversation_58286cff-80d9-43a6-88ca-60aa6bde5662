#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/5/24 14:30
# <AUTHOR> 10263601
import copy


def get_target_schema_ref_name(openapi_schema):
    if paths := openapi_schema.get("paths"):
        if len(paths) != 1:
            return
        for _, method in paths.items():
            for _, resource in method.items():
                if content := resource.get("requestBody", {}).get("content"):
                    for content_type, schema in content.items():
                        if schema and (ref := schema.get("schema", {}).get("$ref")):
                            return parse_ref_name(ref)


def inline_components_references(openapi_schema):
    def traverse(obj):
        if isinstance(obj, dict):
            if "$ref" in obj:
                ref_name = parse_ref_name(obj["$ref"])
                ref_value = openapi_schema["components"]["schemas"][ref_name]
                obj.pop("$ref")
                obj.update(ref_value)
            else:
                for value in obj.values():
                    traverse(value)
        elif isinstance(obj, list):
            for item in obj:
                traverse(item)

    if ref_name := get_target_schema_ref_name(openapi_schema):
        target_schema = copy.deepcopy(openapi_schema["components"]["schemas"][ref_name])
        set_para_default_attrs(target_schema)
        traverse(target_schema)
        openapi_schema["components"]["schemas"] = target_schema

    return openapi_schema


def parse_ref_name(ref):
    return ref.split("/")[-1]


def set_para_default_attrs(target_schema):
    for attr in target_schema.get("properties", {}).values():
        attr.setdefault("ifEmptyUseDefault", True)
