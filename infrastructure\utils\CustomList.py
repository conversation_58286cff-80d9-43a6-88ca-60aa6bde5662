# -*- encoding: utf-8 -*-
'''
@File    :   CustomList.py
@Time    :   2023/08/17 18:47:44
<AUTHOR>   王建雄10154402
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import asyncio


class CustomList(list):
    def map(self, func, *args, **kwargs):
        res = [func(item, *args, **kwargs) for item in self]
        return CustomList(item for item in res if item is not None)

    async def async_map(self, func, *args, **kwargs):
        tasks = [func(item, *args, **kwargs) for item in self]
        res = await asyncio.gather(*tasks)
        return CustomList(item for item in res if item is not None)


if __name__ == '__main__':
    a = CustomList(["a", "a", "b", "c"])

    def test(i: str, l: list):
        if i in l:
            return
        l.append(i)
        return i

    res = a.map(test, [])
    print(res)