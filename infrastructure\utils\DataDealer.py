import copy
import json
import re
from functools import reduce
from typing import Optional

from infrastructure.logger.logger import logger


def lower_dict_keys(data):
    return {key.lower(): data.get(key) for key in data.keys()}


def filter_dict_by_keys(items: list, keys: list, mocName: str, subNetWork: str, meId: str) -> list:
    rets = []
    for item in items:
        res = {key: item[key] for key in keys if key in item}
        if res:
            res.update(
                {'ldn': item['ldn'], 'moId': item['moId'], 'mocName': mocName, 'subNetWork': subNetWork, 'meId': meId})
            rets.append(res)
    return rets


def str_to_dict(dictStr: str) -> dict:  # str_to_dict 将固定格式的str转为dict，str格式为: "k1=v1,k2=v2"
    dictStr = dictStr.strip()
    if len(dictStr) == 0:
        return {}
    items = dictStr.split(",")
    ret = {}
    for item in items:
        tempItem = item.split("=")
        ret.update({tempItem[0]: tempItem[1]})
    return ret


def replace_placeholder_parameters(paras: dict, target: dict) -> dict:
    """
    替换占位符参数
    parameters:
        paras: dict,用于替换的参数, 如 {"A": "1", "B": "2"}
        target: dict,要被替换的参数们, 如 {"k1": "${A}", "k2": {"k3": "${B}"}}
    return:
        替换后的target, 如 {"k1": "1", "k2": {"k3": "2"}}
    """

    def __replace_values(paras: dict, s: str) -> str:
        for k, v in paras.items():
            placeholder = "${" + k + "}"
            s = s.replace(placeholder, str(v))
        return s

    def _fast_replace():
        target_str = json.dumps(target)  # 将 target 序列化为字符串
        target_str = __replace_values(paras, target_str)
        return json.loads(target_str.encode("utf-8"))  # 将 target_str 反序列化为字典

    def _slow_replace(paras: dict, d: dict):

        for k in d:
            if isinstance(d[k], str):
                d[k] = __replace_values(paras, d[k])
            elif isinstance(d[k], dict):
                d[k] = _slow_replace(paras, d[k])  # 如果该值是字典类型，则对其递归调用

        return d

    try:
        return _fast_replace()
    except:
        return _slow_replace(paras, target)


def str_to_float(floatStr: str) -> float:
    return float(floatStr)


def dict_to_str(ldnDict: dict):  # dict_to_str 将dict转化为str, str格式为: "k1=v1,k2=v2"
    if len(ldnDict) == 0:
        return ''
    ret = ''
    for k, v in ldnDict.items():
        ret += f'{k}={v},'
    return ret.strip(',')


def str_to_list(listStr: str) -> list:  # str_to_list 将固定格式的str转为list，str格式为: "k1,k2,k3"
    listStr = listStr.strip()
    if len(listStr) == 0:
        return []
    return listStr.split(";")


def iteritems(d):
    try:
        return d.iteritems()
    except AttributeError:
        return d.items()


def is_contain_dict(aimDict, conditionDict):
    for key in conditionDict.keys():
        if key not in aimDict:
            return False
        else:
            if aimDict.get(key) != conditionDict.get(key):
                return False
    return True


def get_filtered_List(inputDictList, filterDict):
    filteredList = [dic for dic in inputDictList if all(
        dic.get(key) == filterDict[key] for key in filterDict)]
    return filteredList


def get_value_by_data_chain(data_chain: list, data: dict):
    tmp = data
    for key in data_chain:
        tmp = tmp.get(key)
    return tmp


def is_not_empty_dict(inputDict):
    return inputDict != {}


def capture_num_from_str(string1):
    nums = re.findall(r"\d", string1)
    numString = ''
    for num in nums:
        numString += num
    return str(numString)


def assemble_result_by_field_name(keyList, valueLists, needKeyList=None) -> list:
    if not needKeyList:
        needKeyList = keyList
    resultDictList = []
    needKeyIndexs = []
    for key in needKeyList:
        if key in keyList:
            needKeyIndexs.append(keyList.index(key))
        else:
            raise Exception(
                "Not Found attr Key <{0}> in {1}".format(key, keyList))
    for valueList in valueLists:
        resultValue = []
        for index in needKeyIndexs:
            resultValue.append(valueList[index])
        resultDictList.append(dict(zip(needKeyList, resultValue)))
    return resultDictList


def get_mo_filter_with_key_path(moAttrList, keyMoPathDict=None):
    if keyMoPathDict is None:
        return moAttrList
    moFilteredList = []
    keyMoPathDict1 = copy.deepcopy(keyMoPathDict)
    for moAttr in moAttrList:
        ldn = moAttr.get("ldn")
        count = 0
        for key in keyMoPathDict1.keys():
            mo = "{0}={1}".format(key, keyMoPathDict1.get(key))
            if mo not in ldn:
                break
            else:
                count = count + 1
        if count == len(keyMoPathDict1):
            moFilteredList.append(moAttr)
    return moFilteredList


def get_result_with_attribute(attributeNameList, moList):
    resultList = []
    if attributeNameList:
        for mo in moList:
            mo_copy = copy.deepcopy(mo)
            for key in mo.keys():
                if key not in attributeNameList:
                    mo_copy.pop(key)
            resultList.append(mo_copy)
    else:
        resultList = moList
    return resultList


def judge_ldn_by_key_path(attrList, keyMoPathDict) -> str:
    ldnResultList = []
    keyMoPathStringList = []
    for key, value in keyMoPathDict.items():
        keyMoPathStringList.append("{0}={1}".format(key, value))
    for item in attrList:
        ldnString = item.get("ldn")
        ldnList = ldnString.split(",")
        if set(ldnList) >= set(keyMoPathStringList):
            ldnResultList.append(ldnString)
    if len(ldnResultList) == 1:
        return ldnResultList[0]
    else:
        logger.error("After match Multi Moc:{0}".format(ldnResultList))
        raise Exception("KeyMoPath error, can not find the unique moc")


def parse_ldn_to_dict(ldn: str) -> dict:
    """
    将 ldn 字符串解析为字典。例如 "NRCellCU=1,NRCellDU=2" 将被转换为 {'NRCellCU': '1', 'NRCellDU': '2'}。
    """
    elements = ldn.split(",")
    ldn_dict = {}
    for elem in elements:
        if "=" in elem:
            key, value = elem.split("=")
            ldn_dict[key] = value
    return ldn_dict


def filter_res_by_cell_attr(cell_attr: dict, res: list[dict]) -> list[dict]:
    cellMoNames = ["NRCellCU", "NRCellDU", "NRPhysicalCellDU", "DUEUtranCellFDDLTE", "DUEUtranCellTDDLTE",
                   "CUEUtranCellFDDLTE", "CUEUtranCellTDDLTE"]

    # 提取第一个元素的 ldn，并解析为字典
    tmpLdn = res[0].get("ldn", "")
    tmpLdn_dict = parse_ldn_to_dict(tmpLdn)

    # 查找匹配的 cellName
    cell = next((cellName for cellName in cellMoNames if cellName in tmpLdn_dict), "")

    # 如果不属于小区下的MO，直接返回
    if not cell:
        return res

    # 过滤结果，根据 cell_attr 中的 cellId 精确匹配字典中的对应值
    cell_id = cell_attr.get("cellId")
    return [item for item in res if parse_ldn_to_dict(item.get("ldn", "")).get(cell) == cell_id]


def update_mo_data_by_parent_mo(mo_datas: list, parent_ldn: str, moc_name: Optional[str] = None) -> list:
    for mo_data in mo_datas:
        mo_data["ldn"] = f'{parent_ldn},{moc_name or mo_data.get("mocName")}={mo_data.get("moId")}'
    return mo_datas


def judge_ldns_by_key_path(attrList, keyMoPathDict) -> list:
    ldnResultList = []
    keyMoPathStringList = []
    for key, value in keyMoPathDict.items():
        keyMoPathStringList.append("{0}={1}".format(key, value))
    for item in attrList:
        ldnString = item.get("ldn")
        ldnList = ldnString.split(",")
        if set(ldnList) >= set(keyMoPathStringList):
            ldnResultList.append(ldnString)
    return ldnResultList


def judge_attr_exactitude(mocName, defaultAttr, mandatoryAttr):
    moreKey = []
    errorKey = []
    for key, value in defaultAttr.items():
        if key not in mandatoryAttr.keys():
            errorKey.append(key)
        if value is None and not mandatoryAttr.get(key):
            defaultAttr.pop(key)
        if value is None and mandatoryAttr.get(key):
            moreKey.append(key)
    if len(errorKey):
        raise Exception(
            "Assemble Mo attrs {0} fail, Not Exist Attr: {1}".format(mocName, errorKey))
    if len(moreKey):
        raise Exception(
            "Assemble Mo attrs {0} fail, Need More Attr: {1}".format(mocName, moreKey))
    return defaultAttr


def pop_not_mandatory_attr(attrDict, mandatoryDict):
    for key, value in attrDict.items():
        if value is None or not mandatoryDict.get(key, False):
            attrDict.pop(key)
    return attrDict


def get_filtered_moId_list(moDictList, keyMoPathDict):
    moIdList = []
    for moDict in moDictList:
        if 'ldn' not in moDict or 'moId' not in moDict:
            continue
        if _is_key_mo_path_in_ldn(str(moDict.get('ldn')), keyMoPathDict):
            moIdList.append(str(moDict.get('moId')))
    return moIdList


def _is_key_mo_path_in_ldn(ldn, keyMoPathDict):
    for key in keyMoPathDict:
        if '{0}={1}'.format(key, str(keyMoPathDict.get(key))) not in ldn:
            return False
    return True


def have_same_key(dict1, dict2):
    for key in dict1.keys():
        if key in dict2:
            return True
    return False


def get_average_value(valueList):
    value = 0.0
    if valueList:
        for item in valueList:
            value += float(item.replace(",", ""))
        average = value / len(valueList)
        return str(round(average, 3))
    return ""


def transfer_ldn_to_key_path(ldn):
    nodeStrList = ldn.split(',')
    keyPathDict = {}
    for nodeStr in nodeStrList:
        nodeElementList = nodeStr.split('=')
        keyPathDict.update({nodeElementList[0]: nodeElementList[1]})
    return keyPathDict


def get_value_by_path(data, path):
    return reduce(lambda d, k: d.get(k) if d else None, path, data)


def get_target_value(d, key):
    if key in d:
        return d[key]
    for k, v in d.items():
        if isinstance(v, dict):
            result = get_target_value(v, key)
            if result is not None:
                return result
    return None


def trans_refLdn_to_dict(refLdn: str) -> list[dict]:  # 将mo的以ref为开头的属性值转化为字典列表
    return [str_to_dict(ldn) for ldn in refLdn.split(';')]


# 将query_mo的结果中的所有ldn（包括以ref为开头的属性中的ldn）转化为一个字典列表
def trans_query_rlt_to_ldn_list(queryRlts: list[dict]) -> list[dict]:
    rlt = []
    for query in queryRlts:
        ldnDict = str_to_dict(query.get('ldn'))
        rlt.append(ldnDict)
        for attrName in query:
            if attrName.startswith('ref'):
                for refLdn in trans_refLdn_to_dict(query.get(attrName)):
                    rlt.append(refLdn)
    return rlt


def generate_ldn_by_query_rlt(ldnList: list[dict], refQueryLdnList: list[dict]) -> list[str | None]:
    rlt = []
    for ldnDict in ldnList:
        for refQueryLdnDict in refQueryLdnList:
            isSubset = all(refQueryLdnDict.get(key) ==
                           val for key, val in ldnDict.items())
            isSuperset = all(ldnDict.get(key) == val for key,
            val in refQueryLdnDict.items())
            if isSubset or isSuperset:
                rlt.append(dict_to_str(ldnDict))
    return rlt


def ref_as_key_mo_path(mocName: str, refResult: dict) -> bool:
    refName = "ref" + mocName
    if refName in refResult:
        return True
    return False


def filter_ldn_by_moc(mocName: str, ldn: str) -> str:
    """
    根据 mocName 来过滤 ldn，示例：
        mocName=A,ldn="A=1,B=2,C=3"  return "A=1"
        mocName=B,ldn="A=1,B=2,C=3"  return "A=1,B=2"
        mocName=C,ldn="A=1,B=2,C=3"  return "A=1,B=2,C=3"
        mocName=D,ldn="A=1,B=2,C=3"  return "A=1,B=2,C=3"
    """
    if not mocName or not ldn:
        return ldn

    ldnPairs = ldn.split(',')
    filteredPairs = []

    for pair in ldnPairs:
        # 将键值对添加到过滤列表中
        filteredPairs.append(pair)
        # 如果键是我们要找的mocName，则停止添加
        if pair.startswith(mocName + "="):
            break

    return ','.join(filteredPairs)


def merge_key_mo_path_and_ldn(keyMoPath: dict, ldn: str) -> str:
    """
    合并 keyMoPath 和 ldn，示例：
        keyMoPath={C: 3},ldn="A=1,B=2"  return "A=1,B=2,C=3"    简单合并
        keyMoPath={C: 4},ldn="A=1,B=2,C=3"  return "A=1,B=2,C=4"    有冲突取keyMoPath
    """
    if not keyMoPath or not ldn:
        return ldn

    ldnDict = transfer_ldn_to_key_path(ldn)
    return dict_to_str({**ldnDict, **keyMoPath})


def build_key_paths_by_ref_result(mocName: str, refResultList: list[dict]) -> list[dict]:
    keyMoPaths = []
    refName = "ref" + mocName
    for refResult in refResultList:
        refLdn = refResult.get(refName, "")
        refLdnDict = str_to_dict(refLdn)
        keyMoPaths.append(refLdnDict)
    return keyMoPaths


def build_filters_by_ref_result(refResultList: list[dict]) -> list[dict]:
    filters = []
    refName = "ref" + refResultList[0]["mocName"]
    for refResult in refResultList:
        refLdn = refResult.get("ldn", "")
        filters.append({refName: refLdn})
    return filters


def unique_list(data_list: list) -> list:
    '''
    对列表元素进行去重，并去除空列表
    '''
    seen = set()
    unique_list = []
    for d in data_list:
        # 跳过空列表
        if d == []:
            continue
        # 将字典转换为排序后的JSON字符串作为唯一标识
        serial_d = json.dumps(d, sort_keys=True)
        if serial_d not in seen:
            unique_list.append(d)
            seen.add(serial_d)
    return unique_list


def append_to_dic(dict, keyList, valueList):
    if len(keyList) != len(valueList):
        raise 'the number of key does not equal value'
    for i in range(0, len(keyList)):
        if is_not_empty_dict(keyList[i]):
            dict[keyList[i]] = valueList[i]
    return dict


if __name__ == '__main__':
    print(merge_key_mo_path_and_ldn({'C': 3}, "A=1,B=2"))  # 应输出: "A=1,B=2,C=3"
    print(merge_key_mo_path_and_ldn({'C': 4}, "A=1,B=2,C=3"))  # 应输出: "A=1,B=2,C=4"
    print(merge_key_mo_path_and_ldn({'C': 4, 'D': 5}, "A=1,B=2,C=3"))  # 应输出: "A=1,B=2,C=4,D=5"

    # print(filter_ldn_by_moc('A', "A=1,B=2,C=3"))  # 应输出: "A=1"
    # print(filter_ldn_by_moc('B', "A=1,B=2,C=3"))  # 应输出: "A=1,B=2"
    # print(filter_ldn_by_moc('C', "A=1,B=2,C=3"))  # 应输出: "A=1,B=2,C=3"
    # print(filter_ldn_by_moc('D', "A=1,B=2,C=3"))  # 应输出: "A=1,B=2,C=3"

    # a = [{"ManagedElementType": "ITBBU", "SubNetwork": "16016", "ManagedElement": "3769",
    #       "ldn": "TransportNetwork=1,Sctp=1", "lastModifiedTime": "2023-05-20T15:20:02.832+08:00", "moId": "1",
    #       "localPort": "3769", "refIp": "TransportNetwork=1,Ip=1", "remotePort": "38412", "remoteIp": "**************",
    #       "primaryPathNo": "1", "inOutStreamNum": "3", "dscp": "48", "radioMode": "8192", "maxInitRetran": "5",
    #       "maxPathRetran": "5", "maxAssoRetran": "10", "minRTO": "500", "maxRTO": "5000", "hbInterval": "5000",
    #       "adminState": "", "sctpControlState": "", "operState": "0", "availStatus": "normal", "assoType": "1",
    #       "groupId": "", "initRTO": "1000", "delayAckTime": "10", "congestEndure": "65535", "refBandwidthResource": "",
    #       "refDtls": "", "userLabel": "Sctp-1"},
    #      {"ManagedElementType": "ITBBU", "SubNetwork": "16016", "ManagedElement": "3769",
    #       "ldn": "TransportNetwork=1,Sctp=3", "lastModifiedTime": "2023-05-20T15:20:02.832+08:00", "moId": "3",
    #       "localPort": "5000", "refIp": "TransportNetwork=1,Ip=1", "remotePort": "38412", "remoteIp": "**************",
    #       "primaryPathNo": "1", "inOutStreamNum": "3", "dscp": "48", "radioMode": "8192", "maxInitRetran": "5",
    #       "maxPathRetran": "5", "maxAssoRetran": "10", "minRTO": "500", "maxRTO": "5000", "hbInterval": "5000",
    #       "adminState": "", "sctpControlState": "", "operState": "0", "availStatus": "normal", "assoType": "1",
    #       "groupId": "", "initRTO": "1000", "delayAckTime": "10", "congestEndure": "65535", "refBandwidthResource": "",
    #       "refDtls": "", "userLabel": "Sctp-3"},
    #      {"ManagedElementType": "ITBBU", "SubNetwork": "16016", "ManagedElement": "3769",
    #       "ldn": "TransportNetwork=1,Sctp=2", "lastModifiedTime": "2023-05-20T15:20:02.832+08:00", "moId": "2",
    #       "localPort": "38422", "refIp": "TransportNetwork=1,Ip=1", "remotePort": "38423", "remoteIp": "**************",
    #       "primaryPathNo": "1", "inOutStreamNum": "3", "dscp": "48", "radioMode": "8192", "maxInitRetran": "5",
    #       "maxPathRetran": "5", "maxAssoRetran": "10", "minRTO": "500", "maxRTO": "5000", "hbInterval": "5000",
    #       "adminState": "", "sctpControlState": "", "operState": "0", "availStatus": "normal", "assoType": "2",
    #       "groupId": "", "initRTO": "1000", "delayAckTime": "10", "congestEndure": "65535", "refBandwidthResource": "",
    #       "refDtls": "", "userLabel": "Sctp-2"}]
    # b = {"Sctp": "1"}
    # ret = get_mo_filter_with_key_path(a, b)
    # print(ret)
