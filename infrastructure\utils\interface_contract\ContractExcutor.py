# -*- encoding: utf-8 -*-
"""
@File    :   Contract.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import copy
from itertools import chain
from infrastructure.utils.interface_contract.CmdExcutor import CmdExcutor
from infrastructure.utils.interface_contract.FuncExcutor import FuncExcutor
from infrastructure.utils.interface_contract.ProcessStack import ProcessStack
from infrastructure.utils.interface_contract.ContractParser import ContractParser
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister
from infrastructure.utils.interface_contract.ContractResultRepo import ContractResultRepo


class ContractExcutor(object):
    '''
    classdocs
    '''

    def __init__(self, contractPath, paraDict, objSet):
        '''
        Constructor
        '''
        self._finalContract = ContractParser().sub_contract_para(contractPath, paraDict)
        self._globalConfig = ContractParser().get_default_config(contractPath)
        self._processStack = ProcessStack()
        self._objSet = objSet

    async def excute(self, exitTimes=0, isRelogin=True, containerIndex=-1, throwException=True):
        cmdResult = None
        contractDefaultConfig = copy.deepcopy(self._finalContract)
        contracts = contractDefaultConfig.pop('contract')
        contractDefaultConfig = dict(chain(self._globalConfig.items(), contractDefaultConfig.items()))
        execTool = None
        for contract in contracts:
            cmdDict = dict(chain(contractDefaultConfig.items(), contract.items()))
            newExecTool = ContractRegister().find(cmdDict.get('tool'))
            if newExecTool != execTool and isRelogin:
                await self._objSet.login()
                execTool = newExecTool
            excutor = self._choose_executor_by_type(cmdDict)
            try:
                cmdResult = await excutor.excute(containerIndex=containerIndex)
            except:
                self._exit_proc(-1, contractDefaultConfig)
                if throwException:
                    raise
            if cmdDict.get('result'):
                ContractResultRepo().add(cmdDict.get('result'), cmdResult)
        self._exit_proc(exitTimes, contractDefaultConfig)
        return cmdResult

    def get_contract(self):
        return ContractParser.contract()

    def _exit_proc(self, exitTimes, contractDefaultConfig):
        exitTimes = self._processStack.size() if exitTimes == -1 else exitTimes
        cmd = {'expected': '#', 'timeout': 2, 'cmd': 'exit'}
        exitCmd = dict(chain(contractDefaultConfig.items(), cmd.items()))
        for _ in range(int(exitTimes)):
            try:
                excutor = self._choose_executor_by_type(exitCmd)
                excutor.excute()
            except:
                pass

    def _choose_executor_by_type(self, cmdDict):
        if cmdDict.get('type') == 'CMD':
            paramsList = ['tool', 'cmd', 'expected', 'timeout', 'waitSecs', 'failedRetried', 'isFailedContinue', 'proc',
                          'containerName', 'exeTimes', 'isRaiseNotMatchException']
            excutor = CmdExcutor(self._generate_params_dict(cmdDict, paramsList))
        elif cmdDict.get('type') == 'FUNC':
            paramsList = ['tool', 'func', 'waitSecs', 'failedRetried', 'isFailedContinue', 'funcParas']
            excutor = FuncExcutor(self._generate_params_dict(cmdDict, paramsList))
        else:
            excutor = ContractExcutor(cmdDict.get('contractPath'), cmdDict.get('contractParas'))
        excutor.stack = self._processStack
        return excutor

    def _generate_params_dict(self, cmdDict, keyList):
        returnDict = {}
        for key in keyList:
            returnDict.update({key: cmdDict.get(key, None)})
        return returnDict


if __name__ == '__main__':
    vswdict = {"id": "GNBVSW1", "type": "DEVICE", "subtype": "VSW",
               "attributes": {"ip": "*************", "port": "23", "slot": "1"},
               "resources": [{"id": "service", "type": "PORT", "subtype": "ETH",
                              "attributes": {"serviceIp": "************", "servicePort": "22", "username": "itran",
                                             "password": "Itran_2430!@#", "channelType": "Ssh"}}]
               }
    contractPath = 'Comm.restart_container'
    params = {"gnbAlias": "gnb", "boardType": "VSW", "dockername": "hccm"}
    print("2223333", ContractExcutor(contractPath, params).excute())
