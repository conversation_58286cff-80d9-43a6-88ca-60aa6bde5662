from infrastructure.logger.logger import logger
import time
import asyncio

def retries_on_exception(maxTries, hook=None, hookArg=None, hookGrainSize=1, exceptions=(Exception,),
                         logExceptions=False, everyTryDelaySecs=0):
    def dec(func):

        if asyncio.iscoroutinefunction(func):
            async def f2(*args, **kwargs):
                hookGrainSizeInit = hookGrainSize
                tries = reversed(range(maxTries))
                for triesRemaining in tries:
                    hookGrainSizeInit = hookGrainSizeInit - 1
                    try:
                        return await func(*args, **kwargs)
                    except exceptions as exceptionMsg:
                        if logExceptions:
                            logger.warn(exceptionMsg)
                        if triesRemaining > 0:
                            if hookGrainSizeInit == 0:
                                hookGrainSizeInit = hookGrainSize
                                if hook is not None:
                                    if hookArg is not None:
                                        if asyncio.iscoroutinefunction(hook):
                                            await hook(hookArg)
                                        else:
                                            hook(hookArg)
                                    else:
                                        if asyncio.iscoroutinefunction(hook):
                                            await hook()
                                        else:
                                            hook()
                            await asyncio.sleep(everyTryDelaySecs)
                        else:
                            logger.error(
                                'try ' + str(maxTries) + ' times, but execute ' + func.__name__ + ' is still failing')
                            raise
                    else:
                        break

            return f2
        else:
            def f2(*args, **kwargs):
                hookGrainSizeInit = hookGrainSize
                tries = reversed(range(maxTries))
                for triesRemaining in tries:
                    hookGrainSizeInit = hookGrainSizeInit - 1
                    try:
                        return func(*args, **kwargs)
                    except exceptions as exceptionMsg:
                        if logExceptions:
                            logger.warn(exceptionMsg)
                        if triesRemaining > 0:
                            if hookGrainSizeInit == 0:
                                hookGrainSizeInit = hookGrainSize
                                if hook is not None:
                                    if hookArg is not None:
                                        hook(hookArg)
                                    else:
                                        hook()
                        else:
                            logger.error('try ' + str(maxTries) + ' times, but excute ' + func.__name__ + ' is still fail')
                            raise
                        time.sleep(everyTryDelaySecs)
                    else:
                        break

            return f2
    return dec


def retries_on_flag(maxTries, hook=None, hookArg=None, hookGrainSize=1, flag=False, isRaiseException=True,
                    everyTryDelaySecs=0):
    def dec(func):
        if asyncio.iscoroutinefunction(func):
            async def f2(*args, **kwargs):
                hookGrainSizeInit = hookGrainSize
                tries = reversed(range(maxTries))
                for triesRemaining in tries:
                    hookGrainSizeInit = hookGrainSizeInit - 1
                    result = await func(*args, **kwargs)
                    if result == flag:
                        if triesRemaining > 0:
                            if hookGrainSizeInit == 0:
                                hookGrainSizeInit = hookGrainSize
                                if hook is not None:
                                    if hookArg is not None:
                                        if asyncio.iscoroutinefunction(hook):
                                            await hook(hookArg)
                                        else:
                                            hook(hookArg)
                                    else:
                                        if asyncio.iscoroutinefunction(hook):
                                            await hook()
                                        else:
                                            hook()
                            else:
                                if isRaiseException:
                                    raise Exception(
                                        'try ' + str(
                                            maxTries) + ' times, but execute ' + func.__name__ + ' is still failing')
                                else:
                                    logger.error(
                                        'try ' + str(
                                            maxTries) + ' times, but execute ' + func.__name__ + ' is still failing')
                                    return result
                            await asyncio.sleep(everyTryDelaySecs)
                        else:
                            if isRaiseException:
                                raise Exception(
                                    'try ' + str(
                                        maxTries) + ' times, but execute ' + func.__name__ + ' is still failing')
                            else:
                                logger.error(
                                    'try ' + str(
                                        maxTries) + ' times, but execute ' + func.__name__ + ' is still failing')
                                return result
                    else:
                        return result
            return f2
        else:
            def f2(*args, **kwargs):
                hookGrainSizeInit = hookGrainSize
                tries = reversed(range(maxTries))
                for triesRemaining in tries:
                    hookGrainSizeInit = hookGrainSizeInit - 1
                    result = func(*args, **kwargs)
                    if result == flag:
                        if triesRemaining > 0:
                            if hookGrainSizeInit == 0:
                                hookGrainSizeInit = hookGrainSize
                                if hook is not None:
                                    if hookArg is not None:
                                        hook(hookArg)
                                    else:
                                        hook()
                        else:
                            if isRaiseException:
                                raise Exception(
                                    'try ' + str(maxTries) + ' times, but excute ' + func.__name__ + ' is still fail')
                            else:
                                logger.error(
                                    'try ' + str(maxTries) + ' times, but excute ' + func.__name__ + ' is still fail')
                                return result
                        time.sleep(everyTryDelaySecs)
                    else:
                        return result

            return f2

    return dec


def retries_during_time(durationSecs=100, everyTryDelaySecs=0, flag=False, isRaiseException=True, maxTries=10000,
                        isSim=False, returnType='0'):
    def dec(func):
        if asyncio.iscoroutinefunction(func):
            async def f2(*args, **kwargs):
                if isSim:
                    return True
                endTime = int(time.time()) + durationSecs
                while int(time.time()) < endTime:
                    result = await func(*args, **kwargs)
                    if result == flag:
                        await asyncio.sleep(everyTryDelaySecs)
                    else:
                        return ('0' == returnType and [True] or [result])[0]
                if isRaiseException:
                    raise Exception(
                        'try several times during {0} secs but excute {1} still failed'.format(durationSecs,
                                                                                               func.__name__))
                return False

            return f2
        else:
            def f2(*args, **kwargs):
                if isSim:
                    return True
                endTime = int(time.time()) + durationSecs
                for _ in range(0, maxTries):
                    result = func(*args, **kwargs)
                    if result == flag:
                        time.sleep(everyTryDelaySecs)
                    else:
                        return ('0' == returnType and [True] or [result])[0]
                    if int(time.time()) >= endTime:
                        break
                if isRaiseException:
                    raise Exception(
                        'try several times during {0} secs but excute {1} still failed'.format(durationSecs, func.__name__))
                return False

            return f2

    return dec
