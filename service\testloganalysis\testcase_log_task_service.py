"""
@Author: <EMAIL>
@Date: 2025/4/22 下午6:46
@File: testcase_log_task_service.py
@Description: 
"""
from service.testloganalysis.testcaselogDto import LogQueryInfo, ManualConfigInfo
from service.testloganalysis.QueryTestcaseLog import QueryTestCaseLogDto
from domain.repository.testcaselogrepository.LogAnalysisRepository import TestCaseLogRepository


class TestcaseLogAnalysisService():
    def query_testcase_log(self, log_info: LogQueryInfo):
        filters = QueryTestCaseLogDto().from_query_testcase_log(log_info.dict())
        return TestCaseLogRepository().find(filters, page_size=log_info.pageSize, page_no=log_info.curPage)

    def manual_testcase_analysis_result(self, manual_info: ManualConfigInfo):
        condition = {"task_id": manual_info.task_id, "testcase_name": manual_info.testcase_name}
        return TestCaseLogRepository().update_manual(condition, self.get_args(manual_info))

    def get_args(self, manual_info: ManualConfigInfo):
        attrs = {}
        for attr_name, value in manual_info.dict().items():
            if attr_name not in ["task_id", "testcase_name"] and value:
                attrs.update({attr_name: value})
        if len(attrs) == 0:
            raise Exception(f"参数缺失，请校验！")
        return attrs
