# coding=utf-8

import codecs
import datetime
from functools import wraps
import logging
import hashlib
import os
import re
import time
import sys
import traceback


base = [str(x) for x in range(10)] + [chr(x) for x in range(ord('A'), ord('A') + 6)]


def make_md5(rawStr):
    m1 = hashlib.md5()
    m1.update(rawStr.encode("utf-8"))
    return m1.hexdigest()


def gen_list(var):
    if isinstance(var, list):
        varList = var
    else:
        varList = []
        if var:
            varList = [var]
    return varList

def write_file(path, content):
    file_object = codecs.open(path, "w+", "utf-8", buffering=0)
    file_object.write(content)
    file_object.flush()
    file_object.close()


def parse_time(timeNum, timeFormat="%Y-%m-%d %H:%M:%S"):
    timeStamp = timeNum / 1000
    timeArray = time.localtime(timeStamp)
    return datetime.datetime.strptime(time.strftime(timeFormat, timeArray), timeFormat)


def local2utc(local_st, timeFormat="%Y-%m-%dT%H:%M:%SZ"):
    time_struct = time.mktime(local_st.timetuple())
    utc_st = datetime.datetime.utcfromtimestamp(time_struct)
    return utc_st.strftime(timeFormat)


def convert_to_relative_path(absolutePath, splitStr):
    splitStr = splitStr.lstrip('/').replace('\\', '/')
    absolutePath = absolutePath.replace('\\', '/')
    match = re.search(splitStr + '.*', absolutePath)
    if match:
        return match.group()
    return absolutePath


def make_dir(path):
    isExist = os.path.exists(path)
    if not isExist:
        os.makedirs(path)


def convert_url_args(argsDict):
    urlArgs = ""
    for key, value in argsDict.items():
        urlArgs = urlArgs + key + "=" + value + "&"
    return urlArgs.rstrip("&")


def format_datetime(datetimeStr, timeFormat='%Y-%m-%dT%H:%M:%S+08:00'):
    moduel = re.compile(r'\.\d+')
    return datetime.datetime.strptime(re.sub(moduel, '', datetimeStr), timeFormat)


def delete_file(filePath):
    if os.path.exists(filePath):
        os.remove(filePath)


def fn_timer(function):
    @wraps(function)
    def function_timer(*args, **kwargs):
        t0 = time.time()
        result = function(*args, **kwargs)
        t1 = time.time()
        logging.info("Total time running %s: %s seconds" %
                     (function.func_name, str(t1 - t0)))
        return result
    return function_timer


def dec2bin(string_num):
    num = int(string_num)
    mid = []
    while True:
        if num == 0:
            break
        num, rem = divmod(num, 2)
        mid.append(base[rem])
    return ''.join([str(x) for x in mid[::-1]])


def hex2dec(string_num):
    return str(int(string_num.upper(), 16))


def hex2bin(string_num):
    return dec2bin(hex2dec(string_num.upper()))


def convert_list2dic(fieldList):
    fieldDic = {}
    map(lambda field: fieldDic.update({field: ''}), fieldList)
    return fieldDic


def get_object_size(obj):
    return round(sys.getsizeof(obj)/1024/1024.0,2)


def check_str_contain_chinese(checkStr):
    try:
        if not isinstance(checkStr, unicode):
            checkStr = checkStr.decode('utf-8')
        for ch in checkStr:
            if u'\u4e00' <= ch <= u'\u9fff':
                return True
    except Exception as e:
        traceback.print_exc()
        logging.warning('checkStr code error!')
    return False


def map_ch_analysis_type_to_en(analysisType, mapAnalysisType):
    if check_str_contain_chinese(analysisType):
        logging.info(u'***analysisType: {0} contains chinese, map chinese analysisType to "analysis_result"'.format(analysisType))
        return mapAnalysisType
    return analysisType

# print make_md5('liufan刘凡')
# print get_md5_id(u'liufan刘凡')
