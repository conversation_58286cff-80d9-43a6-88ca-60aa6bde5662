# 全局变量列表，务必在main_entry中加载，在写时加上global 声明
import requests
from requests.adapters import HTTPAdapter
from urllib3 import Retry

from infrastructure.const.common import REQUEST_MAX_RETRIES, DATE

# 会话
session = requests.Session()
session.verify = False
adapter = HTTPAdapter(pool_connections=50, max_retries=Retry(total=REQUEST_MAX_RETRIES,
                                                             status_forcelist=[408, 429, 500, 501, 502, 503, 504],
                                                             allowed_methods=["GET", "PUT", "DELETE", "POST"]))
