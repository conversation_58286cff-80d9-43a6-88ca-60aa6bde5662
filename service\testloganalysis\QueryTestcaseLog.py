"""
@Author: <EMAIL>
@Date: 2025/4/22 下午7:44
@File: QueryTestcaseLog.py
@Description: 
"""


class QueryTestCaseLogDto():
    def from_query_testcase_log(self, log_record_request: dict) -> dict:
        filters = {}
        beg_date = log_record_request.get("startTime", '')
        end_date = log_record_request.get("endTime", '')
        date_dict = {}
        if beg_date:
            date_dict.update({"$gte": beg_date + " 00:00:00"})
        if end_date:
            date_dict.update({"$lte": end_date + " 23:59:59"})
        if date_dict:
            filters.update({"createdTime": date_dict})
        for argName in log_record_request.keys():
            if argName in ["startTime", "endTime", "curPage", "pageSize"]:
                continue
            argValue = log_record_request.get(argName, '')
            if argValue and argName != "subtype":
                filters.update({argName: argValue})
        return filters
