import os
import logging
import sys
sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))
from locales import MsgId
from infrastructure.utils.RestfulDevice import RestfulDevice
from infrastructure.utils.Singleton import Singleton
from infrastructure.utils.exception import TdlException
from infrastructure.utils.AdaptExecutor import AdaptExecutor
from infrastructure.utils.Env import Env


@Singleton
class Tdl(RestfulDevice, AdaptExecutor):
    def __init__(self):
        config = Env.get_config().TDL.model_dump()
        logging.debug("TDL config:%s" % config)
        interface_folder = os.path.join(os.path.split(__file__)[0], "interface_jsons")
        print(interface_folder)
        RestfulDevice.__init__(self, config, interface_folder)
        self._attrs = {"host": self._deviceIp,
                       "port": self._devicePort}
        self._attrs.update(config)
        AdaptExecutor.__init__(self, MsgId.TDL_NOT_CONN, MsgId.TDL_ERR_CODE, MsgId.TDL_ERR_BASE,
                               TdlException, self._adapt_and_execute_cmd)


if __name__=="__main__":
    data = {
    "actionId": "86db0578995996b712f1ee580c393c83",
    "para": {},
    "envId": "RAN3-上海高频CI团队-VAT1014",
    "config": {
        "UME-10_226_209_230": {
            "attr": {
                "ip": "**************",
                "port": "28001",
                "username": "g5_ume_ci",
                "password": "Ume_5gNr_Ci",
                "dataAreaId": "VAT1014"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "UME-10_226_209_230",
            "type": "UME"
        },
        "GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "ip": "*************",
                "meId": "8110",
                "gnbId": "8110",
                "subNetwork": "16016",
                "plmn": "460-11",
                "plmnList": [
                    "460-11"
                ],
                "gnbIdLength": "24"
            },
            "links": [
                "UME-10_226_209_230",
                "CELL-26-GNB-8110-460-11-10_230_21_100",
                "CELL-28-GNB-8110-460-11-10_230_21_100",
                "CELL-25-GNB-8110-460-11-10_230_21_100",
                "CELL-27-GNB-8110-460-11-10_230_21_100"
            ],
            "id": "GNB-8110-460-11-10_230_21_100",
            "type": "GNB"
        },
        "CELL-26-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "26",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27150.0",
                "pci": "26",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-26-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-28-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "28",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27349.92",
                "pci": "28",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-28-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-25-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "25",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27050.04",
                "pci": "25",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-25-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "CELL-27-GNB-8110-460-11-10_230_21_100": {
            "attr": {
                "cellId": "27",
                "bandWith": "100",
                "cellStatus": "ACTIVE",
                "freq": "27249.96",
                "pci": "27",
                "plmn": "460-11"
            },
            "links": [
                "GNB-8110-460-11-10_230_21_100"
            ],
            "id": "CELL-27-GNB-8110-460-11-10_230_21_100",
            "type": "CELL"
        },
        "UE-1-5126-10_230_69_122": {
            "attr": {
                "ermsSerialNo": 1,
                "controlIp": "*************",
                "controlPort": "5126",
                "serviceMgrPort": "",
                "ueId": "",
                "ueType": "HFQUALCOMM",
                "name": "CPE-高频",
                "telphoneNumber": None,
                "ueAttr": {
                    "cpeId": "1",
                    "imsi": "460087960001536",
                    "ues": [
                        "1"
                    ],
                    "ueGroups": [
                        "1"
                    ],
                    "workDir": ""
                }
            },
            "links": [],
            "id": "UE-1-5126-10_230_69_122",
            "type": "UE"
        },
        "BizCode": {
            "ip": "",
            "port": "",
            "type": "BizCode"
        }
    },
    "refs": [],
    "actionType": "composite"
}
    import json
    print(json.loads(Tdl().adapt_and_execute_cmd("execute", {"body": data}).text))
