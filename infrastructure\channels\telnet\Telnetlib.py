# coding=utf-8
import socket
import threading
from telnetlib import Telnet



class Telnetlib(Telnet):

    def __init__(self, host, port, timeout=socket._GLOBAL_DEFAULT_TIMEOUT):
        Telnet.__init__(self, host, port, timeout)
        self._readLock = threading._allocate_lock()
        self._logger = None

    def listener(self):
        while 1:
            try:
                self._readLock.acquire()
                self.read_eager()
                self._readLock.release()
            except EOFError:
                return

    def fill_rawq(self):
        """Fill raw queue from exactly one recv() system call.

        Block if no data is immediately available.  Set self.eof when
        connection is closed.

        """
        if self.irawq >= len(self.rawq):
            self.rawq = ''
            self.irawq = 0
        # The buffer size should be fairly small so as to avoid quadratic
        # behavior in process_rawq() above
        try:
            buf = self.sock.recv(50)
            if self._logger is not None:
                self._logger.write(buf)
            self.msg("recv %r", buf)
            self.eof = (not buf)
            self.rawq = self.rawq.encode() + buf
        except socket.timeout:
            return
