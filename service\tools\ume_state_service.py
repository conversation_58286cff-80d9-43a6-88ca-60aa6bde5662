from domain.monitor.UmeStateCheck import *
from service.tools.UmeService import *
from datetime import datetime, timedelta
import time


class ume_state_service(object):

    @staticmethod
    def query_ume_alarm(data):
        MAX_RETRIES = 5
        retry_count = 0
        env_id = data.get("env_id")
        me_id = data.get("me_id")
        # 设置目标时间为今天早上2点钟
        # target_date = datetime.combine(datetime.now().date(), datetime.min.time()) + timedelta(hours=2)
        # 设置目标时间为前一天的早上5点钟
        target_date = datetime.combine(datetime.now().date() - timedelta(days=1), datetime.min.time()) + timedelta(hours=5)
        print(target_date)
        alarms = UmeService.get_current_alarms(env_id)
        time.sleep(10)

        while retry_count < MAX_RETRIES:
            alarms_info = ActionService.get_execution_info(alarms.execute_result)
            info = alarms_info.execute_info
            print(info)

            if info.get('output') is not None:
                return UmeStateCheck.ume_alarm_check(info, me_id, target_date)

            retry_count += 1
            if retry_count < MAX_RETRIES + 1:
                print(f"Output is None, retrying... (Attempt {retry_count}/{MAX_RETRIES})")
                time.sleep(10)

        return {"result": False, "details": {}}

    @staticmethod
    def query_me_connect(data):
        MAX_RETRIES = 5
        retry_count = 0
        env_id = data.get("env_id")
        me_id = data.get("me_id")
        alarms = UmeService.get_current_alarms(env_id)
        time.sleep(10)

        while retry_count < MAX_RETRIES:
            alarms_info = ActionService.get_execution_info(alarms.execute_result)
            info = alarms_info.execute_info
            print(info)

            if info.get('output') is not None:
                return UmeStateCheck.me_connect_check(alarms_info.execute_info, me_id)

            retry_count += 1
            if retry_count < MAX_RETRIES + 1:
                print(f"Output is None, retrying... (Attempt {retry_count}/{MAX_RETRIES})")
                time.sleep(10)

        return {"result": False, "details": {}}



    @staticmethod
    def query_mts_num(data):
        return UmeStateCheck.mts_num_check(data)