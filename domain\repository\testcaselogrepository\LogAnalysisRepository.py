from infrastructure.db.mongo.Repository import Repository
from domain.model.testcaseloganalyzer.testcaselog import Test<PERSON>aseLog
from infrastructure.utils.DatetimeFormater import get_current_time


class TestCaseLogRepository(Repository):

    def __init__(self):
        super().__init__("log_info")

    def find(self, filters: dict, page_size=10, page_no=1):
        filter_config = [{"$match": filters}, {"$sort": {"createdTime": -1}}]
        review_page_record = self.aggregate(filter_config)
        skip = page_size * (page_no - 1)
        return {"data": review_page_record[skip: skip + page_size], "total": len(review_page_record)}

    def find_template_by_name(self, name, user):
        condition = {"name": {"$regex": name}}
        if user:
            condition.update({"user": user})
        query_result = self._get_by_condition(condition)
        template = []
        for result in query_result:
            template.append({"id": result.get("_id"), "name": result.get("name")})
        return template

    def get_detail(self, uuid):
        condition = {"_id": uuid}
        detail = self._get_by_condition(condition)
        if detail:
            return detail[0]
        return {}

    def save(self, testcaselog: TestCaseLog):
        testcaselog.updatedTime = get_current_time()
        testcaselog.createdTime = get_current_time()
        testcaselog_id = self.insert(testcaselog.dict())
        return True, str(testcaselog_id)

    def _get_by_condition(self, condition):
        return self.query(condition)

    def find_by_id(self, id):
        query_result = self.query({"_id": id})
        if len(query_result) > 0:
            return query_result[0]
        return {}

    def updates_new_field(self, condition, attrDict):
        return self.update_attrs(condition, attrDict)

    def delete(self, task_id: str):
        return super().delete_many({"task_id": task_id})

    def update_manual(self, condition, attrDict):
        return self.update_with_condition(attrDict, condition)
