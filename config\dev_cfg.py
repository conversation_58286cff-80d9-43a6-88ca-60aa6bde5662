import sys, os

sys.path.append(os.path.abspath(f"{sys.path[0]}/.."))
from infrastructure.utils.utils import get_host

CONFIG = {
    "MONGO_DB": {
        "db_replset": "************:27017",
        "name": "ci_log_info",
        "table": "",
        "user": "LOG",
        "password": "LOG_123"
    },

    "ENV_SFTP": {
        "host": "**********",
        "username": "root",
        "password": "TD-test123.",
        "port": 22
    },

    # "ES": {
    #     "scheme": "https",
    #     "ip": "*************",
    #     "port": 9200,
    #     "username": "elastic",
    #     "password": "Zenap_123"
    # },

    # "ES": {
    #     "scheme": "https",
    #     "ip": "**************",
    #     "port": 9200,
    #     "username": "elastic",
    #     "password": "Ran_test@1"
    # },

    "ES": {
        "scheme": "https",
        "ip": "************",
        "port": 9200,
        "username": "elastic",
        "password": "Zenap_123"
    },

    "REDIS": {
        "ip": "************",
        "port": 6379,
        "password": 'Zenap_123',
        "ci_agent_queue": f"task:ciagents:test"
    },

    "TDL": {
        "port": 3303,
        "host": "zxmte.zte.com.cn",
        "protocol": "https"
    }
}

if __name__ == "__main__":
    print(CONFIG["REDIS"])
