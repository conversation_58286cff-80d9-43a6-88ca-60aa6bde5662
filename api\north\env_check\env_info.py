from fastapi import APIRouter
from domain.repository.es_env import EsBizCoreEnv
from infrastructure.utils.ResultFormat import ResultFormat

router = APIRouter(prefix="/env_info")


@router.get("/principal")
async def get_env_principal(env_id: str):
    rf = ResultFormat()
    try:
        es_biz_core = EsBizCoreEnv()
        # 查询指定env_id的数据
        result = es_biz_core.query_by_filter_without_sort({"env_id": env_id})

        # result是一个元组，第一个元素是总数，第二个元素是数据列表
        total, data_list = result

        # 检查是否有数据
        if data_list and len(data_list) > 0:
            principal = data_list[0].get('principal', '')
            rf.data = {"principal": principal}
        else:
            rf.result = False
            rf.fail_reason = f"No environment found with env_id: {env_id}"

    except Exception as e:
        rf.result = False
        rf.fail_reason = f"Failed to get principal: {str(e)}"

    return rf.format()


@router.post("/batch_principal")
async def batch_get_env_principal(env_ids: list[str]):
    rf = ResultFormat()
    try:
        es_biz_core = EsBizCoreEnv()
        result_dict = {}

        for env_id in env_ids:
            # 查询指定env_id的数据
            result = es_biz_core.query_by_filter_without_sort({"env_id": env_id})

            # result是一个元组，第一个元素是总数，第二个元素是数据列表
            total, data_list = result

            # 检查是否有数据
            if data_list and len(data_list) > 0:
                principal = data_list[0].get('principal', '')
                result_dict[env_id] = principal
            else:
                result_dict[env_id] = ""

        rf.data = result_dict

    except Exception as e:
        rf.result = False
        rf.fail_reason = f"Failed to batch get principals: {str(e)}"

    return rf.format()