import copy
import inspect
import json
import logging
import os
import re
import time
import requests
from infrastructure.const.common import NAME
from locales import MsgId
from infrastructure.utils.RestfulInterface import RestfulInterface
from infrastructure.utils.exception import SystemFailedException
from requests_toolbelt.multipart.encoder import MultipartEncoder

from infrastructure.const.common import X_EMPLOYEE_NO, X_WORKSPACE_ID, X_CUSTOM_ID, X_APP_ID, X_APP_ID_TEST_CENTER

REQ_PRINT_LENGTH = 500
REPSONSE_PRINT_LENGTH = 3000
SPLITE_LIMIT = 100 * 1024
BUFFER_SIZE = 512
requests.packages.urllib3.disable_warnings()
from infrastructure.utils.global_variables import session, adapter
session.mount('https://', adapter)
session.mount('http://', adapter)


class RestfulDevice(object):

    '''
    classdocs
    '''

    def __init__(self, deviceAttrDict, interfaceFolder):
        '''
        Constructor
        '''
        self._deviceIp = deviceAttrDict.get("host")
        self._devicePort = deviceAttrDict.get("port")
        if "https" == deviceAttrDict.get("protocol", "https"):
            self._basicUrl = "https://%(host)s:%(port)s" % (deviceAttrDict)
        else:
            self._basicUrl = "http://%(host)s:%(port)s" % (deviceAttrDict)
        self._attrs = {}
        self.interface = RestfulInterface(interfaceFolder)

    @property
    def attrs(self):
        return self._attrs

    @attrs.setter
    def attrs(self, attrDict):
        if isinstance(attrDict, dict):
            self._attrs.update(attrDict)
        return self.attrs

    def login(self, username, password):
        pass

    def _get_filled_interface_with_attr(self, interfaceKey, attrDict):
        interfaceDict = self.interface.find(interfaceKey)
        tempAttr = copy.deepcopy(self.attrs)
        tempAttr.update(attrDict)
        interfaceStr = json.dumps(interfaceDict) % tempAttr
        interfaceStr = self._handle_struct_variable(interfaceStr, attrDict)
        interfaceDict = json.loads(interfaceStr)
        if interfaceDict.get("bodyEncodeType", None):
            interfaceDict["body"] = interfaceDict["body"].encode(interfaceDict["bodyEncodeType"])
        if interfaceDict.get("bodyNeedDump", False):
            body = interfaceDict.get("body")
            interfaceDict.update({"body": json.dumps(body)})
        return interfaceDict

    def _handle_struct_variable(self, interfaceStr, attrDict):
        interfaceDict = json.loads(interfaceStr)
        if ',' in interfaceDict.get('uri'):
            interfaceDict.update({'uri': interfaceDict.get('uri').replace(',', '%2C')})
            interfaceStr = json.dumps(interfaceDict)
        structVariables = re.findall("#(.+?)#", interfaceStr)
        for variable in structVariables:
            if variable not in attrDict.keys():
                logging.error("Variable {0} Not Found".format(variable))
            interfaceStr = interfaceStr.replace('\"#{0}#\"'.format(variable), json.dumps(attrDict.get(variable, None)))
        return interfaceStr

    def _inspect_args(self, interfaceDict):
        if "uploadFilePath" in interfaceDict:
            inspectArg = inspect.getfullargspec(self.upload_file)
        elif "saveFilePath" in interfaceDict:
            inspectArg = inspect.getfullargspec(self.download_file)
        else:
            inspectArg = inspect.getfullargspec(self.execute_cmd)
        return inspectArg

    def _eval_execute_function(self, interfaceDict, variableList):
        try:
            if "uploadFilePath" in interfaceDict:
                return eval(self._assemble_eval_string("self.upload_file", "variableList", len(variableList)))
            elif "saveFilePath" in interfaceDict:
                return eval(self._assemble_eval_string("self.download_file", "variableList", len(variableList)))
            else:
                return eval(self._assemble_eval_string("self.execute_cmd", "variableList", len(variableList)))
        except Exception as err:
            logging.error(err, exc_info=True)

    def _adapt_and_execute_cmd(self, interfaceKey, attrDict={}):
        interfaceDict = self._get_filled_interface_with_attr(interfaceKey, attrDict)
        inspectArg = self._inspect_args(interfaceDict)
        variablenames = inspectArg.args
        variablenames.pop(0)
        defaults = inspectArg.defaults
        variableList = self._assemble_variables_by_inspect_args(interfaceDict, variablenames, defaults)
        return self._eval_execute_function(interfaceDict, variableList)

    def _assemble_variables_by_inspect_args(self, interfaceDict, variablenames, defaults):
        defaultLen = len(defaults)
        for i in range(defaultLen):
            index = i - defaultLen
            if variablenames[index] not in interfaceDict:
                interfaceDict.update({variablenames[index]: defaults[index]})
        variableList = []
        for variable in variablenames:
            if variable in interfaceDict:
                variableList.append(interfaceDict.get(variable))
            else:
                raise SystemFailedException(MsgId.OTHER_INPUT_PARA_NO_VAR, {NAME: variable})
        return variableList

    def _assemble_eval_string(self, methodName, variableName, variableLen):
        evalStr = methodName + "("
        for i in range(variableLen):
            evalStr += "%s[%d]" % (variableName, i)
            if i != variableLen - 1:
                evalStr += ", "
        evalStr += ")"
        return evalStr

    def execute_cmd(self, uri, method, body, header, timeout=(5, None), isDebugResponse=True):
        def _send_request():
            if logging.getLevelName(logging.root.getEffectiveLevel()) == 'DEBUG':
                wic_start = int(1e9 * time.time())
                response = session.request(method, self._basicUrl + uri, data=body, headers=header, timeout=timeout)
                wic_end = int(1e9 * time.time())
                logging.debug("外部接口 {0} {1} 耗时: {2}ms".format(method, uri, ((wic_end - wic_start) / 1e6)))
            else:
                response = session.request(method, self._basicUrl + uri, data=body, headers=header, timeout=timeout)
            return response

        def _execute_cmd(self, uri, method, body, header, timeout, isDebugResponse):
            logging.debug("Send Request to {0} by {1}".format(uri, method))
            if body and not isinstance(body, MultipartEncoder):
                logging.debug("Request Data: {0}".format(body))
            response = _send_request()
            self._set_response_encoding(response)
            request_time = "%.3f" %(response.elapsed.microseconds / 1000000.0)
            # standard_formatter = StandardFormatter(fmt=STANDARD_FORMAT, syslogtag=TESTLIB_APP, uri=uri, request_method=method,
            #                                        request_body = body,ztedpgssouser = header.get(X_EMPLOYEE_NO),
            #                                        status = response.status_code, request_time=request_time)
            # logging.getLogger().handlers[1].setFormatter(standard_formatter)
            if response.status_code not in [200, 201, 401]:
                logging.warning("Response Status Code:{0}".format(response.status_code))
            if isDebugResponse:
                logging.debug("Response: %s" % (response.text))
            # logging.getLogger().handlers[1].setFormatter(StandardFormatter(fmt=STANDARD_FORMAT))
            return response

        header = self._set_default_headers(header)
        return _execute_cmd(self, uri, method, body, header, timeout, isDebugResponse)

    def _set_default_headers(self, header):
        # gjc整个工作区灰度，临时方案
        if X_WORKSPACE_ID in header:
            header[X_CUSTOM_ID] = header[X_WORKSPACE_ID]
        # 标识为测试中心后台发出请求
        if X_APP_ID in header:
            if X_APP_ID_TEST_CENTER not in header[X_APP_ID].split(","):
                header[X_APP_ID] = header[X_APP_ID] + "," + X_APP_ID_TEST_CENTER
        else:
            header[X_APP_ID] = X_APP_ID_TEST_CENTER

        return header

    def _set_response_encoding(self, response):
        response.encoding = 'utf-8'

    def download_file(self, uri, method, saveFilePath, header, timeout=(60, None), body=None):
        response = self.execute_cmd(uri, method, body, header, timeout, False)
        with open(saveFilePath, 'wb') as fd:
            for chunk in response.iter_content(chunk_size=BUFFER_SIZE):
                fd.write(chunk)
        logging.debug("Download file successful")
        return response

    def upload_file(self, uri, method, uploadFilePath, filedName, header, timeout=(60, None)):
        fields = {filedName: (os.path.split(uploadFilePath)[1], open(uploadFilePath, 'rb'), "application/octet-stream")}
        data = MultipartEncoder(fields=fields)
        header.update({"Content-Type": data.content_type, "isFromWeb": "1"})
        return self.execute_cmd(uri, method, data, header, timeout)