from dataclasses import dataclass, field, asdict
from typing import Any, TypeVar, Generic, Optional, Dict
from domain.model.response.ret_code import RetCode, ExceptionEnum

T = TypeVar('T')


@dataclass
class Response(Generic[T]):
    code: RetCode = field(default_factory=RetCode)
    data: Optional[T] = None
    other: Optional[Any] = None

    @classmethod
    def empty(cls):
        return cls()

    @classmethod
    def build(cls, data: T, other: Any = None):
        return cls(data=data, other=other)

    @classmethod
    def from_exception(cls, enum: ExceptionEnum, data: T = None, other: Any = None, msg: str = None):
        return cls(code=RetCode.from_exception_enum(enum, msg), data=data, other=other)

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
