# coding=utf-8
import re
from infrastructure.utils.Singleton import <PERSON>ton


@Singleton
class StubCmdParser(object):

    def parse(self, serviceName, replaceDict={}):
        self._stubs = {}
        self._operations = {}
        moduleName, stubName = self._pase_service_name(serviceName)
        self._import_stub_module(moduleName)
        serviceOps = self._stubs.get(stubName, [stubName])
        return [self._parse_op(op, replaceDict) for op in serviceOps]

    def _import_stub_module(self, moduleName):
        self._update_cmds('CommonStub')
        self._update_cmds(moduleName)

    def _pase_service_name(self, serviceName):
        serviceNameList = serviceName.split(".")
        if len(serviceNameList) == 1:
            return (None, serviceNameList[0])
        if len(serviceNameList) == 2:
            return (serviceNameList[0], serviceNameList[1])
        raise Exception("serviceName %s invalid" % serviceName)

    def _update_cmds(self, moduleName):
        if not moduleName:
            return
        stubModule = __import__('testlib5g.infrastructure.utility.stub.stubfile.' + moduleName, globals(), locals(), ['STUBS', 'OPERATIONS'])
        stubs = getattr(stubModule, 'STUBS')
        operations = getattr(stubModule, 'OPERATIONS')
        self._stubs.update(stubs)
        self._operations.update(operations)

    def _parse_op(self, op, replaceDict):
        if isinstance(op, dict):
            originOp = op
        else:
            originOp = self._operations.get(op, {})
        parsedOp = {}
        for key, value in originOp.iteritems():
            parsedOp[key] = self._replace(value, replaceDict)
        return parsedOp

    def _replace(self, opStr, replaceDict):
        for key, value in replaceDict.iteritems():
            opStr = re.sub(str(key), str(value), str(opStr))
        return opStr


if __name__ == '__main__':
    print(StubCmdParser().parse('CpeStub.attach', {'_IMSILOW_': '1200'}))
