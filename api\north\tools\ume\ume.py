from fastapi import APIRouter
from api.north.tools.ume.schemas import VersionRollback, VersionUpgrade
from service.tools.UmeService import UmeService

router = APIRouter(prefix="/ume")

@router.post("/version_rollback")
async def verison_rollback(data: VersionRollback):
    return UmeService.version_rollback(data.env_id, data.tar_path, data.group_no)

@router.post("/verison_upgrade")
async def verison_upgrade(data: VersionUpgrade):
    return UmeService.version_upgrade(data.env_id, data.tar_path, data.group_no)

@router.get("/get_mcs_and_bler")
async def get_mcs_and_bler(env_id: str, group_no: str = ""):
    return UmeService.get_mcs_and_bler(env_id, group_no)

@router.get("/get_bs_current_version")
async def get_bs_current_version(env_id: str, group_no: str = ""):
    return UmeService.get_bs_current_version(env_id, group_no)