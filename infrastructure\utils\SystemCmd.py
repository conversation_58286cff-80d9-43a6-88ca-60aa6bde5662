"""
@File: SystemCmd.py
@Author: 许王禾子10333721
@Time: 2024/1/11 上午10:29
@License: Copyright 2022-2030
@Desc: None
"""
import asyncio
from infrastructure.logger.logger import logger

TASK_CANCELLED_ERROR = "task has been cancelled!"


class SystemCmd:

    @staticmethod
    async def run(cmd: str) -> tuple[bool, str]:
        res, msg = False, ""
        process = await asyncio.create_subprocess_shell(cmd)
        logger.info(f"exec cmd: {cmd}")
        try:
            sysCode = await process.wait()
            if sysCode != 0:
                res, msg = False, f"exec fail, sys code != 0, sys code is {sysCode}"
                logger.error(msg)
            else:
                res, msg = True, "exec cmd success"
                logger.info(msg)
        except asyncio.CancelledError:
            process.terminate()
            logger.error(TASK_CANCELLED_ERROR)
            raise asyncio.CancelledError
        except Exception as err:
            res, msg = False, f"exec fail, err: {err}"
            logger.error(msg)

        return res, msg



