from service.tools.pc_state_service import *
from fastapi import APIRouter, Body
import warnings
warnings.filterwarnings(
    action='ignore',
    message='TripleDES has been moved to cryptography.hazmat.decrepit'
)

router = APIRouter(prefix="/pc")

@router.post("/pc_net_info")
async def check_net_state(data: dict = Body(...)):
    return pc_state_service.query_pc_net(data)

@router.post("/pc_update_time")
async def check_net_state(data: dict = Body(...)):
    return pc_state_service.update_pc_time(data)
