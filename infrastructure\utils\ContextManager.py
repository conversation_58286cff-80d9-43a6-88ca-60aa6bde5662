from infrastructure.logger.logger import logger


def teardown(hook=None, hookArg=None):
    def run_teardown(func):
        def _teardown(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(e)
            finally:
                if hook is not None:
                    if hookArg is None:
                        hook()
                    else:
                        hook(hookArg)

        return _teardown

    return run_teardown


def setup(hook=None, *hookArg):
    def run_setup(func):
        def _setup(*args, **kwargs):
            try:
                if hook is not None:
                    if len(hookArg) == 0:
                        hook()
                    else:
                        hook(*hookArg)
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(e)

        return _setup

    return run_setup
