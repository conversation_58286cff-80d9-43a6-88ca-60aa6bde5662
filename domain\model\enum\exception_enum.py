from enum import Enum


class ExceptionEnum(Enum):
    SUCCESS = ("0000", "请求操作成功")
    SYSTEM_FAILED = ("0001", "服务器发生异常")
    AUTH_FAILED = ("0002", "身份校验不通过")
    BUSINESS_FAILED = ("0003", "业务校验不通过")
    VALIDATED_FAILED = ("0004", "参数校验不通过")
    SERVER_FAILED = ("0005", "服务程序发生异常")
    DATA_FAILURE = ("0006", "数据不存在")
    NOT_FOUND = ("0006", "数据不存在")

    def __init__(self, code: str, msg: str):
        self.code = code
        self.msg = msg
