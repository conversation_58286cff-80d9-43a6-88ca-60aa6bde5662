import logging
import os, sys

from pydantic import BaseModel, Field

sys.path.append(os.path.abspath(f"{sys.path[0]}/../.."))


class MongdbInfo(BaseModel):
    db_replset: str = Field(description="mogodb的ip port")
    name: str = Field(description="mogodb的dbname")
    table: str = Field(default="", description="mogodb的table")
    user: str = Field(description="mogodb的用户名")
    password: str = Field(description="mogodb的密码")


class SftpInfo(BaseModel):
    host: str = Field(description="sftp服务器的ip")
    username: str = Field(description="sftp的用户名")
    password: str = Field(description="sftp的密码")
    port: int = Field(default=22, description="sftp端口")


class EsInfo(BaseModel):
    scheme: str = Field(default="http", description="es协议")
    ip: str = Field(description="es的ip")
    port: int = Field(description="es端口")
    username: str = Field(description="es用户名")
    password: str = Field(description="es密码")


class RedisInfo(BaseModel):
    ip: str = Field(description="redis的ip")
    port: int = Field(description="redis端口")
    password: str = Field(description="redis密码")
    ci_agent_queue: str = Field(description="CI代理队列名称")


class TdlInfo(BaseModel):
    port: int = Field(description="TDL端口")
    host: str = Field(description="TDL主机地址")
    protocol: str = Field(default="https", description="通信协议")


class Config(BaseModel):
    MONGO_DB: MongdbInfo = Field(description="mongodb信息")
    ENV_SFTP: SftpInfo = Field(description="sftp信息")
    ES: EsInfo = Field(description="es信息")
    REDIS: RedisInfo = Field(description="redis信息")
    TDL: TdlInfo = Field(description="TDL信息")

