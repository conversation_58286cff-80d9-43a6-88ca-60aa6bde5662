#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/6/6 14:33
# <AUTHOR> 10263601

import inspect
from dataclasses import dataclass
from pydantic import BaseModel
from typing import Annotated, Callable, get_args, get_origin


@dataclass
class Annotation:
    model: str
    attrName: str
    # attrName: str | list[str]


class AnnotationParser:

    def __init__(self, func: Callable):
        self.func = func
        self.annotations = {}

    def generate_annotated_paras_annotation(self):
        signature = inspect.signature(self.func)

        for paraName, para in signature.parameters.items():
            annotation = para.annotation
            if issubclass(annotation, BaseModel):
                for fieldName, fieldAnnotation in annotation.__annotations__.items():
                    self._update_origin_annotation(fieldName, fieldAnnotation)

    def _update_origin_annotation(self, paraName, annotation):
        if get_origin(annotation) is Annotated:
            annotatedArgs = get_args(annotation)
            model, attrName = annotatedArgs[-1].split(".")
            self.annotations[paraName] = Annotation(model=model, attrName=attrName)


if __name__ == '__main__':
    pass
