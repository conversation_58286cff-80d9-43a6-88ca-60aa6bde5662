import redis
from infrastructure.utils.json2 import marshal
from domain.model.dto.jenkins_job_info import JenkinsJobInfo
from domain.model.dto.env_check import *

from domain.model.dto.logdto import LogJobInfo

pool = redis.ConnectionPool(host='*************', port=6379, password='Zenap_123')
r = redis.Redis(connection_pool=pool)

msg = JenkinsJobInfo(
    env_id="RAN3-T17-10466",
    test_uc_tag="UC-CODE-XYZ",
    test_work_dir="/path/to/workdir",
    test_testcase_dir="/path/to/testcases",
    jenkins_log_name="1003-下行udp灌包流量不足.html",
    jenkins_job_name="smoke123-1013-dailybuild_test_rebuild",
    jenkins_build_number="56",
    service_type="jenkins_job_save",
    full_path="",
    version_test_result="True"
)

msg1 = EnvCheck(
    log_name="",
    env_id="RAN3-上海高频CI团队-VAT1014",
    service_type="env_check",
    log_analysis_results= [{'testcase_name': 'NR3 17 高频.01 CI.01 CI1.02 SA快速冒烟.Micell1VIF4DAAU 1003.下行UDP灌包校验-RCT',
                            'status': 'FAIL (critical)',
                            'message': '下行UDP流量不达标',
                            'detail_msg': "Arguments: [ '2247.9 > 2380' | '\\u4e0b\\u884cUDP\\u6d41\\u91cf\\u4e0d\\u8fbe\\u6807' ] 下行UDP流量不达标",
                            'fail_keywords': ['FOR  ${i} IN RANGE [ 2 | ${totalUeNum}+1 ]  ', 'VAR  ${i} = 2  ', 'BuiltIn . Should Be True ${dlUdp} > ${dlUdpThreshold}, 下行UDP流量不达标 '],
                            'suite_id': 's1-s1-s1-s1-s1',
                            'result': '接入用例失败'}]
)

from datetime import datetime

msg2 = LogJobInfo(
    log_name="smoke123-1013-dailybuild_test_rebuild",
    job_name="smoke123-1013-dailybuild_test_rebuild",
    build_number="56",
    env_id="1013",
    task_id=datetime.now().replace(microsecond=0).strftime('%Y-%m-%dT%H-%M-%S'),
    service_type="local_test"
)

r.lpush("task:ciagents", marshal(msg1))
