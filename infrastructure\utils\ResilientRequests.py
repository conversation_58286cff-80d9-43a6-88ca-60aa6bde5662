"""
@File: ObjectSaver.py
@Author: 许王禾子10333721
@Time: 2024/3/5 下午4:30
@License: Copyright 2022-2030
@Desc: None
"""
import json
import os

import requests
from config.CommonCfg import FAILED_MESSAGE_FILE
from infrastructure.utils.FileLock import FileLock, KEEP_TRYING
from infrastructure.logger.logger import logger


class ResilientRequests:
    def __init__(self):
        self._path = FAILED_MESSAGE_FILE

    def request(self, url, method, **kwargs):
        try:
            response = requests.request(url=url, method=method, **kwargs)
            if not response or not response.ok:
                raise
            return response
        except:
            request_info = {"url": url, "method": method, **kwargs}
            logger.info("request failed, info: ", request_info)
            self._save_request(request_info)

    def _save_request(self, request_info: dict):
        with FileLock(self._path, mode='a', encoding="utf-8") as fl:
            fl.fh.write(json.dumps(request_info) + "\n")


if __name__ == "__main__":
    resilient_req = ResilientRequests()
    resilient_req.request("http://baiduu.com", "GET", timeout=1)
    resilient_req.request("http://baiduu.com", "GET", timeout=1)
    resilient_req.request("http://baiduu.com", "GET", timeout=1)
    resilient_req.request("http://baiduu.com", "GET", timeout=1)
    resilient_req.request("http://baiduu.com", "GET", timeout=1)

