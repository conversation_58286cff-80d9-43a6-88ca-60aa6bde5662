'''
Created on Feb 17, 2016

@author: 10154402
'''
import pymongo
from bson import ObjectId
from pymongo import UpdateOne

from infrastructure.const.Mongo_const import INDEX_ASCENDING, ID, SORT_COLLATION
from infrastructure.utils.Repeat import retries_on_exception


class MongoDbHandler(object):

    def __init__(self, dbcfg, collection=None):
        self._dbcfg = dbcfg
        self._reconnection(collection)

    def create_collection(self, collection):
        return self.__db.create_collection(collection)

    def rename_collection(self, oldname, newname):
        self.__db[oldname].rename(newname)

    def copy_collection(self, originalCollName, duplicateCollName):
        return self.__db[originalCollName].aggregate([{'$out': duplicateCollName}])

    def drop_collection(self, collection):
        return self.__db.drop_collection(collection)

    def set_collection(self, collection):
        if collection:
            self.__collection = self.__db[collection]

    def is_table_exist(self, collection):
        if collection in self.__db.collection_names():
            return True
        return False

    def create_index(self, index, expireAfterSeconds=None):
        if expireAfterSeconds is None:
            self.__collection.create_index([(index, INDEX_ASCENDING)])
        else:
            self.__collection.create_index([(index, INDEX_ASCENDING)], expireAfterSeconds=expireAfterSeconds)

    def create_multi_fields_index(self, index_fields, name, unique=False, expire_sec: int = 0):
        if not expire_sec:
            self.__collection.ensure_index([(index, INDEX_ASCENDING) for index in index_fields], unique=unique,
                                           name=name)
        else:
            self.__collection.ensure_index([(index, INDEX_ASCENDING) for index in index_fields], unique=unique,
                                           name=name,
                                           expireAfterSeconds=expire_sec)

    def create_shard_key(self, collection, key):
        stats = self.__db.command('collstats', collection)
        if stats.get("sharded"):
            return
        full_collection = "%s.%s" % (self._dbcfg['name'], collection)
        admin_db = self.__use_admin()
        admin_db.command("shardcollection", full_collection, key=key)

    def is_index_exist(self, index, collection):
        self.set_collection(collection)
        indexs = self.__collection.index_information()
        if index in indexs:
            return True
        return False

    def get_all_index(self, collection):
        self.set_collection(collection)
        return list(self.__collection.index_information().keys())

    def _reconnection(self, collection=None):
        self.__db = self.__init_db()
        if collection:
            self.__collection = self.__db[collection]

    def __init_db(self):
        db = self.__connect_db()
        try:
            db.authenticate(self._dbcfg['user'], self._dbcfg['password'])
        except:
            db = self.__connect_db()
        return db

    def __connect_db(self):
        if "db_replset" in self._dbcfg:
            conn = pymongo.MongoClient(self._dbcfg['db_replset'], serverSelectionTimeoutMS=10000,
                                       socketTimeoutMS=10000, waitQueueTimeoutMS=5000)
            return conn[self._dbcfg['name']]
        conn = pymongo.MongoClient(self._dbcfg['host'], int(self._dbcfg['port']), serverSelectionTimeoutMS=10000,
                                   socketTimeoutMS=10000, waitQueueTimeoutMS=5000)
        return conn[self._dbcfg['name']]

    def __use_admin(self):
        conn = pymongo.MongoClient(self._dbcfg['host'], int(self._dbcfg['port']), serverSelectionTimeoutMS=10000,
                                   socketTimeoutMS=10000, waitQueueTimeoutMS=5000)
        conn[self._dbcfg["name"]].authenticate(self._dbcfg['user'], self._dbcfg['password'])
        return conn["admin"]

    def aggregate(self, pipeline, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _aggregate(self, pipeline):
            cursor = self.__collection.aggregate(pipeline)
            result = []
            for contentDict in cursor:
                if contentDict.has_key(ID):
                    contentDict[ID] = str(contentDict[ID])
                result.append(contentDict)
            return result

        return _aggregate(self, pipeline)

    def query_all(self, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_all(self):
            cursor = self.__collection.find()
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result

        return _query_all(self)

    def query_with_constraint(self, condition, collection=None, constraint={}, skip=0, limit=0, sort=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_with_constraint(self, condition, constraint, skip, limit, sort):
            if sort is None:
                cursor = self.__collection.find(condition, constraint).skip(skip).limit(limit)
            elif isinstance(sort, tuple):
                cursor = self.__collection.find(condition, constraint).collation(SORT_COLLATION).sort(sort[0],
                                                                                                      sort[1]).skip(
                    skip).limit(limit)
            else:
                cursor = self.__collection.find(condition, constraint).collation(SORT_COLLATION).sort(sort).skip(
                    skip).limit(limit)
            result = []
            for contentDict in cursor:
                if ID in contentDict:
                    contentDict[ID] = str(contentDict[ID])
                result.append(contentDict)
            return result

        return _query_with_constraint(self, condition, constraint, skip, limit, sort)

    def count(self, condition, collection=None, limit=1, sort=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _count(self, condition):
            if sort:
                if isinstance(sort, tuple):
                    cursor = self.__collection.find(condition, limit=limit).sort(sort[0], sort[1])
                else:
                    cursor = self.__collection.find(condition, limit=limit).sort(sort)
            else:
                cursor = self.__collection.find(condition, limit=limit)
            return cursor.count()

        return _count(self, condition)

    def query(self, condition, collection=None, limit=0, sort=None, skip=0):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query(self, condition):
            if sort:
                if isinstance(sort, tuple):
                    cursor = self.__collection.find(condition).sort(sort[0], sort[1]).skip(skip).limit(limit)
                else:
                    cursor = self.__collection.find(condition).sort(sort).skip(skip).limit(limit)
            else:
                cursor = self.__collection.find(condition).skip(skip).limit(limit)
            result = []
            for contentDict in cursor:
                if contentDict.get(ID, False):
                    contentDict.update({ID: str(contentDict[ID])})
                result.append(contentDict)
            return result

        return _query(self, condition)

    def distinct(self, attr, condition={}, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _distinct(self, attr):
            cursor = self.__collection.find(condition).distinct(attr)
            result = []
            for contentDict in cursor:
                result.append(contentDict)
            return result

        return _distinct(self, attr)

    def query_without_id(self, condition, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _query_without_id(self, condition):
            return self.query_with_constraint(condition, collection, {ID: 0})

        return _query_without_id(self, condition)

    def save(self, json_format_data, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _save(self, json_format_data):
            return self.__collection.insert(json_format_data)

        return _save(self, json_format_data)

    def delete(self, condition, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _delete(self, condition):
            return self.__collection.remove(condition)

        return _delete(self, condition)

    def clear_all(self, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _clear_all(self):
            self.__collection.remove()

        return _clear_all(self)

    def clear(self, condition, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _clear(self, condition):
            self.__collection.remove(condition)

        return _clear(self, condition)

    def update_attr(self, object_id, attr_dict, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update_attr(self, object_id, attr_dict):
            self.update({ID: object_id}, attr_dict)

        return _update_attr(self, object_id, attr_dict)

    def update(self, criteria, new_obj, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update(self, criteria, new_obj):
            self.__collection.update(criteria, {'$set': new_obj})

        _update(self, criteria, new_obj)
        return True

    def update_one(self, filter, attr_dict, collection=None, upsert=False):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update(self, filter, attr_dict):
            self.__collection.update_one(filter, {'$set': attr_dict}, upsert=upsert)

        _update(self, filter, attr_dict)
        return True

    def update_one_del_key(self, filter, attr_dict, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update(self, filter, attr_dict):
            self.__collection.update_one(filter, {'$unset': attr_dict})

        _update(self, filter, attr_dict)
        return True

    def update_many(self, filter, attr_dict, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _update(self, filter, attr_dict):
            return self.__collection.update_many(filter, {'$set': attr_dict})

        return _update(self, filter, attr_dict)

    def delete_by_key(self, criteria, key_path, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _delete_by_key(self, criteria, key_path):
            self.__collection.update(criteria, {'$unset': {key_path: ''}}, True)

        return _delete_by_key(self, criteria, key_path)

    def insert_one_document(self, attr_dict, collection=None):
        self.set_collection(collection)

        @retries_on_exception(2, self._reconnection, collection)
        def _insert_one_document(self, attr_dict):
            self.__collection.insert_one(attr_dict).inserted_id

        return _insert_one_document(self, attr_dict)

    def insert_many(self, attrDicts, collection=None):
        self.set_collection(collection)
        return self.__collection.insert_many(attrDicts)

    def bulk_write(self, update_elements, collection=None):
        self.set_collection(collection)
        return self.__collection.bulk_write([UpdateOne({"_id": ObjectId(element.uuid), "dirId": element.dir_id},
                                                       {"$set": element.update_data}, upsert=True) for element in
                                             update_elements])
