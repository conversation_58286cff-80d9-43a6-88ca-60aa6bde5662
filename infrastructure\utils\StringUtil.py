import json
import re
import operator


class StringUtil(object):

    @staticmethod
    def search_in_string(listPattern, stringText):
        for patternStr in listPattern:
            pattern = re.compile(patternStr)
            if pattern.search(stringText):
                return True
        return None

    @staticmethod
    def _convert_list_to_string(dictIn, key, formatStr):
        formatStr += '['
        for value in dictIn[key]:
            formatStr += u'{0}'.format(value)
        formatStr += '],  '
        return formatStr

    @staticmethod
    def get_dict_unicode_str(dictIn):
        formatStr = u'{'
        for k in dictIn:
            formatStr += u'{0}:'.format(k, )
            if isinstance(dictIn[k], list):
                formatStr = StringUtil()._convert_list_to_string(dictIn, k, formatStr)
            else:
                formatStr += u'{0},  '.format(dictIn[k])
        return formatStr + u'}'

    @staticmethod
    def convert_list_to_attr_obj(strList):
        return '\\\"' + ';'.join(strList) + '\\\"'

    @staticmethod
    def convert_string_to_dict(ldnString):
        resDict = {}
        ldnList = ldnString.split(',')
        for item in ldnList:
            itemList = item.split('=')
            resDict[itemList[0]] = itemList[1]
        return resDict

    @staticmethod
    def is_key_mo_path_dict_in_ldn(keyMoPathDict, ldn):
        ldnList = ldn.split(",")
        for key, value in keyMoPathDict.items():
            if "{0}={1}".format(key, value) not in ldnList:
                return False
        return True

    @staticmethod
    def get_num_str(string):
        nums = re.findall("\d", string)
        if nums:
            return "".join(nums)

    @staticmethod
    def convert_form_data_to_json(formData):
        result = {}
        if formData is not None:
            for field, value in formData.items():
                if isinstance(value, (list, dict, bool)):
                    result[field] = json.dumps(value)
                else:
                    result[field] = str(value)
        return result

    @staticmethod
    def contains_any(container, *items):
        if not items:
            raise AttributeError("At least one possible contained item needs to be passed in.")
        for item in items:
            if operator.contains(container, item):
                return True
        return False

    @staticmethod
    def contains_all(container, *items):
        if not items:
            raise AttributeError("At least one possible contained item needs to be passed in.")
        for item in items:
            if not operator.contains(container, item):
                return False
        return True

    @staticmethod
    def num_string_convert_to_list(numstring: str):
        result = list()
        for temp in numstring.split(','):
            num = temp.split('-')
            if len(num) == 1:
                result.append(num[0])
            else:
                result += [str(_) for _ in range(int(num[0]), int(num[1]) + 1)]
        return result
