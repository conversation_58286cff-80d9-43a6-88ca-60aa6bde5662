# coding=utf-8

from ftplib import FTP
from infrastructure.logger.logger import logger
import socket
import traceback


class FtpClient(object):

    def __init__(self, ftpParaDict):
        self._remotedir = './'
        self._user = ftpParaDict['username']
        self._passwd = ftpParaDict['password']
        self._host = ftpParaDict['host']
        self._port = ftpParaDict['port']
        self._timeout = ftpParaDict['timeout']
        self._ftp = FTP(self._host, self._user, self._passwd, timeout=self._timeout)

    def set_host(self, host):
        self._host = host

    def set_port(self, port):
        self._port = port

    def connect(self, *args):
        try:
            socketDefaultTimeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(20)
            self._ftp.set_pasv(True)
            self._ftp.connect(self._host, self._port)
            self.__login()
        except Exception:
            self.disconnect()
            traceback.print_exc()
        finally:
            socket.setdefaulttimeout(socketDefaultTimeout)

    def disconnect(self, *args):
        try:
            self._ftp.quit()
        except Exception:
            pass

    def __login(self):
        try:
            self._ftp.login(self._user, self._passwd)
            self._ftp.cwd(self._remotedir)
            return True
        except Exception as e:
            return False, e

    def pwd(self):
        return self._ftp.pwd()

    def _find_large_files(self, path, minSize, maxSize):
        print(path)
        large_files = []
        try:
            self._ftp.cwd(path)
        except Exception as e:
            print(e)
            return
        files = self._ftp.nlst()
        print(files)
        for file in files:
            try:
                fileSize = self._ftp.size(file)
                if minSize <= fileSize <= maxSize:
                    large_files.append(file)
                    continue
            except Exception as e:
                logger.debug(f'Bizcode finding files, get the file {repr(file)} failed, reason: {repr(e)}')
                subdir_files = self._find_large_files(file, minSize, maxSize)
                print(subdir_files)
                large_files.extend(subdir_files)
                print(large_files)
                continue
        return large_files

    def find_large_files(self, path, minSize, maxSize):
        large_files = []
        try:
            self._ftp.cwd(path)
        except Exception as e:
            logger.debug(f'Bizcode cwd path failed, {repr(path)}, reason: {repr(e)}')
            return large_files
        files = self._ftp.nlst()
        for file in files:
            try:
                fileSize = self._ftp.size(file)
                print(file, fileSize)
                if minSize <= fileSize <= maxSize:
                    fullFileName = path + '/' + file
                    large_files.append(fullFileName)
                    logger.debug(f'Bizcode found the correct file {repr(fullFileName)} of size {repr(fileSize)}')
                    return large_files
            except Exception as e:
                logger.debug(f'Bizcode finding files, get the file size {repr(file)} failed, reason: {repr(e)}')
                fullPath = path + '/' + file
                logger.debug(f"Bizcode retry to find, maybe it's path {repr(fullPath)}")
                try:
                    self._ftp.cwd(fullPath)
                except Exception as e:
                    logger.debug(f"maybe {repr(file)} is not path, and can't be read as file, reason: {repr(e)}'")
                nlstFiles = self._ftp.nlst()
                if nlstFiles != [] and file not in nlstFiles:
                    subdir_files = self.find_large_files(fullPath, minSize, maxSize)
                    large_files.extend(subdir_files)
        return large_files

# if __name__ == '__main__':
#     ftpParas = {'username': 'zte',
#                 'password': 'At_2021!@#',
#                 'host': '*************',
#                 'port': 21,
#                 'timeout': 20}
#     ftp = FtpClient(ftpParas)
#     ftp.connect()
#     userRootDir = ftp.pwd()
#     print(userRootDir)
#     # print(ftp.list_dir(userRootDir))
#     fileList = ftp.find_large_files(userRootDir, 42949672960, 64424509440)
#     print(fileList)
