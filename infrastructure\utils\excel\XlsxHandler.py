import os
import openpyxl


class XlsxHandler:

    def __init__(self, path):
        self._workbook = None
        self._fileName = path
        self.open_exist_excel_file()

    def open_exist_excel_file(self):
        if os.path.exists((self._fileName)):
            try:
                self._workbook =openpyxl.load_workbook(self._fileName, data_only=True)
            except IOError as e1:
                raise Exception('Excel is abnormal in setup, err={0}'.format(e1))
        else:
            self._workbook = openpyxl.Workbook()
            self._create_path_if_not_exist(self._fileName)


    def _create_path_if_not_exist(self, fileName):
        index = fileName.rfind('\\')
        if index <= 0:
            index = fileName.rfind('/')
        if index > 0:
            fileNametemp = fileName[0:index]
            if os.path.exists((fileNametemp)):
                return
            os.makedirs(fileNametemp)

    def is_sheetname_exist(self, sheetname):
        sheetnames = self._workbook.get_sheet_names()
        return sheetname in sheetnames

    def get_sheet_by_name(self, sheetname, index=None):
        self._reload_excel_file()
        if not self.is_sheetname_exist(sheetname):
            return self.create_sheet(sheetname, index)
        return self._workbook.get_sheet_by_name(sheetname)

    def create_sheet(self, sheetname, index=None):
        if self.is_sheetname_exist(sheetname):
            return self.get_sheet_by_name(sheetname)
        return self._workbook.create_sheet(sheetname, index)

    def _reload_excel_file(self):
        if (self._workbook is None) and (os.path.exists(self._fileName)):
            try:
                self._workbook = openpyxl.load_workbook(self._fileName, data_only=True)
            except IOError as e1:
                raise Exception('Excel is abnormal in reading reload, err={0}'.format(e1))

    def read_excel_cell_value(self, sheetName, row, col):
        sheet = self.get_sheet_by_name(sheetName)
        return sheet.cell(int(row), int(col)).value

    def read_excel_cell_value_by_position(self, sheetName, positionInfo):
        sheet = self.get_sheet_by_name(sheetName)
        return sheet[positionInfo].value

    def read_excel_row_values(self, sheetName, row):
        sheet = self.get_sheet_by_name(sheetName)
        for row in sheet.iter_rows(min_row=row, max_row=row, values_only=True):
            result = list(row)
        return result

    def read_excel_col_values(self, sheetName, col):
        sheet = self.get_sheet_by_name(sheetName)
        for col in sheet.iter_cols(min_col=col, max_col=col, values_only=True):
            result = list(col)
        return result

    def read_excel_block_values_by_row(self, sheetName, min_row=None, max_row=None, min_col=None, max_col=None):
        sheet = self.get_sheet_by_name(sheetName)
        result = []
        for values in sheet.iter_rows(min_row, max_row, min_col, max_col, values_only=True):
            result.append(list(values))
        return result
    def read_excel_block_values_by_col(self, sheetName, min_col=None, max_col=None, min_row=None, max_row=None):
        sheet = self.get_sheet_by_name(sheetName)
        result = []
        for values in sheet.iter_cols(min_col, max_col, min_row, max_row, values_only=True):
            result.append(list(values))
        return result

    def write_block_data(self, sheetName, dataList):
        if dataList is None or len(dataList) == 0:
            return
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            for data in dataList:
                currentSheet.append(data)
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing Value={0}, err="{1}"'.format(
                dataList, e1))
        return True

    def write_excel_cell_value(self, sheetName, row, col, newValue):
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            currentSheet.cell(int(row), int(col)).value = newValue
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, row={0}, col={1}, Value={2}, err="{3}"'.format(
                row, col, newValue, e1))
        return True

    def write_excel_cell_value_by_position(self, sheetName, positionInfo, newValue):
        try:
            sheet = self.get_sheet_by_name(sheetName)
            sheet[positionInfo].value = newValue
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, positionInfo={0}, newValue={1}, err="{2}"'.format(
                positionInfo, newValue, e1))
        return True

    def insert_cell_after_row(self, sheetName, row, col, vaule=None):
        try:
            sheet = self.get_sheet_by_name(sheetName)
            sheet.insert_cols(idx=sheet.max_column)
            # 从第row行开始，每行第col列插入一个单元格,值为value，其余单元格右移一位
            for rows in sheet.iter_rows(min_row=row):
                beforecell = vaule
                for cell in rows:
                    if cell.column > col:
                        nowcell = cell.value
                        cell.value = beforecell
                        beforecell = nowcell
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, row={0}, col={1}, err="{2}"'.format(
                row, col, e1))
        return True

    def write_excel_row_value(self, sheetName, row, start_column=1, value_list=[]):
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            col = int(start_column)
            for value in value_list:
                currentSheet.cell(int(row), col).value = value
                col += 1
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, row={0}, startcol={1}, Value={2}, err="{3}"'.format(
                row, start_column, value_list, e1))
        return True

    def write_excel_col_value(self, sheetName, col, start_rowumn=1, value_list=[]):
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            row = int(start_rowumn)
            for value in value_list:
                currentSheet.cell(row, int(col)).value = value
                row += 1
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, startrow={0}, col={1}, Value={2}, err="{3}"'.format(
                start_rowumn, col, value_list, e1))
        return True

    def write_excel_rows_value(self, sheetName, start_rowumn=1, start_column=1, dataList=[]):
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            row = int(start_rowumn)
            for row_values in dataList:
                col = int(start_column)
                for value in row_values:
                    currentSheet.cell(row, col).value = value
                    col += 1
                row += 1
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, start_row={0}, startcol={1}, Value={2}, err="{3}"'.format(
                start_rowumn, start_column, dataList, e1))
        return True

    def write_excel_cols_value(self, sheetName, start_column=1, start_rowumn=1, dataList=[]):
        try:
            currentSheet = self.get_sheet_by_name(sheetName)
            col = int(start_column)
            for col_values in dataList:
                row = int(start_rowumn)
                for value in col_values:
                    currentSheet.cell(row, col).value = value
                    row += 1
                col += 1
            self._workbook.save(self._fileName)
        except Exception as e1:
            raise Exception('Excel is abnormal in writing, start_col={0}, start_row={1}, Value={2}, err="{3}"'.format(
                start_column, start_rowumn, dataList, e1))
        return True


    def get_head(self, sheetName):
        return self.read_excel_row_values(sheetName, 1)

    def get_worksheet_row_col_num(self, sheetName):
        currentSheet = self.get_sheet_by_name(sheetName)
        return (currentSheet.max_row, currentSheet.max_column)

    def read_excel_sheet_names(self):
        self._reload_excel_file()
        return self._workbook.sheet_names()

