# coding=utf-8
'''
Created on 2015年5月25日

@author: wang<PERSON><PERSON><PERSON><PERSON>
'''
import logging
import re
import asyncio
import traceback
import time

import paramiko

from infrastructure.channels.Channel import Channel
from infrastructure.device.DeviceCmd import DeviceCmd

class Ssh(Channel):

    '''
    classdocs
    '''

    def __init__(self, sshParaDict: dict, port: int, timeout: int = 10):
        '''
        Constructor
        '''
        self._host = sshParaDict['hostname']
        self._port = port
        self._username = sshParaDict['username']
        self._password = sshParaDict['password']
        self.timeout = timeout
        self._sshType = sshParaDict['sshType']
        self._bufSize = 8192
        self._recvTimeout = 10
        self.matchIndex = -1
        super(Ssh, self).__init__(self._host, port, self._sshType)

    def get_status(self):
        if self._tn is None or self._chan.closed:
            return False
        else:
            return True

    def connect(self, *args):
        try:
            logging.info('ip:{0},port:{1},time:{2}'.format(
                self._host, self._port, self.timeout))
            self._tn = paramiko.SSHClient()
            self._tn.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self._connect_ssh()
            chan = self._tn._transport.open_session()
            chan.get_pty(width=240)
            chan.invoke_shell()
            self._chan = chan
        except Exception as e:
            logging.error(e)
            logging.error('SSH Connect error')
            self._tn = None
            return self._tn
        else:
            return self._tn

    def _connect_ssh(self):
        return self._tn.connect(hostname=self._host, port=int(self._port), username=self._username, password=self._password, timeout=self.timeout, look_for_keys=False)

    def connect_without_screen(self, *args):
        try:
            logging.info('ip:{0},port:{1},time:{2}'.format(
                self._host, self._port, self.timeout))
            self._tn = paramiko.SSHClient()
            self._tn.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self._connect_ssh()
            self._chan = self._tn._transport.open_session()
            self._chan.invoke_shell()
        except Exception as e:
            logging.error(e)
            logging.error('SSH Connect error')
            self._tn = None
        finally:
            return self._tn

    def __get_not_clear_buff_cmds(self):
        return ['Y', 'y', 'CloseRbIndexFlow', '1', '']

    async def execute_cmd(self, cmdObj):
        if self._tn is None:
            logging.error('SSH channel not exist!!!!!')
            cmdObj.fail_to_excute('SSH channel not exist')
        else:
            try:
                if cmdObj.command is not None:
                    if cmdObj.command.strip() not in self.__get_not_clear_buff_cmds():
                        await self.clear_buff()
                    if not cmdObj.command.endswith('\n'):
                        cmdObj.command += '\n'
                    self._chan.send(cmdObj.command)
                self._recvTimeout = cmdObj.timeout
                cmdObj = await self._recv_result(cmdObj)
            except Exception as e:
                traceback.print_exc()
                logging.error(e)
                cmdObj.fail_to_excute('SSH execute cmd error')
        return cmdObj.cmd_result

    async def _recv_result(self, cmdObj):
        buff, isExpectedBuff = await self._find_expected_buff(cmdObj.expected)
        if isExpectedBuff:
            cmdObj.success_to_excute(buff)
        else:
            cmdObj.fail_to_excute(
                'SSH recv failed:expected {0},but not found, buff is {1}'.format(cmdObj.expected, buff))
            cmdObj._cmdResult.update_return_string(buff)
        return cmdObj

    async def _find_expected_buff(self, expectedValue):
        totalBuff = ""
        initTime = time.time()
        printErrorCount = 2
        while True:
            buff = self._recv(self._bufSize, printErrorCount)
            totalBuff = totalBuff + buff.decode('utf8', errors='ignore')
            expectmessage = re.findall(expectedValue, totalBuff)
            if expectedValue in ['', None]:
                return totalBuff, True
            if len(expectmessage) != 0:
                return totalBuff, True
            if (time.time() - initTime) > int(self._recvTimeout):
                return totalBuff, False
            printErrorCount -= 1
            if not buff:
                await asyncio.sleep(0.01)

    def _recv(self, bufSize, printErrorCount=1):
        try:
            self._chan.timeout = self._recvTimeout
            return self._chan.recv(bufSize)
        except Exception:
            if printErrorCount > 0:
                traceback.print_exc()
            return b""

    def disconnect(self, *args):
        if self._tn is not None:
            try:
                self._chan.send("exit")
            except Exception:
                pass
            finally:
                self._chan.close()
                self._tn.close()
        else:
            pass

    async def clear_buff(self, expectedValue: str = None):
        # try:
        #     logging.debug("clear buff start")
        #     await self._find_expected_buff(expectedValue)
        # except Exception:
        #     logging.debug("clear buff timeout")
        try:
            self._chan.settimeout(0.02)
            for _ in range(1000):
                recv = self._chan.recv(self._bufSize)
                logging.info('clear buff: {0}'.format(recv))
        except Exception:
            pass


if __name__ == '__main__':
    import time
    sshParaDict = {'hostname': '**************', 'username': 'pict',
                   'password': "PaaS1!2@3#4$", 'sshType': 'bmsc'}
    tn = Ssh(sshParaDict, 22, 5)
    tn.connect()
    cmd = DeviceCmd('sudo su', expected='pict:', timeout=5)
    cmdobj = asyncio.run(tn.execute_cmd(cmd))
    print(cmdobj.return_string)
    print(cmdobj.result)
    tn.disconnect()
