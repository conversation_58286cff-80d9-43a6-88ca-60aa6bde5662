"""
@File: DistrubutedLock.py
@Author: 许王禾子10333721
@Time: 2024/3/5 下午6:41
@License: Copyright 2022-2030
@Desc: None
"""
import redis
import time
import uuid


class RedisLock:
    def __init__(self, lock_id, redis_client):
        self.lock_id = lock_id
        self.redis_client = redis_client
        self.lock_value = None

    def acquire_lock(self, timeout=10):
        """获取锁"""
        self.lock_value = str(uuid.uuid4())
        end = time.time() + timeout
        while time.time() < end:
            if self.redis_client.set(self.lock_id, self.lock_value, nx=True, ex=timeout):
                return True
            time.sleep(0.001)
        return False

    def release_lock(self):
        """释放锁"""
        script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        release_flag = self.redis_client.eval(script, 1, self.lock_id, self.lock_value)
        return release_flag


# 使用分布式锁
client = redis.StrictRedis(host='localhost', port=6379, db=0)
lock = RedisLock('my_lock', client)

if lock.acquire_lock():
    try:
        print("Lock acquired, doing some critical work...")
        # 这里执行需要同步的代码
        time.sleep(2)
    finally:
        lock.release_lock()
        print("Lock released.")
else:
    print("Failed to acquire lock.")