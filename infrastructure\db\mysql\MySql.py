import pymysql
import logging
from infrastructure.utils.Repeat import retries_on_exception


# 如果访问频繁，需要改造为线程池
class MySql(object):
    def __init__(self, dbcfg):
        self._dbcfg = dbcfg
        self._init_db()

    def _reconnect(self):
        self._init_db()

    def _init_db(self):
        self._con, self._cursor = self._conn()

    def _conn(self):
        try:
            conn = self._connect_db()
            cursor = conn.cursor()
        except Exception as e:
            logging.error("DataBase connect error,please check the db config：{}".format(e))
            raise Exception("DataBase connect error,please check the db config：{}".format(e))
        return conn, cursor

    def _connect_db(self):
        conn = pymysql.connect(host=self._dbcfg['host'], port=int(self._dbcfg['port']),
                               db=self._dbcfg["db"], user=self._dbcfg["user"],
                               password=self._dbcfg["password"], charset="utf8",
                               connect_timeout=10,
                               read_timeout=30,
                               write_timeout=60,
                               cursorclass=pymysql.cursors.DictCursor)
        return conn

    def close(self):
        try:
            self._con.close()
            self._cursor.close()
        except Exception as e:
            logging.error("close connectiong error;please check the db config.")
            return e

    @property
    def cursor(self):
        return self._cursor

    def fetch(self, table_name=None, fields=(), where=None, many=False):
        if len(fields) == 0:
            fields = "*"

        @retries_on_exception(2, self._reconnect)
        def query_all():
            cur = self._con.cursor()
            if where:
                sql = f'select {",".join(fields)} from {table_name} where {where}'
            else:
                sql = f'select {",".join(fields)} from {table_name}'
            cur.execute(sql)
            if many:
                data = cur.fetchmany()
            else:
                data = cur.fetchone()
            cur.close()
            return data

        return query_all()
