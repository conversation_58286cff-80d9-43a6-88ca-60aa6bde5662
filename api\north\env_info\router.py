from api.north.env_info.jenkins_server_env import router as jenkins_server_env_router
from api.north.env_info.pc_env import router as pc_env_router
from api.north.env_info.biz_core_env import router as biz_core_env_router
from api.north.env_info.version_env import router as version_env_router
from api.north.env_info.device_env import router as device_env_router
from api.north.env_info.logs import router as logs_router
from api.north.env_info.cron_task import router as cron_task_router


ROUTERS = [
    jenkins_server_env_router,
    pc_env_router,
    biz_core_env_router,
    version_env_router,
    device_env_router,
    logs_router,
    cron_task_router
]


def init_env_router(app):
    for router in ROUTERS:
        app.include_router(router)

