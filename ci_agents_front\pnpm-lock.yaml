lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@pureadmin/descriptions':
        specifier: ^1.2.1
        version: 1.2.1(echarts@5.6.0)(element-plus@2.9.9(vue@3.5.13(typescript@5.8.3)))(typescript@5.8.3)
      '@pureadmin/table':
        specifier: ^3.2.1
        version: 3.2.1(element-plus@2.9.9(vue@3.5.13(typescript@5.8.3)))(typescript@5.8.3)
      '@pureadmin/utils':
        specifier: ^2.6.0
        version: 2.6.0(echarts@5.6.0)(vue@3.5.13(typescript@5.8.3))
      '@vueuse/core':
        specifier: ^13.1.0
        version: 13.1.0(vue@3.5.13(typescript@5.8.3))
      '@vueuse/motion':
        specifier: ^3.0.3
        version: 3.0.3(vue@3.5.13(typescript@5.8.3))
      animate.css:
        specifier: ^4.1.1
        version: 4.1.1
      axios:
        specifier: ^1.9.0
        version: 1.9.0
      dayjs:
        specifier: ^1.11.13
        version: 1.11.13
      echarts:
        specifier: ^5.6.0
        version: 5.6.0
      element-plus:
        specifier: ^2.9.8
        version: 2.9.9(vue@3.5.13(typescript@5.8.3))
      js-cookie:
        specifier: ^3.0.5
        version: 3.0.5
      localforage:
        specifier: ^1.10.0
        version: 1.10.0
      mitt:
        specifier: ^3.0.1
        version: 3.0.1
      nprogress:
        specifier: ^0.2.0
        version: 0.2.0
      path-browserify:
        specifier: ^1.0.1
        version: 1.0.1
      pinia:
        specifier: ^3.0.2
        version: 3.0.2(typescript@5.8.3)(vue@3.5.13(typescript@5.8.3))
      pinyin-pro:
        specifier: ^3.26.0
        version: 3.26.0
      qs:
        specifier: ^6.14.0
        version: 6.14.0
      responsive-storage:
        specifier: ^2.2.0
        version: 2.2.0
      sortablejs:
        specifier: ^1.15.6
        version: 1.15.6
      vue:
        specifier: ^3.5.13
        version: 3.5.13(typescript@5.8.3)
      vue-router:
        specifier: ^4.5.0
        version: 4.5.1(vue@3.5.13(typescript@5.8.3))
      vue-tippy:
        specifier: ^6.7.0
        version: 6.7.0(vue@3.5.13(typescript@5.8.3))
      vue-types:
        specifier: ^6.0.0
        version: 6.0.0(vue@3.5.13(typescript@5.8.3))
    devDependencies:
      '@commitlint/cli':
        specifier: ^19.8.0
        version: 19.8.0(@types/node@20.17.32)(typescript@5.8.3)
      '@commitlint/config-conventional':
        specifier: ^19.8.0
        version: 19.8.0
      '@commitlint/types':
        specifier: ^19.8.0
        version: 19.8.0
      '@eslint/js':
        specifier: ^9.25.1
        version: 9.25.1
      '@faker-js/faker':
        specifier: ^9.7.0
        version: 9.7.0
      '@iconify/json':
        specifier: ^2.2.331
        version: 2.2.333
      '@iconify/vue':
        specifier: 4.2.0
        version: 4.2.0(vue@3.5.13(typescript@5.8.3))
      '@tailwindcss/vite':
        specifier: ^4.1.4
        version: 4.1.4(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))
      '@types/js-cookie':
        specifier: ^3.0.6
        version: 3.0.6
      '@types/node':
        specifier: ^20.17.30
        version: 20.17.32
      '@types/nprogress':
        specifier: ^0.2.3
        version: 0.2.3
      '@types/path-browserify':
        specifier: ^1.0.3
        version: 1.0.3
      '@types/qs':
        specifier: ^6.9.18
        version: 6.9.18
      '@types/sortablejs':
        specifier: ^1.15.8
        version: 1.15.8
      '@vitejs/plugin-vue':
        specifier: ^5.2.3
        version: 5.2.3(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3))
      '@vitejs/plugin-vue-jsx':
        specifier: ^4.1.2
        version: 4.1.2(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3))
      boxen:
        specifier: ^8.0.1
        version: 8.0.1
      code-inspector-plugin:
        specifier: ^0.20.10
        version: 0.20.10
      cssnano:
        specifier: ^7.0.6
        version: 7.0.6(postcss@8.5.3)
      eslint:
        specifier: ^9.25.1
        version: 9.25.1(jiti@2.4.2)
      eslint-config-prettier:
        specifier: ^10.1.2
        version: 10.1.2(eslint@9.25.1(jiti@2.4.2))
      eslint-plugin-prettier:
        specifier: ^5.2.6
        version: 5.2.6(eslint-config-prettier@10.1.2(eslint@9.25.1(jiti@2.4.2)))(eslint@9.25.1(jiti@2.4.2))(prettier@3.5.3)
      eslint-plugin-vue:
        specifier: ^10.0.0
        version: 10.0.1(eslint@9.25.1(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.25.1(jiti@2.4.2)))
      gradient-string:
        specifier: ^3.0.0
        version: 3.0.0
      husky:
        specifier: ^9.1.7
        version: 9.1.7
      lint-staged:
        specifier: ^15.5.1
        version: 15.5.1
      postcss:
        specifier: ^8.5.3
        version: 8.5.3
      postcss-html:
        specifier: ^1.8.0
        version: 1.8.0
      postcss-load-config:
        specifier: ^6.0.1
        version: 6.0.1(jiti@2.4.2)(postcss@8.5.3)(yaml@2.7.1)
      postcss-scss:
        specifier: ^4.0.9
        version: 4.0.9(postcss@8.5.3)
      prettier:
        specifier: ^3.5.3
        version: 3.5.3
      rimraf:
        specifier: ^6.0.1
        version: 6.0.1
      rollup-plugin-visualizer:
        specifier: ^5.14.0
        version: 5.14.0(rollup@4.40.1)
      sass:
        specifier: ^1.87.0
        version: 1.87.0
      stylelint:
        specifier: ^16.19.0
        version: 16.19.1(typescript@5.8.3)
      stylelint-config-recess-order:
        specifier: ^6.0.0
        version: 6.0.0(stylelint@16.19.1(typescript@5.8.3))
      stylelint-config-recommended-vue:
        specifier: ^1.6.0
        version: 1.6.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3))
      stylelint-config-standard-scss:
        specifier: ^14.0.0
        version: 14.0.0(postcss@8.5.3)(stylelint@16.19.1(typescript@5.8.3))
      stylelint-prettier:
        specifier: ^5.0.3
        version: 5.0.3(prettier@3.5.3)(stylelint@16.19.1(typescript@5.8.3))
      svgo:
        specifier: ^3.3.2
        version: 3.3.2
      tailwindcss:
        specifier: ^4.1.4
        version: 4.1.4
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.31.0
        version: 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      unplugin-icons:
        specifier: ^22.1.0
        version: 22.1.0(@vue/compiler-sfc@3.5.13)
      vite:
        specifier: ^6.3.3
        version: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)
      vite-plugin-cdn-import:
        specifier: ^1.0.1
        version: 1.0.1(rollup@4.40.1)(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))
      vite-plugin-compression:
        specifier: ^0.5.1
        version: 0.5.1(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))
      vite-plugin-fake-server:
        specifier: ^2.2.0
        version: 2.2.0
      vite-plugin-remove-console:
        specifier: ^2.2.0
        version: 2.2.0
      vite-plugin-router-warn:
        specifier: ^1.0.0
        version: 1.0.0
      vite-svg-loader:
        specifier: ^5.1.0
        version: 5.1.0(vue@3.5.13(typescript@5.8.3))
      vue-eslint-parser:
        specifier: ^10.1.3
        version: 10.1.3(eslint@9.25.1(jiti@2.4.2))
      vue-tsc:
        specifier: ^2.2.10
        version: 2.2.10(typescript@5.8.3)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@ampproject/remapping/-/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@antfu/install-pkg@1.0.0':
    resolution: {integrity: sha1-KRKhUPyLNeyRL1g/kAdO6Y9k1mo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@antfu/install-pkg/-/install-pkg-1.0.0.tgz}

  '@antfu/utils@8.1.1':
    resolution: {integrity: sha1-lbGUfSkqmi7/+6IIF5bcqgXs7fs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@antfu/utils/-/utils-8.1.1.tgz}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/code-frame/-/code-frame-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha1-ghwdNWQcNVKE1KhwuKSnsMFB42c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/compat-data/-/compat-data-7.26.8.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.10':
    resolution: {integrity: sha1-XIdvg8jE3LIz7ktnDAYG8qwwAPk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/core/-/core-7.26.10.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.0':
    resolution: {integrity: sha1-dkOCtTkuW5r/k8rbGQ0HRYZsvCw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/generator/-/generator-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.0':
    resolution: {integrity: sha1-3gx1OxzR2atV1HPFpc9xcPCoGIA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.0':
    resolution: {integrity: sha1-UY+tajB8apb0SvFJErLCCr6b/DA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha1-nf/+RvcnAFpeopBRrINftzXkwaM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha1-MySuULrn4qs8M/YMmod7agFGtU4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha1-GFgNAMmTQRetcZOSxPZYXJMzzDU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha1-bLBOgq4pHa6OcjNd/kOLByXxTI0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.0':
    resolution: {integrity: sha1-U9FWCY3vqCQ+qw8y+hdYkHWhuAg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/helpers/-/helpers-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.0':
    resolution: {integrity: sha1-PX1u4mjkHSYACRy9ThRf/uhaROw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/parser/-/parser-7.27.0.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha1-o0MToXjqVvGVFZm5KcHOrO5xkpA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.9':
    resolution: {integrity: sha1-Z92it02kNyfPIdRs+a/vI/Q2U5k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.27.0':
    resolution: {integrity: sha1-op/TSB2oVgHH40CRKW6XRtLMy6g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/template@7.27.0':
    resolution: {integrity: sha1-slPlQGzB3xxX3NGPEXYMLb9AwLQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/template/-/template-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.0':
    resolution: {integrity: sha1-EdfmRHeeFmwEQvmgcnTQLNkdSnA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/traverse/-/traverse-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.0':
    resolution: {integrity: sha1-75rLawbDFz9mMtmT7LbUrkcLRVk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@babel/types/-/types-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@commitlint/cli@19.8.0':
    resolution: {integrity: sha1-5m5aUmhDfkK3xvKkR+lOqDuKMnI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/cli/-/cli-19.8.0.tgz}
    engines: {node: '>=v18'}
    hasBin: true

  '@commitlint/config-conventional@19.8.0':
    resolution: {integrity: sha1-ShRwoBVvXR9WBCbAXPw6anlsK5w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/config-conventional/-/config-conventional-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/config-validator@19.8.0':
    resolution: {integrity: sha1-CzDCdORCfTlC/WLs9TwZ2Z9DrEo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/config-validator/-/config-validator-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/ensure@19.8.0':
    resolution: {integrity: sha1-SBwwcG3EqkqOhefR8Yd8MOUgGg0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/ensure/-/ensure-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/execute-rule@19.8.0':
    resolution: {integrity: sha1-68rCbcddSB4iOww31Z5b3XoWTUo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/execute-rule/-/execute-rule-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/format@19.8.0':
    resolution: {integrity: sha1-sGCE+zuAfyFCwZyUVyEn1KOvXaE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/format/-/format-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/is-ignored@19.8.0':
    resolution: {integrity: sha1-PkExaLEiLmJ5jdGjbGTSRU57spE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/is-ignored/-/is-ignored-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/lint@19.8.0':
    resolution: {integrity: sha1-wr+F3m0uhuE8lyzxmi1CXmLpsFc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/lint/-/lint-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/load@19.8.0':
    resolution: {integrity: sha1-fHB4+9ycTjoU+lqGhMPLhUNTVR4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/load/-/load-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/message@19.8.0':
    resolution: {integrity: sha1-GsHFJ6YBsyk3O6UP/XCnE6ywuMo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/message/-/message-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/parse@19.8.0':
    resolution: {integrity: sha1-vOQV3aYOEVI5zTN7FSYuRZyrjro=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/parse/-/parse-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/read@19.8.0':
    resolution: {integrity: sha1-0WvqhGYZEA4jxNVrzxkcOX2nVC8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/read/-/read-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/resolve-extends@19.8.0':
    resolution: {integrity: sha1-LvbESO1/klhAzKKC4843vqjptyY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/resolve-extends/-/resolve-extends-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/rules@19.8.0':
    resolution: {integrity: sha1-DKK3vZ3CJAkXOWPxUGHYysZxvao=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/rules/-/rules-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/to-lines@19.8.0':
    resolution: {integrity: sha1-9733h4mZwGIPOi9G+Cn8wfHx0Rg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/to-lines/-/to-lines-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/top-level@19.8.0':
    resolution: {integrity: sha1-X/TZuL5EUB7dLJS5NgKnEE1j2S0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/top-level/-/top-level-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@commitlint/types@19.8.0':
    resolution: {integrity: sha1-WnfHpyOnFJlaUZF+3Q7PzylJXTw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@commitlint/types/-/types-19.8.0.tgz}
    engines: {node: '>=v18'}

  '@csstools/css-parser-algorithms@3.0.4':
    resolution: {integrity: sha1-dEJuk70cTcqz5EH1zHuk+zXZQ1Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.4.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-tokenizer@3.0.3':
    resolution: {integrity: sha1-pVAshTkmX+y9hzweOVqJAznxGcI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@csstools/css-tokenizer/-/css-tokenizer-3.0.3.tgz}
    engines: {node: '>=18'}

  '@csstools/media-query-list-parser@4.0.2':
    resolution: {integrity: sha1-6A4X66FpP86vuNbyz8aMDnqat4o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@csstools/media-query-list-parser/-/media-query-list-parser-4.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/selector-specificity@5.0.0':
    resolution: {integrity: sha1-A3gXtXQmITTKvWj8TsGkVPFoQHs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@csstools/selector-specificity/-/selector-specificity-5.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss-selector-parser: ^7.0.0

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz}
    engines: {node: '>=10'}

  '@dual-bundle/import-meta-resolve@4.1.0':
    resolution: {integrity: sha1-UZwVSbDhR3WeeCVwHs/9JeWBn3s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@dual-bundle/import-meta-resolve/-/import-meta-resolve-4.1.0.tgz}

  '@element-plus/icons-vue@2.3.1':
    resolution: {integrity: sha1-H2Na1f3VyF7ZNkgVJVcOgrWoMHo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz}
    peerDependencies:
      vue: ^3.2.0

  '@esbuild/aix-ppc64@0.24.2':
    resolution: {integrity: sha1-OISNPiWv6EKnlDZDy804fMbhNGE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/aix-ppc64/-/aix-ppc64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/aix-ppc64@0.25.3':
    resolution: {integrity: sha1-AUGA2aFJz/2Vqu6tNxeUM/XqVDc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/aix-ppc64/-/aix-ppc64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.24.2':
    resolution: {integrity: sha1-9ZKVeui1ZDEp+oiceeac2GabuJQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-arm64/-/android-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.25.3':
    resolution: {integrity: sha1-ZJ5H4E3bJKJ9wFw5VyS8X0xVy/4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-arm64/-/android-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.24.2':
    resolution: {integrity: sha1-ctiiBjqmMDCK9Ian5cvNHhNDNbM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-arm/-/android-arm-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.25.3':
    resolution: {integrity: sha1-ig9xnI3CikplZ+9zKMNuqF9Wj/Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-arm/-/android-arm-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.24.2':
    resolution: {integrity: sha1-mncTUE1fBHkvM76cGXqIKy2I/rs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-x64/-/android-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.25.3':
    resolution: {integrity: sha1-4qsYLR/Qbam+8HhKE8KKdgLXgAk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/android-x64/-/android-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.24.2':
    resolution: {integrity: sha1-Aq4ErY6//W4uoJYYGzNmgWsrWTY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/darwin-arm64/-/darwin-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.25.3':
    resolution: {integrity: sha1-x/MWb87OTRWKc9z+cbJnLKCxZos=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/darwin-arm64/-/darwin-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.24.2':
    resolution: {integrity: sha1-nsMSvCnGDhts7K3IK9UE2K2qGek=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/darwin-x64/-/darwin-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.3':
    resolution: {integrity: sha1-2MU0LsGkv0sZFWQ9/gMbpLFzqHo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/darwin-x64/-/darwin-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.24.2':
    resolution: {integrity: sha1-XoL0TLSQbWrr8kSX1qBoz8FS+gA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/freebsd-arm64/-/freebsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.25.3':
    resolution: {integrity: sha1-n314ni63dH1IaIF0F8yWj/qE81s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.24.2':
    resolution: {integrity: sha1-P7HOkvJ2Fot1B0tOUaoNgUHszn8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/freebsd-x64/-/freebsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.3':
    resolution: {integrity: sha1-itNcUdCEGEqOnna7Q1bpU1CmRwk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/freebsd-x64/-/freebsd-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.24.2':
    resolution: {integrity: sha1-hWtjLXnrgK7AhkOB79Kd6P0LH0M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-arm64/-/linux-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.25.3':
    resolution: {integrity: sha1-OvDaPZGGCSqe3U4o+jQvV9njzTA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-arm64/-/linux-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.24.2':
    resolution: {integrity: sha1-yEa0aU3Fp10URPUiV8zFZZAhtzY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-arm/-/linux-arm-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.25.3':
    resolution: {integrity: sha1-6RyvqV5EdLOuPVTaEuAGt4LlciU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-arm/-/linux-arm-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.24.2':
    resolution: {integrity: sha1-+KFmFaeIJsy7ZWb6ualgbP1KN9U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-ia32/-/linux-ia32-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.25.3':
    resolution: {integrity: sha1-gQJXMthbaO5RAWG5Ss334wB+oXc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-ia32/-/linux-ia32-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.24.2':
    resolution: {integrity: sha1-HEUVOMdlvxSRNRLHbtijUeGLCfw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-loong64/-/linux-loong64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.25.3':
    resolution: {integrity: sha1-PHROTI1eEUjL5gpxoRtY7Y7l3rg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-loong64/-/linux-loong64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.24.2':
    resolution: {integrity: sha1-CEbt7vvD2NUGRcUYacxkQB2SOcs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-mips64el/-/linux-mips64el-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.3':
    resolution: {integrity: sha1-Hf4qXWNwLbkDTMaxCzCHzAQk7CY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-mips64el/-/linux-mips64el-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.24.2':
    resolution: {integrity: sha1-jj/FRQVnHRkzN6Nt/UwaI7ikFBI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-ppc64/-/linux-ppc64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.3':
    resolution: {integrity: sha1-LoXZdkwEoeuzRtwIE+oFlSyaXFY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-ppc64/-/linux-ppc64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.24.2':
    resolution: {integrity: sha1-ah6SCW1eaPe7EKDWS7W20dr5ppQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-riscv64/-/linux-riscv64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.3':
    resolution: {integrity: sha1-qeozNFVrCfhcy/6tWMgD0wUJJBU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-riscv64/-/linux-riscv64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.24.2':
    resolution: {integrity: sha1-qxjlbmb3o8ScuX0zfNCm/qKKhXc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-s390x/-/linux-s390x-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.25.3':
    resolution: {integrity: sha1-9qfLZ5aSIrIAl03ljxBd/o6ZRI0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-s390x/-/linux-s390x-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.24.2':
    resolution: {integrity: sha1-gUDJtA2mNNOAsLKcg3oLQmev848=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-x64/-/linux-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.25.3':
    resolution: {integrity: sha1-ojfTV47N0YSjBmsfQl4xSt4PgDM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/linux-x64/-/linux-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.24.2':
    resolution: {integrity: sha1-ZfGRYUMrr7OYH18gp/9Fq7LnCOY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/netbsd-arm64/-/netbsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-arm64@0.25.3':
    resolution: {integrity: sha1-TBXGjYFJYU3balb5yFrmLMygglk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.24.2':
    resolution: {integrity: sha1-ejqX13q/0Rdlpy8cb5sY9TlrzEA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/netbsd-x64/-/netbsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.3':
    resolution: {integrity: sha1-EvaFb4xUwtfQqKZKlxHAGnQ4eNU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/netbsd-x64/-/netbsd-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.24.2':
    resolution: {integrity: sha1-WLACON2PEjv/9o06zFOm7jaa+J8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-arm64@0.25.3':
    resolution: {integrity: sha1-ygeNrUo03xksYCM7BY2yyj2UvFw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.24.2':
    resolution: {integrity: sha1-CshD/aD+uFqT4oiEKTbCGgCoogU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/openbsd-x64/-/openbsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.3':
    resolution: {integrity: sha1-yReK22DhQOA6iB0HkSSEiceflbI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/openbsd-x64/-/openbsd-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.24.2':
    resolution: {integrity: sha1-i3qoleB4KNNsQipEBMwuzyf7FcY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/sunos-x64/-/sunos-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.25.3':
    resolution: {integrity: sha1-A3ZettQhT/J+UjCvd56AeQ0e4J8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/sunos-x64/-/sunos-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.24.2':
    resolution: {integrity: sha1-wCOvtkfKvww+0T8O3fxPHWHGaoU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-arm64/-/win32-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.25.3':
    resolution: {integrity: sha1-8chnvRcwqbjfxGF4XsZGLjSUEeo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-arm64/-/win32-arm64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.24.2':
    resolution: {integrity: sha1-lsNWEy0t2pkAmMi4uVEgnDzXQ8I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-ia32/-/win32-ia32-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.25.3':
    resolution: {integrity: sha1-d0kfWe9snd9B33BnDVZ4vrOswyI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-ia32/-/win32-ia32-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.24.2':
    resolution: {integrity: sha1-NKoLUtD7saZUtZas+llfDHt3p3s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-x64/-/win32-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.25.3':
    resolution: {integrity: sha1-sXohcfkHTfnpG/sH75mokqwGQSo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@esbuild/win32-x64/-/win32-x64-0.25.3.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.6.1':
    resolution: {integrity: sha1-5MWP3PBpbnpfGcMCAe1DEjqxWrw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint-community/eslint-utils/-/eslint-utils-4.6.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint-community/regexpp/-/regexpp-4.12.1.tgz}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.20.0':
    resolution: {integrity: sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/config-array/-/config-array-0.20.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.2.1':
    resolution: {integrity: sha1-JgQsAo0b7uXOIjWnkpuRxSZRZG0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/config-helpers/-/config-helpers-0.2.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.13.0':
    resolution: {integrity: sha1-vwLyCYRtO/mW+egAnbYt8nObRYw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/core/-/core-0.13.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/eslintrc/-/eslintrc-3.3.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.25.1':
    resolution: {integrity: sha1-JfXJMMK2i16+eshX91TL1h720Rc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/js/-/js-9.25.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/object-schema/-/object-schema-2.1.6.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.8':
    resolution: {integrity: sha1-R0iNj4FxtdRhPoMzE/POcI41Jfg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@faker-js/faker@9.7.0':
    resolution: {integrity: sha1-HPH+z8rV4tojMhQL87XyPMHCp/Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@faker-js/faker/-/faker-9.7.0.tgz}
    engines: {node: '>=18.0.0', npm: '>=9.0.0'}

  '@floating-ui/core@1.6.9':
    resolution: {integrity: sha1-ZNHaJRQzAZ2voJHemyiG/zXsFOY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@floating-ui/core/-/core-1.6.9.tgz}

  '@floating-ui/dom@1.6.13':
    resolution: {integrity: sha1-qKk4UyrqJ6lRIewW5meny+jFnjQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@floating-ui/dom/-/dom-1.6.13.tgz}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@floating-ui/utils/-/utils-0.2.9.tgz}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@humanfs/core/-/core-0.19.1.tgz}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@humanfs/node/-/node-0.16.6.tgz}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@humanwhocodes/retry/-/retry-0.3.1.tgz}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.2':
    resolution: {integrity: sha1-GGBHPeffoVRnZ0SPMz24DLD/IWE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@humanwhocodes/retry/-/retry-0.4.2.tgz}
    engines: {node: '>=18.18'}

  '@iconify/json@2.2.333':
    resolution: {integrity: sha1-pqWC+/4cWICWMstBzoc8QcMseKs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@iconify/json/-/json-2.2.333.tgz}

  '@iconify/types@2.0.0':
    resolution: {integrity: sha1-qw6epoHWyKEhTzDNdB/jogzFf1c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@iconify/types/-/types-2.0.0.tgz}

  '@iconify/utils@2.3.0':
    resolution: {integrity: sha1-G7v4xHfr6afKyup4sbfok3+cv7o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@iconify/utils/-/utils-2.3.0.tgz}

  '@iconify/vue@4.2.0':
    resolution: {integrity: sha1-cNeAdLorP1gPe3/8GQkMETmQhTM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@iconify/vue/-/vue-4.2.0.tgz}
    peerDependencies:
      vue: '>=3'

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@isaacs/cliui/-/cliui-8.0.2.tgz}
    engines: {node: '>=12'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}

  '@keyv/serialize@1.0.3':
    resolution: {integrity: sha1-4P43EOKjecsEkM1B5aX/orq1i/Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@keyv/serialize/-/serialize-1.0.3.tgz}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    engines: {node: '>= 8'}

  '@nuxt/kit@3.17.1':
    resolution: {integrity: sha1-Q2t64sU7W8oe436DMzFjnItJXxI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@nuxt/kit/-/kit-3.17.1.tgz}
    engines: {node: '>=18.12.0'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha1-PSbc443mWQ73nEfsLFV5PAatT2c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@parcel/watcher/-/watcher-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@pkgr/core@0.2.4':
    resolution: {integrity: sha1-2JcXCisLpR94oJntzNlo97EDOHw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@pkgr/core/-/core-0.2.4.tgz}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@popperjs/core/-/core-2.11.8.tgz}

  '@pureadmin/descriptions@1.2.1':
    resolution: {integrity: sha1-Ezk9vEhRMhOH70NXna8MEq1rlRs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@pureadmin/descriptions/-/descriptions-1.2.1.tgz}
    peerDependencies:
      element-plus: ^2.0.0

  '@pureadmin/table@3.2.1':
    resolution: {integrity: sha1-JkyEF5IPQsA/EuZhAn3h7Iy4LYE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@pureadmin/table/-/table-3.2.1.tgz}
    peerDependencies:
      element-plus: ^2.0.0

  '@pureadmin/utils@2.6.0':
    resolution: {integrity: sha1-s965NuIQEdW10fsm3oxywvZhi/k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@pureadmin/utils/-/utils-2.6.0.tgz}
    peerDependencies:
      echarts: '*'
      vue: '*'
    peerDependenciesMeta:
      echarts:
        optional: true
      vue:
        optional: true

  '@rollup/pluginutils@5.1.4':
    resolution: {integrity: sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/pluginutils/-/pluginutils-5.1.4.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.40.1':
    resolution: {integrity: sha1-4VYtNgvKc8e+9v7vhgmN46Lx1EI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.40.1.tgz}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.40.1':
    resolution: {integrity: sha1-N7pjlAIRZz4V3MX0aaeONCdtvKc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.1.tgz}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.40.1':
    resolution: {integrity: sha1-WLHrhtmX1x2rxbeJAyM6PCdDjKA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.1.tgz}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.40.1':
    resolution: {integrity: sha1-XiLasyMrHlddkwzokauxj+GcWMk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.40.1.tgz}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.40.1':
    resolution: {integrity: sha1-BMiS2f+GTWbjFBljRyarC+uzNwc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.40.1.tgz}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.40.1':
    resolution: {integrity: sha1-9LHgkffPWvyeOgKdcBKK1WQJ7Ps=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.40.1.tgz}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.40.1':
    resolution: {integrity: sha1-yIFLtc4EeoGx/kozYo39SsUr2GQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.40.1.tgz}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.40.1':
    resolution: {integrity: sha1-W0572Dy+u/X/6ViALc/U7jS/c6M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.40.1.tgz}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.40.1':
    resolution: {integrity: sha1-FByEjlPO4BHoKhF3e4pR8bPo13w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.40.1.tgz}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.40.1':
    resolution: {integrity: sha1-Iuvq8vowGqSqbIS3YObNHRrH6x4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.40.1.tgz}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.40.1':
    resolution: {integrity: sha1-ILd9x45iL1gU/46QwUyTjOuAQ7w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.40.1.tgz}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.40.1':
    resolution: {integrity: sha1-LJD5nJh+8RmNT40V11TChuHwexM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.40.1.tgz}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.40.1':
    resolution: {integrity: sha1-kzb9XkfX9HYNAqqF92l2F27vU8o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.40.1.tgz}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.40.1':
    resolution: {integrity: sha1-11tNVNRkObtcbBN2J4j1fnmPVnA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.40.1.tgz}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.40.1':
    resolution: {integrity: sha1-6fCbgC8SkYOSRzmQKL6u+c4DTIE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.40.1.tgz}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.40.1':
    resolution: {integrity: sha1-BBMWncAEcGZ96oV1wRKdTnpz6yk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.1.tgz}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.40.1':
    resolution: {integrity: sha1-x2/VkzI8YOohlDmgDabG0z/9DqY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.40.1.tgz}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.40.1':
    resolution: {integrity: sha1-x3JMOG7tC9pa5xQ+QIHBkQyrNJs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.1.tgz}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.40.1':
    resolution: {integrity: sha1-d0nhtly2T+bUGtGtnpcKDMyKw1A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.40.1.tgz}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.40.1':
    resolution: {integrity: sha1-gHi3H+DVgl3L+D1Sp9yFiznaFlw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.40.1.tgz}
    cpu: [x64]
    os: [win32]

  '@sxzz/popperjs-es@2.11.7':
    resolution: {integrity: sha1-p/aeNmXT2psRX55xZx2uG5fhNnE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz}

  '@tailwindcss/node@4.1.4':
    resolution: {integrity: sha1-z6u7zVPLuuihddx0Tm/jHorUPT4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/node/-/node-4.1.4.tgz}

  '@tailwindcss/oxide-android-arm64@4.1.4':
    resolution: {integrity: sha1-XO4QhfbIVvDaLBguKdEVrx8RGOg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.4':
    resolution: {integrity: sha1-h4wOo4+id/BYCEuxqRpIkdkEmUU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.4':
    resolution: {integrity: sha1-/96UdYH36qfh3yviIiVcz/Bj3oo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.4':
    resolution: {integrity: sha1-iU2+AVWv6SQHEZjERjVmPT2clno=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4':
    resolution: {integrity: sha1-e1195qiGE+XJCKaPHthKxnX9k1E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.4':
    resolution: {integrity: sha1-nXezfArVLDcN41cyQJk9Q9boIUE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.4':
    resolution: {integrity: sha1-oYOUJaqnpCpGXVgBf1PDgX2YrD0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.4':
    resolution: {integrity: sha1-vxGpvyGR2WS7j2ltLqkEtVFAuAA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-x64-musl@4.1.4':
    resolution: {integrity: sha1-EcdClUOVHPowgBbUqVerakGSs38=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-wasm32-wasi@4.1.4':
    resolution: {integrity: sha1-LGsauh8IbDM3YlzbM3LDlVgydow=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.4.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.4':
    resolution: {integrity: sha1-/9/tPWEgNCjUSPUuNRhfhaDvmFY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.4':
    resolution: {integrity: sha1-Crt5IFZLz12vq8VpFO7qOFR6Msk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.4.tgz}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.4':
    resolution: {integrity: sha1-vzvOYTELZL1H9h8SCDrkkDqRuo4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/oxide/-/oxide-4.1.4.tgz}
    engines: {node: '>= 10'}

  '@tailwindcss/vite@4.1.4':
    resolution: {integrity: sha1-SuZgCOP2lJm3qVG6QqpLw8svfNA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@tailwindcss/vite/-/vite-4.1.4.tgz}
    peerDependencies:
      vite: ^5.2.0 || ^6

  '@trysound/sax@0.2.0':
    resolution: {integrity: sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@trysound/sax/-/sax-0.2.0.tgz}
    engines: {node: '>=10.13.0'}

  '@types/conventional-commits-parser@5.0.1':
    resolution: {integrity: sha1-jLgc8XCFNJbLxQGjsy3PXkb/tho=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/conventional-commits-parser/-/conventional-commits-parser-5.0.1.tgz}

  '@types/estree@1.0.7':
    resolution: {integrity: sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/estree/-/estree-1.0.7.tgz}

  '@types/js-cookie@3.0.6':
    resolution: {integrity: sha1-oEyhnod2h71En1rTfTOxBLcf35U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/js-cookie/-/js-cookie-3.0.6.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/json-schema/-/json-schema-7.0.15.tgz}

  '@types/lodash-es@4.17.12':
    resolution: {integrity: sha1-ZfbR5fgFOap8+/yWLeXe8M9PNBs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/lodash-es/-/lodash-es-4.17.12.tgz}

  '@types/lodash@4.17.16':
    resolution: {integrity: sha1-lK54+rSjjXMIbpYtC2XDDYFr+wo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/lodash/-/lodash-4.17.16.tgz}

  '@types/node@20.17.32':
    resolution: {integrity: sha1-y5cDUUzY4XLBG+/1gsZgBmRMLYg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/node/-/node-20.17.32.tgz}

  '@types/nprogress@0.2.3':
    resolution: {integrity: sha1-shULBUoTYi+ry6Es9vC1TEixQoc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/nprogress/-/nprogress-0.2.3.tgz}

  '@types/path-browserify@1.0.3':
    resolution: {integrity: sha1-Jd5xLU3vlLOQHwM8MNPTvRbrqNM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/path-browserify/-/path-browserify-1.0.3.tgz}

  '@types/qs@6.9.18':
    resolution: {integrity: sha1-h3KSyqkffBshMDKzRiZQW3RmJMI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/qs/-/qs-6.9.18.tgz}

  '@types/sortablejs@1.15.8':
    resolution: {integrity: sha1-Ee1VUHYEbgCGml74XR52UeembvY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/sortablejs/-/sortablejs-1.15.8.tgz}

  '@types/tinycolor2@1.4.6':
    resolution: {integrity: sha1-Zwy8DK9OWN1h0eOm8mOG5HMIfwY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/tinycolor2/-/tinycolor2-1.4.6.tgz}

  '@types/web-bluetooth@0.0.16':
    resolution: {integrity: sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz}

  '@types/web-bluetooth@0.0.21':
    resolution: {integrity: sha1-UlQzx4Su2bRXqqDuPZKutx80a2M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@types/web-bluetooth/-/web-bluetooth-0.0.21.tgz}

  '@typescript-eslint/eslint-plugin@8.31.1':
    resolution: {integrity: sha1-YvG+/llkdSSZToneRRbY3Lp6hQo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.31.1':
    resolution: {integrity: sha1-6bDM8w033eck7k0V9NvBlZlczhs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/parser/-/parser-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.31.1':
    resolution: {integrity: sha1-HrUudoePVF5K3RQuDY4+l+eqRDs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/scope-manager/-/scope-manager-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/type-utils@8.31.1':
    resolution: {integrity: sha1-vg9Dj7JLA1aOKCoK7YX3dkCflww=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/type-utils/-/type-utils-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.31.1':
    resolution: {integrity: sha1-R47W9+iu4b57Y6YCEra//hQjtdQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/types/-/types-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.31.1':
    resolution: {integrity: sha1-N3kv5+9NMCHHWABnyPGuZtqrrN8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/typescript-estree/-/typescript-estree-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.31.1':
    resolution: {integrity: sha1-VijqA5NZigsvFD0PxtAZ8N7p3RQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/utils/-/utils-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.31.1':
    resolution: {integrity: sha1-Z0Kw47oeDB41va94wD51nrjdjnU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@typescript-eslint/visitor-keys/-/visitor-keys-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vitejs/plugin-vue-jsx@4.1.2':
    resolution: {integrity: sha1-ergTvLqaUUor3uWw4Xww1uQgx0g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vitejs/plugin-vue-jsx/-/plugin-vue-jsx-4.1.2.tgz}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.0.0

  '@vitejs/plugin-vue@5.2.3':
    resolution: {integrity: sha1-caj8gtTS5CWvMEw1vziVBvZ02Js=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vitejs/plugin-vue/-/plugin-vue-5.2.3.tgz}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25

  '@volar/language-core@2.4.13':
    resolution: {integrity: sha1-PgPB1G5t6CNk5Hz9ml6v2daHBqY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@volar/language-core/-/language-core-2.4.13.tgz}

  '@volar/source-map@2.4.13':
    resolution: {integrity: sha1-cie9Ofa+SEhaUr/yTpzyxdtm6G0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@volar/source-map/-/source-map-2.4.13.tgz}

  '@volar/typescript@2.4.13':
    resolution: {integrity: sha1-H1tvGqHmioACmLohfCl8vFMY+K4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@volar/typescript/-/typescript-2.4.13.tgz}

  '@vue/babel-helper-vue-transform-on@1.4.0':
    resolution: {integrity: sha1-YWAgSIaSqcQqYTKA1i7RtycEXZU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.4.0.tgz}

  '@vue/babel-plugin-jsx@1.4.0':
    resolution: {integrity: sha1-wVXHlc6YDt9Gqm/s7tk5Ralcplg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true

  '@vue/babel-plugin-resolve-type@1.4.0':
    resolution: {integrity: sha1-TTV6gfsMycrQ6MgbEYEVvaLFFUM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.4.0.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.5.13':
    resolution: {integrity: sha1-sK5sQ0f2DAPoSaBdNOW/dHyb2gU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/compiler-core/-/compiler-core-3.5.13.tgz}

  '@vue/compiler-dom@3.5.13':
    resolution: {integrity: sha1-uxuHWNvFQrNljdqXO5ihyTEailg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/compiler-dom/-/compiler-dom-3.5.13.tgz}

  '@vue/compiler-sfc@3.5.13':
    resolution: {integrity: sha1-Rh+L00O1wG+sQYnE/vivMt6oK0Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/compiler-sfc/-/compiler-sfc-3.5.13.tgz}

  '@vue/compiler-ssr@3.5.13':
    resolution: {integrity: sha1-53GtzKbT0AD5GkJ3yXKpltB/Q7o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/compiler-ssr/-/compiler-ssr-3.5.13.tgz}

  '@vue/compiler-vue2@2.7.16':
    resolution: {integrity: sha1-K6g3y9PxszwryGX74aO1P7YR4kk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/compiler-vue2/-/compiler-vue2-2.7.16.tgz}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/devtools-api/-/devtools-api-6.6.4.tgz}

  '@vue/devtools-api@7.7.6':
    resolution: {integrity: sha1-SvXbx3vMhUPwqObwKfWY7ZeNbH0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/devtools-api/-/devtools-api-7.7.6.tgz}

  '@vue/devtools-kit@7.7.6':
    resolution: {integrity: sha1-PZy+I3imXtfEuqd+zA9+zf0YX7s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/devtools-kit/-/devtools-kit-7.7.6.tgz}

  '@vue/devtools-shared@7.7.6':
    resolution: {integrity: sha1-XaIhjfYbYFt7iOclJB/GZA3w5LU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/devtools-shared/-/devtools-shared-7.7.6.tgz}

  '@vue/language-core@2.2.10':
    resolution: {integrity: sha1-WuHnGk4W3VnR5LrBZ/S5yMBNnxc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/language-core/-/language-core-2.2.10.tgz}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity@3.5.13':
    resolution: {integrity: sha1-tB/yu4ZeCTiZoiIZ9bJfl7b+FV8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/reactivity/-/reactivity-3.5.13.tgz}

  '@vue/runtime-core@3.5.13':
    resolution: {integrity: sha1-H6+kvwuXrw692dv+mM1jDaNjpFU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/runtime-core/-/runtime-core-3.5.13.tgz}

  '@vue/runtime-dom@3.5.13':
    resolution: {integrity: sha1-YQ/Hld6SRjAOiuiGWTDVNOEkYhU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/runtime-dom/-/runtime-dom-3.5.13.tgz}

  '@vue/server-renderer@3.5.13':
    resolution: {integrity: sha1-Qp6tYu5R3niWRsIu/pCOSJqtRvc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/server-renderer/-/server-renderer-3.5.13.tgz}
    peerDependencies:
      vue: 3.5.13

  '@vue/shared@3.5.13':
    resolution: {integrity: sha1-h7MJpjecIrkm5paJMjeCb2Qzm28=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vue/shared/-/shared-3.5.13.tgz}

  '@vueuse/core@13.1.0':
    resolution: {integrity: sha1-1ZZMOR5NT+o0B5CYGcRd5Ly1ghE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/core/-/core-13.1.0.tgz}
    peerDependencies:
      vue: ^3.5.0

  '@vueuse/core@9.13.0':
    resolution: {integrity: sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/core/-/core-9.13.0.tgz}

  '@vueuse/metadata@13.1.0':
    resolution: {integrity: sha1-lZ+PLaWxj7m4BSlxTd22q2kTHYI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/metadata/-/metadata-13.1.0.tgz}

  '@vueuse/metadata@9.13.0':
    resolution: {integrity: sha1-vCWmza0bGpPDbOMBkRJNplIFOf8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/metadata/-/metadata-9.13.0.tgz}

  '@vueuse/motion@3.0.3':
    resolution: {integrity: sha1-lPixi//5Ws08JMk4oFHa8G4T7Q8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/motion/-/motion-3.0.3.tgz}
    peerDependencies:
      vue: '>=3.0.0'

  '@vueuse/shared@13.1.0':
    resolution: {integrity: sha1-sBrRCPDePxsC4Fkcb8VKnuACkpg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/shared/-/shared-13.1.0.tgz}
    peerDependencies:
      vue: ^3.5.0

  '@vueuse/shared@9.13.0':
    resolution: {integrity: sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/@vueuse/shared/-/shared-9.13.0.tgz}

  JSONStream@1.3.5:
    resolution: {integrity: sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/JSONStream/-/JSONStream-1.3.5.tgz}
    hasBin: true

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.1:
    resolution: {integrity: sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/acorn/-/acorn-8.14.1.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ajv/-/ajv-6.12.6.tgz}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ajv/-/ajv-8.17.1.tgz}

  alien-signals@1.0.13:
    resolution: {integrity: sha1-jW23NGL3Qu5riWcfvYw30LFyen4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/alien-signals/-/alien-signals-1.0.13.tgz}

  animate.css@4.1.1:
    resolution: {integrity: sha1-YU7FqBEx1+TcNipYFD90BqvWgHU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/animate.css/-/animate.css-4.1.1.tgz}

  ansi-align@3.0.1:
    resolution: {integrity: sha1-DN8S4RGs53OobpofrRIlxDyxmlk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-align/-/ansi-align-3.0.1.tgz}

  ansi-escapes@7.0.0:
    resolution: {integrity: sha1-APwZ9JG7sY4dSBuXhoIE+SEJv+c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-escapes/-/ansi-escapes-7.0.0.tgz}
    engines: {node: '>=18'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-regex/-/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-regex/-/ansi-regex-6.1.0.tgz}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-styles/-/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ansi-styles/-/ansi-styles-6.2.1.tgz}
    engines: {node: '>=12'}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/argparse/-/argparse-2.0.1.tgz}

  array-ify@1.0.0:
    resolution: {integrity: sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/array-ify/-/array-ify-1.0.0.tgz}

  array-union@2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/array-union/-/array-union-2.1.0.tgz}
    engines: {node: '>=8'}

  astral-regex@2.0.0:
    resolution: {integrity: sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/astral-regex/-/astral-regex-2.0.0.tgz}
    engines: {node: '>=8'}

  async-validator@4.2.5:
    resolution: {integrity: sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/async-validator/-/async-validator-4.2.5.tgz}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/async/-/async-3.2.6.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/asynckit/-/asynckit-0.4.0.tgz}

  axios@1.9.0:
    resolution: {integrity: sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/axios/-/axios-1.9.0.tgz}

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/balanced-match/-/balanced-match-1.0.2.tgz}

  balanced-match@2.0.0:
    resolution: {integrity: sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/balanced-match/-/balanced-match-2.0.0.tgz}

  base64-js@1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/base64-js/-/base64-js-1.5.1.tgz}

  birpc@2.3.0:
    resolution: {integrity: sha1-5aQC3Hhe+VKiOD7zz8B14IQvPow=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/birpc/-/birpc-2.3.0.tgz}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/boolbase/-/boolbase-1.0.0.tgz}

  boxen@8.0.1:
    resolution: {integrity: sha1-fp/LtF4Rotfm2qj9zr/DJC/Bn+M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/boxen/-/boxen-8.0.1.tgz}
    engines: {node: '>=18'}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/brace-expansion/-/brace-expansion-1.1.11.tgz}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/brace-expansion/-/brace-expansion-2.0.1.tgz}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/braces/-/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  browserslist@4.24.4:
    resolution: {integrity: sha1-xrKGWj8IvLhgoOgnOJADuf5obks=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/browserslist/-/browserslist-4.24.4.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer@6.0.3:
    resolution: {integrity: sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/buffer/-/buffer-6.0.3.tgz}

  bundle-import@0.0.2:
    resolution: {integrity: sha1-KJqDi93kVWbjTdmsS0LAVVimF1k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/bundle-import/-/bundle-import-0.0.2.tgz}

  c12@3.0.3:
    resolution: {integrity: sha1-TW1NNfCEYG/2FtG8rmDmZ26s1L0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/c12/-/c12-3.0.3.tgz}
    peerDependencies:
      magicast: ^0.3.5
    peerDependenciesMeta:
      magicast:
        optional: true

  cacheable@1.8.10:
    resolution: {integrity: sha1-wbNiYCQNgSkSptNQPaOm4AFm91w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cacheable/-/cacheable-1.8.10.tgz}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/call-bound/-/call-bound-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/callsites/-/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camelcase@8.0.0:
    resolution: {integrity: sha1-wNNtQYdT+2rZxeBDdXl0XBwUpTQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/camelcase/-/camelcase-8.0.0.tgz}
    engines: {node: '>=16'}

  caniuse-api@3.0.0:
    resolution: {integrity: sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/caniuse-api/-/caniuse-api-3.0.0.tgz}

  caniuse-lite@1.0.30001716:
    resolution: {integrity: sha1-OSIN+8WMhdnUUZ5wkLZWqhHKS4U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/caniuse-lite/-/caniuse-lite-1.0.30001716.tgz}

  chalk@4.1.1:
    resolution: {integrity: sha1-yAs/qyi/Y3HmhjMl7uZ+YYt35q0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/chalk/-/chalk-4.1.1.tgz}
    engines: {node: '>=10'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/chalk/-/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/chalk/-/chalk-5.4.1.tgz}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  chokidar@4.0.3:
    resolution: {integrity: sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/chokidar/-/chokidar-4.0.3.tgz}
    engines: {node: '>= 14.16.0'}

  citty@0.1.6:
    resolution: {integrity: sha1-D3kE2h7UYl4anqfg+ngJgaq3xeQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/citty/-/citty-0.1.6.tgz}

  cli-boxes@3.0.0:
    resolution: {integrity: sha1-caEMcW/uugBeRQTzYynvCxfPMUU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cli-boxes/-/cli-boxes-3.0.0.tgz}
    engines: {node: '>=10'}

  cli-cursor@5.0.0:
    resolution: {integrity: sha1-JKSDHs9aawHd6zL7caSyCIsNzjg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cli-cursor/-/cli-cursor-5.0.0.tgz}
    engines: {node: '>=18'}

  cli-truncate@4.0.0:
    resolution: {integrity: sha1-bMKKKST+6eJc6R6XPbVscGbmFyo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cli-truncate/-/cli-truncate-4.0.0.tgz}
    engines: {node: '>=18'}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cliui/-/cliui-8.0.1.tgz}
    engines: {node: '>=12'}

  code-inspector-core@0.20.10:
    resolution: {integrity: sha1-/cgR2/hUUExlcvj8f91WmJyfl2Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/code-inspector-core/-/code-inspector-core-0.20.10.tgz}

  code-inspector-plugin@0.20.10:
    resolution: {integrity: sha1-MnDNTo/N0GWL0GShpgvfVO7Di64=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/code-inspector-plugin/-/code-inspector-plugin-0.20.10.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/color-convert/-/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/color-name/-/color-name-1.1.4.tgz}

  colord@2.9.3:
    resolution: {integrity: sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/colord/-/colord-2.9.3.tgz}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/colorette/-/colorette-2.0.20.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/combined-stream/-/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@13.1.0:
    resolution: {integrity: sha1-d2Fn22jHjzjczh+bjXuLmkiKv0Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/commander/-/commander-13.1.0.tgz}
    engines: {node: '>=18'}

  commander@7.2.0:
    resolution: {integrity: sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/commander/-/commander-7.2.0.tgz}
    engines: {node: '>= 10'}

  compare-func@2.0.0:
    resolution: {integrity: sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/compare-func/-/compare-func-2.0.0.tgz}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/concat-map/-/concat-map-0.0.1.tgz}

  confbox@0.1.8:
    resolution: {integrity: sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/confbox/-/confbox-0.1.8.tgz}

  confbox@0.2.2:
    resolution: {integrity: sha1-hlL1OWHHTZ4IF4S+7XhVWXSpwRA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/confbox/-/confbox-0.2.2.tgz}

  consola@3.4.2:
    resolution: {integrity: sha1-WvEQFFOXu2ev2rdwE/3DTK5ZDqc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/consola/-/consola-3.4.2.tgz}
    engines: {node: ^14.18.0 || >=16.10.0}

  conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha1-XuyO2/8VqpsWgKjc+9U+LX6yuno=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/conventional-changelog-angular/-/conventional-changelog-angular-7.0.0.tgz}
    engines: {node: '>=16'}

  conventional-changelog-conventionalcommits@7.0.2:
    resolution: {integrity: sha1-ql2g8bJUMJSInoz3YW6+Go9ccNU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-7.0.2.tgz}
    engines: {node: '>=16'}

  conventional-commits-parser@5.0.0:
    resolution: {integrity: sha1-V/NZS4GtVNQMG0KA8EVU3yhifZo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/conventional-commits-parser/-/conventional-commits-parser-5.0.0.tgz}
    engines: {node: '>=16'}
    hasBin: true

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/convert-source-map/-/convert-source-map-2.0.0.tgz}

  copy-anything@3.0.5:
    resolution: {integrity: sha1-LZLc6MSY95D6etFrAaGuWkWwIKA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/copy-anything/-/copy-anything-3.0.5.tgz}
    engines: {node: '>=12.13'}

  cosmiconfig-typescript-loader@6.1.0:
    resolution: {integrity: sha1-f2RFA+HCv/kK7S0ppjcAjyeWRrs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cosmiconfig-typescript-loader/-/cosmiconfig-typescript-loader-6.1.0.tgz}
    engines: {node: '>=v18'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=9'
      typescript: '>=5'

  cosmiconfig@9.0.0:
    resolution: {integrity: sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cosmiconfig/-/cosmiconfig-9.0.0.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cross-spawn/-/cross-spawn-7.0.6.tgz}
    engines: {node: '>= 8'}

  css-declaration-sorter@7.2.0:
    resolution: {integrity: sha1-bewclSO8SmQ+CIqrjwnmelSWECQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-declaration-sorter/-/css-declaration-sorter-7.2.0.tgz}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.0.9

  css-functions-list@3.2.3:
    resolution: {integrity: sha1-lWUrDCTw9ZspGp/DhgQaGdT0Db4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-functions-list/-/css-functions-list-3.2.3.tgz}
    engines: {node: '>=12 || >=16'}

  css-select@5.1.0:
    resolution: {integrity: sha1-uOvWVUw2N8zHZoiAStP2pv2uqKY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-select/-/css-select-5.1.0.tgz}

  css-tree@2.2.1:
    resolution: {integrity: sha1-NhFdOC1gr9Jx43f5xfZ9Ar1IwDI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-tree/-/css-tree-2.2.1.tgz}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  css-tree@2.3.1:
    resolution: {integrity: sha1-ECZM4eVELoVy/IL75JBkT/VLXCA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-tree/-/css-tree-2.3.1.tgz}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}

  css-tree@3.1.0:
    resolution: {integrity: sha1-eqvANfTma1yG9UVw1V4FsTRusP0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-tree/-/css-tree-3.1.0.tgz}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}

  css-what@6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/css-what/-/css-what-6.1.0.tgz}
    engines: {node: '>= 6'}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cssesc/-/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  cssnano-preset-default@7.0.6:
    resolution: {integrity: sha1-AiD6dQdHg2mqKiJrrAPhIEzQJME=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cssnano-preset-default/-/cssnano-preset-default-7.0.6.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  cssnano-utils@5.0.0:
    resolution: {integrity: sha1-tToDQ91dIQEpEYgttq59Lq4ONoc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cssnano-utils/-/cssnano-utils-5.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  cssnano@7.0.6:
    resolution: {integrity: sha1-Y9VP1CvAF/aq7WnkfZqu+Ft4UOw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/cssnano/-/cssnano-7.0.6.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  csso@5.0.5:
    resolution: {integrity: sha1-+bf+bMasC32QeBuxbV6YdDA+LKY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/csso/-/csso-5.0.5.tgz}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/csstype/-/csstype-3.1.3.tgz}

  dargs@8.1.0:
    resolution: {integrity: sha1-o0hZ6lCcvORUheWqNW/vcL/McnI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dargs/-/dargs-8.1.0.tgz}
    engines: {node: '>=12'}

  dayjs@1.11.13:
    resolution: {integrity: sha1-kkMLATkFXD67YBUKoT6GCktaNmw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dayjs/-/dayjs-1.11.13.tgz}

  de-indent@1.0.2:
    resolution: {integrity: sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/de-indent/-/de-indent-1.0.2.tgz}

  debug@4.4.0:
    resolution: {integrity: sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/debug/-/debug-4.4.0.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/deep-is/-/deep-is-0.1.4.tgz}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz}
    engines: {node: '>=8'}

  defu@6.1.4:
    resolution: {integrity: sha1-Tgyc+f9o/l89fydlzBoBLf3LBHk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/defu/-/defu-6.1.4.tgz}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/delayed-stream/-/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  destr@2.0.5:
    resolution: {integrity: sha1-fREv8bkl+40gefrFvbSpCXO1H9s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/destr/-/destr-2.0.5.tgz}

  detect-libc@1.0.3:
    resolution: {integrity: sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/detect-libc/-/detect-libc-1.0.3.tgz}
    engines: {node: '>=0.10'}
    hasBin: true

  detect-libc@2.0.4:
    resolution: {integrity: sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/detect-libc/-/detect-libc-2.0.4.tgz}
    engines: {node: '>=8'}

  dir-glob@3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dir-glob/-/dir-glob-3.0.1.tgz}
    engines: {node: '>=8'}

  dom-serializer@2.0.0:
    resolution: {integrity: sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dom-serializer/-/dom-serializer-2.0.0.tgz}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/domelementtype/-/domelementtype-2.3.0.tgz}

  domhandler@5.0.3:
    resolution: {integrity: sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/domhandler/-/domhandler-5.0.3.tgz}
    engines: {node: '>= 4'}

  domutils@3.2.2:
    resolution: {integrity: sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/domutils/-/domutils-3.2.2.tgz}

  dot-prop@5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dot-prop/-/dot-prop-5.3.0.tgz}
    engines: {node: '>=8'}

  dotenv@16.5.0:
    resolution: {integrity: sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dotenv/-/dotenv-16.5.0.tgz}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/dunder-proto/-/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eastasianwidth/-/eastasianwidth-0.2.0.tgz}

  echarts@5.6.0:
    resolution: {integrity: sha1-I3eHTcqftQ8QQFHDVTVEdS2jydY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/echarts/-/echarts-5.6.0.tgz}

  electron-to-chromium@1.5.145:
    resolution: {integrity: sha1-q9UHAKwsgJ5ApGlFhPZnEe6Tf7Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/electron-to-chromium/-/electron-to-chromium-1.5.145.tgz}

  element-plus@2.9.9:
    resolution: {integrity: sha1-F2ed0uEdcO0TIhajlQ2d8ycFPQQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/element-plus/-/element-plus-2.9.9.tgz}
    peerDependencies:
      vue: ^3.2.0

  emoji-regex@10.4.0:
    resolution: {integrity: sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/emoji-regex/-/emoji-regex-10.4.0.tgz}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/emoji-regex/-/emoji-regex-8.0.0.tgz}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/emoji-regex/-/emoji-regex-9.2.2.tgz}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha1-coqwgvi3toNt5R8WN6q107lWj68=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/entities/-/entities-4.5.0.tgz}
    engines: {node: '>=0.12'}

  env-paths@2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/env-paths/-/env-paths-2.2.1.tgz}
    engines: {node: '>=6'}

  environment@1.1.0:
    resolution: {integrity: sha1-jobGaxgPNjx6sxF4fgJZZl9FqfE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/environment/-/environment-1.1.0.tgz}
    engines: {node: '>=18'}

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/error-ex/-/error-ex-1.3.2.tgz}

  errx@0.1.0:
    resolution: {integrity: sha1-SIHkEdkKOx4WIKB2BPUAgd1Z86o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/errx/-/errx-0.1.0.tgz}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/es-define-property/-/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/es-errors/-/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-module-lexer@0.4.1:
    resolution: {integrity: sha1-3ajGoU2PNAok40Mx4Pqwy1BDjg4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/es-module-lexer/-/es-module-lexer-0.4.1.tgz}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/es-object-atoms/-/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}

  esbuild-code-inspector-plugin@0.20.10:
    resolution: {integrity: sha1-vJGjvKogRYsHOZ3OrOUBMslAgoc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esbuild-code-inspector-plugin/-/esbuild-code-inspector-plugin-0.20.10.tgz}

  esbuild@0.24.2:
    resolution: {integrity: sha1-tbVb7n3gF7/1+4pOPkTy6+LDVn0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esbuild/-/esbuild-0.24.2.tgz}
    engines: {node: '>=18'}
    hasBin: true

  esbuild@0.25.3:
    resolution: {integrity: sha1-Nx98tBKD5bIZGpYEenqJVillooU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esbuild/-/esbuild-0.25.3.tgz}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/escalade/-/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/escape-html/-/escape-html-1.0.3.tgz}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz}
    engines: {node: '>=12'}

  eslint-config-prettier@10.1.2:
    resolution: {integrity: sha1-MaSzk8QMQYAgLCfoKa9DMjv4UnY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-config-prettier/-/eslint-config-prettier-10.1.2.tgz}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@5.2.6:
    resolution: {integrity: sha1-vjnjuyO7Put+ffCSfNtG5NeUUJY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-plugin-prettier/-/eslint-plugin-prettier-5.2.6.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-vue@10.0.1:
    resolution: {integrity: sha1-UZQxjrdvmMz3t9KPDHEDhCx/SA8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-plugin-vue/-/eslint-plugin-vue-10.0.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      vue-eslint-parser: ^10.0.0

  eslint-scope@8.3.0:
    resolution: {integrity: sha1-EM06kY/91yL18/e1uD25sjyHNA0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-scope/-/eslint-scope-8.3.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha1-aHussq+IT83aim59ZcYG9GoUzUU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.25.1:
    resolution: {integrity: sha1-inz43Q5qy4WPhgKXIK2xeF7ldYA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eslint/-/eslint-9.25.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.3.0:
    resolution: {integrity: sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/espree/-/espree-10.3.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esquery/-/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esrecurse/-/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/estraverse/-/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/estree-walker/-/estree-walker-2.0.2.tgz}

  estree-walker@3.0.3:
    resolution: {integrity: sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/estree-walker/-/estree-walker-3.0.3.tgz}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/esutils/-/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/eventemitter3/-/eventemitter3-5.0.1.tgz}

  execa@8.0.1:
    resolution: {integrity: sha1-UfallDtYD5Y8PKnGMheW24zDm4w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/execa/-/execa-8.0.1.tgz}
    engines: {node: '>=16.17'}

  exsolve@1.0.5:
    resolution: {integrity: sha1-H1trT+gq1rKKFzzLlVpjXXeFnc8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/exsolve/-/exsolve-1.0.5.tgz}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}

  fast-diff@1.3.0:
    resolution: {integrity: sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-diff/-/fast-diff-1.3.0.tgz}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-glob/-/fast-glob-3.3.3.tgz}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fast-uri/-/fast-uri-3.0.6.tgz}

  fastest-levenshtein@1.0.16:
    resolution: {integrity: sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz}
    engines: {node: '>= 4.9.1'}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fastq/-/fastq-1.19.1.tgz}

  fdir@6.4.4:
    resolution: {integrity: sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fdir/-/fdir-6.4.4.tgz}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-entry-cache@10.0.8:
    resolution: {integrity: sha1-K3oyxAYVxKa1nDhfsFmidi+vliQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/file-entry-cache/-/file-entry-cache-10.0.8.tgz}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/file-entry-cache/-/file-entry-cache-8.0.0.tgz}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fill-range/-/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/find-up/-/find-up-5.0.0.tgz}
    engines: {node: '>=10'}

  find-up@7.0.0:
    resolution: {integrity: sha1-6N7BRV90942IitZb98oT3StOZvs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/find-up/-/find-up-7.0.0.tgz}
    engines: {node: '>=18'}

  flat-cache@4.0.1:
    resolution: {integrity: sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/flat-cache/-/flat-cache-4.0.1.tgz}
    engines: {node: '>=16'}

  flat-cache@6.1.8:
    resolution: {integrity: sha1-lo+4mxnfSI/mDzRoV//FS43QuhQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/flat-cache/-/flat-cache-6.1.8.tgz}

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/flatted/-/flatted-3.3.3.tgz}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/follow-redirects/-/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.3.1:
    resolution: {integrity: sha1-Mujp7Rtoo0l777msK2rfkqY4V28=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/foreground-child/-/foreground-child-3.3.1.tgz}
    engines: {node: '>=14'}

  form-data@4.0.2:
    resolution: {integrity: sha1-Ncq73TDDznPessQtPI0+2cpReUw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/form-data/-/form-data-4.0.2.tgz}
    engines: {node: '>= 6'}

  framesync@6.1.2:
    resolution: {integrity: sha1-dV7/L7W487TSsmbdGBIbMArv6ic=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/framesync/-/framesync-6.1.2.tgz}

  fs-extra@10.1.0:
    resolution: {integrity: sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fs-extra/-/fs-extra-10.1.0.tgz}
    engines: {node: '>=12'}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/fsevents/-/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/function-bind/-/function-bind-1.1.2.tgz}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/gensync/-/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-caller-file/-/get-caller-file-2.0.5.tgz}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-east-asian-width@1.3.0:
    resolution: {integrity: sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-east-asian-width/-/get-east-asian-width-1.3.0.tgz}
    engines: {node: '>=18'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-intrinsic/-/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-proto/-/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  get-stream@8.0.1:
    resolution: {integrity: sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-stream/-/get-stream-8.0.1.tgz}
    engines: {node: '>=16'}

  get-tsconfig@4.10.0:
    resolution: {integrity: sha1-QDpoKzc6gjYSR1pMKSjHMm/A9rs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/get-tsconfig/-/get-tsconfig-4.10.0.tgz}

  giget@2.0.0:
    resolution: {integrity: sha1-OV/JNKQ/mnopop1VuZ8j4wwU8ZU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/giget/-/giget-2.0.0.tgz}
    hasBin: true

  git-raw-commits@4.0.0:
    resolution: {integrity: sha1-shL9K/+XJtJ8EoOhFX6ClJBZMoU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/git-raw-commits/-/git-raw-commits-4.0.0.tgz}
    engines: {node: '>=16'}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/glob-parent/-/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/glob-parent/-/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}

  glob@11.0.2:
    resolution: {integrity: sha1-MmHjiXu8YDAwsEH9d7pjYCLVHOA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/glob/-/glob-11.0.2.tgz}
    engines: {node: 20 || >=22}
    hasBin: true

  global-directory@4.0.1:
    resolution: {integrity: sha1-TXrHz9LLc/MExTuIEIkXSN9eNh4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/global-directory/-/global-directory-4.0.1.tgz}
    engines: {node: '>=18'}

  global-modules@2.0.0:
    resolution: {integrity: sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/global-modules/-/global-modules-2.0.0.tgz}
    engines: {node: '>=6'}

  global-prefix@3.0.0:
    resolution: {integrity: sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/global-prefix/-/global-prefix-3.0.0.tgz}
    engines: {node: '>=6'}

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/globals/-/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha1-iY10E8Kbq89rr+Vvyt3thYrack4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/globals/-/globals-14.0.0.tgz}
    engines: {node: '>=18'}

  globals@15.15.0:
    resolution: {integrity: sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/globals/-/globals-15.15.0.tgz}
    engines: {node: '>=18'}

  globby@11.1.0:
    resolution: {integrity: sha1-vUvpi7BC+D15b344EZkfvoKg00s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/globby/-/globby-11.1.0.tgz}
    engines: {node: '>=10'}

  globjoin@0.1.4:
    resolution: {integrity: sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/globjoin/-/globjoin-0.1.4.tgz}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/gopd/-/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/graceful-fs/-/graceful-fs-4.2.11.tgz}

  gradient-string@3.0.0:
    resolution: {integrity: sha1-cW1tbzUwlRP6ktOPUGxt8M4fXrs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/gradient-string/-/gradient-string-3.0.0.tgz}
    engines: {node: '>=14'}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/graphemer/-/graphemer-1.4.0.tgz}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/has-flag/-/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/has-symbols/-/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/has-tostringtag/-/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/hasown/-/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/he/-/he-1.2.0.tgz}
    hasBin: true

  hey-listen@1.0.8:
    resolution: {integrity: sha1-jllWH/ckkI3hqpJO1uzISlapqmg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/hey-listen/-/hey-listen-1.0.8.tgz}

  hookable@5.5.3:
    resolution: {integrity: sha1-bPw1iYSh75keJRjLntSneLvTIV0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/hookable/-/hookable-5.5.3.tgz}

  hookified@1.8.2:
    resolution: {integrity: sha1-s2Wonfzj2kPnkGc6apfTuJauX6c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/hookified/-/hookified-1.8.2.tgz}

  html-tags@3.3.1:
    resolution: {integrity: sha1-oEAmoYyILku6igGj05z+Rl1Atc4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/html-tags/-/html-tags-3.3.1.tgz}
    engines: {node: '>=8'}

  htmlparser2@8.0.2:
    resolution: {integrity: sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/htmlparser2/-/htmlparser2-8.0.2.tgz}

  human-signals@5.0.0:
    resolution: {integrity: sha1-QmZaKE+a4NreO6QevDfrS4UvOig=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/human-signals/-/human-signals-5.0.0.tgz}
    engines: {node: '>=16.17.0'}

  husky@9.1.7:
    resolution: {integrity: sha1-1Go4A10QG0anBFaoUP9CATRMCy0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/husky/-/husky-9.1.7.tgz}
    engines: {node: '>=18'}
    hasBin: true

  ieee754@1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ieee754/-/ieee754-1.2.1.tgz}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ignore/-/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  ignore@7.0.4:
    resolution: {integrity: sha1-oSxw0PJgfFv1CPtlpAx18DfXoHg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ignore/-/ignore-7.0.4.tgz}
    engines: {node: '>= 4'}

  immediate@3.0.6:
    resolution: {integrity: sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/immediate/-/immediate-3.0.6.tgz}

  immutable@5.1.1:
    resolution: {integrity: sha1-1MtVJobzSwdrPc8jxDhMBEJNg1Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/immutable/-/immutable-5.1.1.tgz}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/import-fresh/-/import-fresh-3.3.1.tgz}
    engines: {node: '>=6'}

  import-from-string@0.0.5:
    resolution: {integrity: sha1-fBVUXtQ9lDsMyGeofMGDOlQxe8Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/import-from-string/-/import-from-string-0.0.5.tgz}

  import-meta-resolve@4.1.0:
    resolution: {integrity: sha1-+duL6tn6+mGtuBHbd6K/IsU5lwY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/import-meta-resolve/-/import-meta-resolve-4.1.0.tgz}

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/imurmurhash/-/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  ini@1.3.8:
    resolution: {integrity: sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ini/-/ini-1.3.8.tgz}

  ini@4.1.1:
    resolution: {integrity: sha1-2Vs9hDsekG5W1nR9VEeQT/UM56E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ini/-/ini-4.1.1.tgz}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-arrayish/-/is-arrayish-0.2.1.tgz}

  is-docker@2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-docker/-/is-docker-2.2.1.tgz}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-extglob/-/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha1-+uMWfHKedGP4RhzlErCApJJoqog=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha1-lgnvztfC+X2ntgFF70gceHx7pwQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz}
    engines: {node: '>=18'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-glob/-/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-number/-/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-obj/-/is-obj-2.0.0.tgz}
    engines: {node: '>=8'}

  is-plain-object@5.0.0:
    resolution: {integrity: sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-plain-object/-/is-plain-object-5.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-reference@3.0.3:
    resolution: {integrity: sha1-nve/kCnHCmeyFS2krfV8I9cYkQ8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-reference/-/is-reference-3.0.3.tgz}

  is-stream@3.0.0:
    resolution: {integrity: sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-stream/-/is-stream-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-text-path@2.0.0:
    resolution: {integrity: sha1-skhOK3IKYz/rLoW2fcGT/3LHVjY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-text-path/-/is-text-path-2.0.0.tgz}
    engines: {node: '>=8'}

  is-what@4.1.16:
    resolution: {integrity: sha1-GthgoZ2otIla1Uldoxgs4qzdem8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-what/-/is-what-4.1.16.tgz}
    engines: {node: '>=12.13'}

  is-wsl@2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/is-wsl/-/is-wsl-2.2.0.tgz}
    engines: {node: '>=8'}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/isexe/-/isexe-2.0.0.tgz}

  jackspeak@4.1.0:
    resolution: {integrity: sha1-xInAefK2NtxMvpsDEqE/8SguVhs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/jackspeak/-/jackspeak-4.1.0.tgz}
    engines: {node: 20 || >=22}

  jiti@2.4.2:
    resolution: {integrity: sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/jiti/-/jiti-2.4.2.tgz}
    hasBin: true

  js-cookie@3.0.5:
    resolution: {integrity: sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/js-cookie/-/js-cookie-3.0.5.tgz}
    engines: {node: '>=14'}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/js-tokens/-/js-tokens-4.0.0.tgz}

  js-tokens@9.0.1:
    resolution: {integrity: sha1-LsQ5ZGWENSlvZ2GzThBnHC2VJ/Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/js-tokens/-/js-tokens-9.0.1.tgz}

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/js-yaml/-/js-yaml-4.1.0.tgz}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/jsesc/-/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json-buffer/-/json-buffer-3.0.1.tgz}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/json5/-/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/jsonfile/-/jsonfile-6.1.0.tgz}

  jsonparse@1.3.1:
    resolution: {integrity: sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/jsonparse/-/jsonparse-1.3.1.tgz}
    engines: {'0': node >= 0.2.0}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/keyv/-/keyv-4.5.4.tgz}

  keyv@5.3.3:
    resolution: {integrity: sha1-7C1yP717kI3l7n9Wt2nUbbvq+Lo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/keyv/-/keyv-5.3.3.tgz}

  kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/kind-of/-/kind-of-6.0.3.tgz}
    engines: {node: '>=0.10.0'}

  klona@2.0.6:
    resolution: {integrity: sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/klona/-/klona-2.0.6.tgz}
    engines: {node: '>= 8'}

  knitwork@1.2.0:
    resolution: {integrity: sha1-PMkudiSa6zVEnPvtPzHG34RE2z8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/knitwork/-/knitwork-1.2.0.tgz}

  known-css-properties@0.35.0:
    resolution: {integrity: sha1-9vjkCrTlcA+jL1su9SGKVryFO9Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/known-css-properties/-/known-css-properties-0.35.0.tgz}

  known-css-properties@0.36.0:
    resolution: {integrity: sha1-XENl88lUnKLoE9LnKebEfvamy2A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/known-css-properties/-/known-css-properties-0.36.0.tgz}

  kolorist@1.8.0:
    resolution: {integrity: sha1-7d27vHiUvBMwLN90CvY3TUoEdDw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/kolorist/-/kolorist-1.8.0.tgz}

  launch-ide@1.0.7:
    resolution: {integrity: sha1-rtDZDXccEnSGt2LHsxfQcTC5VCE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/launch-ide/-/launch-ide-1.0.7.tgz}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/levn/-/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}

  lie@3.1.1:
    resolution: {integrity: sha1-mkNrLMd0bKWd56QfpGmz77dr2H4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lie/-/lie-3.1.1.tgz}

  lightningcss-darwin-arm64@1.29.2:
    resolution: {integrity: sha1-bO/ziwETSvSOhZOU4coh5dSfquY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.29.2:
    resolution: {integrity: sha1-iRtvnldoLXlCI8M0Y8pm068/sDg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.29.2:
    resolution: {integrity: sha1-ipX5q3OysrC+7+FZn6+osFiThJU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.29.2:
    resolution: {integrity: sha1-XGC7+Ss51+1R42P3uYpxEb9ZFKE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.29.2:
    resolution: {integrity: sha1-5z12CMTM4DTDZU5ei1O+dIRiJN4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-arm64-musl@1.29.2:
    resolution: {integrity: sha1-qVoY1akJgxwJLgqNLeS5rBqNsVE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  lightningcss-linux-x64-gnu@1.29.2:
    resolution: {integrity: sha1-VRygflZTlJKGQu3ukqzAQuVGy3g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-x64-musl@1.29.2:
    resolution: {integrity: sha1-L9FkVUNAgxvOUChbVxAYF4UN0lg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  lightningcss-win32-arm64-msvc@1.29.2:
    resolution: {integrity: sha1-2kPqSfr8XS3jjgFvGoU51e7Zgxg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.29.2:
    resolution: {integrity: sha1-3e+qCZo5tyWy9bvcufxxhDXMl5c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.29.2:
    resolution: {integrity: sha1-9fD9bmMpKiMml+b+cJ2ltHYk3vM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lightningcss/-/lightningcss-1.29.2.tgz}
    engines: {node: '>= 12.0.0'}

  lilconfig@3.1.3:
    resolution: {integrity: sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lilconfig/-/lilconfig-3.1.3.tgz}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lines-and-columns/-/lines-and-columns-1.2.4.tgz}

  lint-staged@15.5.1:
    resolution: {integrity: sha1-beNSmJZGQbi24GDT2w+2rIZsbiQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lint-staged/-/lint-staged-15.5.1.tgz}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.3.2:
    resolution: {integrity: sha1-wlLsmjM0lQv8qSOEV9CtLBpcyGc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/listr2/-/listr2-8.3.2.tgz}
    engines: {node: '>=18.0.0'}

  local-pkg@1.1.1:
    resolution: {integrity: sha1-9f50qXo708FleI7gjKn76ZjcWN0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/local-pkg/-/local-pkg-1.1.1.tgz}
    engines: {node: '>=14'}

  localforage@1.10.0:
    resolution: {integrity: sha1-XEZdxfYrKAfDqEwMahsbMhJ4HdQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/localforage/-/localforage-1.10.0.tgz}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/locate-path/-/locate-path-6.0.0.tgz}
    engines: {node: '>=10'}

  locate-path@7.2.0:
    resolution: {integrity: sha1-acsXeb2Qs1qx53Hh8viaICwqioo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/locate-path/-/locate-path-7.2.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash-es@4.17.21:
    resolution: {integrity: sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash-es/-/lodash-es-4.17.21.tgz}

  lodash-unified@1.0.3:
    resolution: {integrity: sha1-gLHqwQ7S6wLtGJ8IYUopwn0HyJQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash-unified/-/lodash-unified-1.0.3.tgz}
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha1-soqmKIorn8ZRA1x3EfZathkDMaY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.memoize/-/lodash.memoize-4.1.2.tgz}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.merge/-/lodash.merge-4.6.2.tgz}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz}

  lodash.snakecase@4.1.1:
    resolution: {integrity: sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz}

  lodash.startcase@4.4.0:
    resolution: {integrity: sha1-lDbjTtJgk+1/+uGTYUQ1CRXZrdg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.startcase/-/lodash.startcase-4.4.0.tgz}

  lodash.truncate@4.4.2:
    resolution: {integrity: sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.truncate/-/lodash.truncate-4.4.2.tgz}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.uniq/-/lodash.uniq-4.5.0.tgz}

  lodash.upperfirst@4.3.1:
    resolution: {integrity: sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash.upperfirst/-/lodash.upperfirst-4.3.1.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lodash/-/lodash-4.17.21.tgz}

  log-update@6.1.0:
    resolution: {integrity: sha1-GgT/OBZvlGR64a9WL0vWoVsbfNQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/log-update/-/log-update-6.1.0.tgz}
    engines: {node: '>=18'}

  lru-cache@11.1.0:
    resolution: {integrity: sha1-r6+wYGBxCBMtvBz4rmYa+2lIYRc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lru-cache/-/lru-cache-11.1.0.tgz}
    engines: {node: 20 || >=22}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/lru-cache/-/lru-cache-5.1.1.tgz}

  magic-string@0.25.9:
    resolution: {integrity: sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/magic-string/-/magic-string-0.25.9.tgz}

  magic-string@0.30.17:
    resolution: {integrity: sha1-RQpElnPSRg5bvPupphkWoXFMdFM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/magic-string/-/magic-string-0.30.17.tgz}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/math-intrinsics/-/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  mathml-tag-names@2.1.3:
    resolution: {integrity: sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz}

  mdn-data@2.0.28:
    resolution: {integrity: sha1-XsSOe+8SBlRTkGnhrk3cgcpJDro=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mdn-data/-/mdn-data-2.0.28.tgz}

  mdn-data@2.0.30:
    resolution: {integrity: sha1-zk32+Ar2z74hjs1cVSuhPE36CMw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mdn-data/-/mdn-data-2.0.30.tgz}

  mdn-data@2.12.2:
    resolution: {integrity: sha1-mubEGp5lrfYTGLMr/3tk+/sT+M8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mdn-data/-/mdn-data-2.12.2.tgz}

  mdn-data@2.21.0:
    resolution: {integrity: sha1-86SV6LHmDLT76vkTau+6L5h6VuE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mdn-data/-/mdn-data-2.21.0.tgz}

  memoize-one@6.0.0:
    resolution: {integrity: sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/memoize-one/-/memoize-one-6.0.0.tgz}

  meow@12.1.1:
    resolution: {integrity: sha1-5Vjd26sSR3tpsumicowyfxkbrOY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/meow/-/meow-12.1.1.tgz}
    engines: {node: '>=16.10'}

  meow@13.2.0:
    resolution: {integrity: sha1-a31j+RP5hAY7PMJhtuiADEzTR08=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/meow/-/meow-13.2.0.tgz}
    engines: {node: '>=18'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/merge-stream/-/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/merge2/-/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/micromatch/-/micromatch-4.0.8.tgz}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mime-db/-/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mime-types/-/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mimic-fn/-/mimic-fn-4.0.0.tgz}
    engines: {node: '>=12'}

  mimic-function@5.0.1:
    resolution: {integrity: sha1-rL4rM0n5m53qyn+3Dki4PpTmcHY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mimic-function/-/mimic-function-5.0.1.tgz}
    engines: {node: '>=18'}

  minimatch@10.0.1:
    resolution: {integrity: sha1-zgUhhWtFPIbiXyxMDQPm/33cRAs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/minimatch/-/minimatch-10.0.1.tgz}
    engines: {node: 20 || >=22}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/minimatch/-/minimatch-3.1.2.tgz}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/minimatch/-/minimatch-9.0.5.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/minimist/-/minimist-1.2.8.tgz}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/minipass/-/minipass-7.1.2.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  mitt@3.0.1:
    resolution: {integrity: sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mitt/-/mitt-3.0.1.tgz}

  mlly@1.7.4:
    resolution: {integrity: sha1-PXKV6iNY7HonHqpdAAoPhP6+EA8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/mlly/-/mlly-1.7.4.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ms/-/ms-2.1.3.tgz}

  muggle-string@0.4.1:
    resolution: {integrity: sha1-OzZr1Dsy+AncIGWVNN0w58ig0yg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/muggle-string/-/muggle-string-0.4.1.tgz}

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/nanoid/-/nanoid-3.3.11.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/natural-compare/-/natural-compare-1.4.0.tgz}

  node-addon-api@7.1.1:
    resolution: {integrity: sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/node-addon-api/-/node-addon-api-7.1.1.tgz}

  node-fetch-native@1.6.6:
    resolution: {integrity: sha1-rh0OU3rzXCwLDegcv/N+7dQQqjc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/node-fetch-native/-/node-fetch-native-1.6.6.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/node-releases/-/node-releases-2.0.19.tgz}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/normalize-path/-/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  normalize-wheel-es@1.2.0:
    resolution: {integrity: sha1-D6JZPWGfckWlQWUmGRBasHas8J4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz}

  npm-run-path@5.3.0:
    resolution: {integrity: sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/npm-run-path/-/npm-run-path-5.3.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nprogress@0.2.0:
    resolution: {integrity: sha1-y480xTIT2JVyP8urkH6UIq28r7E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/nprogress/-/nprogress-0.2.0.tgz}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/nth-check/-/nth-check-2.1.1.tgz}

  nypm@0.6.0:
    resolution: {integrity: sha1-OgRiPRw1ipP8Szy5z7ahGvCA/so=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/nypm/-/nypm-0.6.0.tgz}
    engines: {node: ^14.16.0 || >=16.10.0}
    hasBin: true

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/object-inspect/-/object-inspect-1.13.4.tgz}
    engines: {node: '>= 0.4'}

  ohash@2.0.11:
    resolution: {integrity: sha1-YLEejP9iyp3uiNE3R6W6oUX1kAs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ohash/-/ohash-2.0.11.tgz}

  onetime@6.0.0:
    resolution: {integrity: sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/onetime/-/onetime-6.0.0.tgz}
    engines: {node: '>=12'}

  onetime@7.0.0:
    resolution: {integrity: sha1-nxbJLYye9RIOOs2d2ZV8zuzBq2A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/onetime/-/onetime-7.0.0.tgz}
    engines: {node: '>=18'}

  open@8.4.2:
    resolution: {integrity: sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/open/-/open-8.4.2.tgz}
    engines: {node: '>=12'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/optionator/-/optionator-0.9.4.tgz}
    engines: {node: '>= 0.8.0'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/p-limit/-/p-limit-3.1.0.tgz}
    engines: {node: '>=10'}

  p-limit@4.0.0:
    resolution: {integrity: sha1-kUr2VE7TK/pUZwsGHK/L0EmEtkQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/p-limit/-/p-limit-4.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/p-locate/-/p-locate-5.0.0.tgz}
    engines: {node: '>=10'}

  p-locate@6.0.0:
    resolution: {integrity: sha1-PamknUk0uQEIncozAvpl3FoFwE8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/p-locate/-/p-locate-6.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz}

  package-manager-detector@0.2.11:
    resolution: {integrity: sha1-OvCzT5nYbSSvCgYgYD0uEYDQXJw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/package-manager-detector/-/package-manager-detector-0.2.11.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/parent-module/-/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/parse-json/-/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}

  path-browserify@1.0.1:
    resolution: {integrity: sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-browserify/-/path-browserify-1.0.1.tgz}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-exists/-/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha1-pqrZSJIAsh+rMeSc8JJ35RFvuec=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-exists/-/path-exists-5.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-key/-/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-key/-/path-key-4.0.0.tgz}
    engines: {node: '>=12'}

  path-scurry@2.0.0:
    resolution: {integrity: sha1-nwUiifI62L+Tl6KgQl57hhXFhYA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-scurry/-/path-scurry-2.0.0.tgz}
    engines: {node: 20 || >=22}

  path-to-regexp@8.2.0:
    resolution: {integrity: sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-to-regexp/-/path-to-regexp-8.2.0.tgz}
    engines: {node: '>=16'}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/path-type/-/path-type-4.0.0.tgz}
    engines: {node: '>=8'}

  pathe@1.1.2:
    resolution: {integrity: sha1-bEy0epRWkuSKHd1uQJTRcFFkN+w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pathe/-/pathe-1.1.2.tgz}

  pathe@2.0.3:
    resolution: {integrity: sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pathe/-/pathe-2.0.3.tgz}

  perfect-debounce@1.0.0:
    resolution: {integrity: sha1-nC6LwwsWnMmEpYt9WygEmDlZHSo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/perfect-debounce/-/perfect-debounce-1.0.0.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/picocolors/-/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/picomatch/-/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/picomatch/-/picomatch-4.0.2.tgz}
    engines: {node: '>=12'}

  pidtree@0.6.0:
    resolution: {integrity: sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pidtree/-/pidtree-0.6.0.tgz}
    engines: {node: '>=0.10'}
    hasBin: true

  pinia@3.0.2:
    resolution: {integrity: sha1-BhbC4bOZFfJTx2Jts8gbfNrWldo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pinia/-/pinia-3.0.2.tgz}
    peerDependencies:
      typescript: '>=4.4.4'
      vue: ^2.7.0 || ^3.5.11
    peerDependenciesMeta:
      typescript:
        optional: true

  pinyin-pro@3.26.0:
    resolution: {integrity: sha1-njuKn4SCY7gVUtVukxn1IPdwmtY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pinyin-pro/-/pinyin-pro-3.26.0.tgz}

  pkg-types@1.3.1:
    resolution: {integrity: sha1-vXzHCIEZJ3fu9TJsGd60bokJF98=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pkg-types/-/pkg-types-1.3.1.tgz}

  pkg-types@2.1.0:
    resolution: {integrity: sha1-cMnhucdLY/3edJh27gqgB+qe3q0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/pkg-types/-/pkg-types-2.1.0.tgz}

  popmotion@11.0.5:
    resolution: {integrity: sha1-jj4BRCGg/6MOzXIlZP0lWJVOH30=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/popmotion/-/popmotion-11.0.5.tgz}

  portfinder@1.0.37:
    resolution: {integrity: sha1-krdU74mhGAHI7+Sw5c2EWwBkwhI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/portfinder/-/portfinder-1.0.37.tgz}
    engines: {node: '>= 10.12'}

  postcss-calc@10.1.1:
    resolution: {integrity: sha1-UrOF8uYoI5aG6246FiB6Q/NgZMo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-calc/-/postcss-calc-10.1.1.tgz}
    engines: {node: ^18.12 || ^20.9 || >=22.0}
    peerDependencies:
      postcss: ^8.4.38

  postcss-colormin@7.0.2:
    resolution: {integrity: sha1-bzxTwTFYFoZp9FrcOSbzXLJA744=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-colormin/-/postcss-colormin-7.0.2.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-convert-values@7.0.4:
    resolution: {integrity: sha1-/BPs7d7WNl88eUtQLbz3fSmNoSw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-convert-values/-/postcss-convert-values-7.0.4.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-discard-comments@7.0.3:
    resolution: {integrity: sha1-nEFOjumdNRStBqNGXMwg7B2854A=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-discard-comments/-/postcss-discard-comments-7.0.3.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-discard-duplicates@7.0.1:
    resolution: {integrity: sha1-+H8v5H2PAa+x6YNhwds84eiv0aM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-discard-duplicates/-/postcss-discard-duplicates-7.0.1.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-discard-empty@7.0.0:
    resolution: {integrity: sha1-IYgp0e8KXVFC3WLwqmDgDlmdIDM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-discard-empty/-/postcss-discard-empty-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-discard-overridden@7.0.0:
    resolution: {integrity: sha1-sSPqUePU4dCiVM9x6v8SAZJtMZw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-discard-overridden/-/postcss-discard-overridden-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-html@1.8.0:
    resolution: {integrity: sha1-LqHp1sU/BOqZQTUhnTvYqeOA4Ts=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-html/-/postcss-html-1.8.0.tgz}
    engines: {node: ^12 || >=14}

  postcss-load-config@6.0.1:
    resolution: {integrity: sha1-b9fc2K6JutzxstZESJy6v4OqgJY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-load-config/-/postcss-load-config-6.0.1.tgz}
    engines: {node: '>= 18'}
    peerDependencies:
      jiti: '>=1.21.0'
      postcss: '>=8.0.9'
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      jiti:
        optional: true
      postcss:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  postcss-media-query-parser@0.2.3:
    resolution: {integrity: sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz}

  postcss-merge-longhand@7.0.4:
    resolution: {integrity: sha1-pS0GYrSylCDztkqNWwrFEz2Nt3Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-merge-longhand/-/postcss-merge-longhand-7.0.4.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-merge-rules@7.0.4:
    resolution: {integrity: sha1-ZIzIZNMSHm7HLCpPCN8cyAHmDOg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-merge-rules/-/postcss-merge-rules-7.0.4.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-minify-font-values@7.0.0:
    resolution: {integrity: sha1-0Wp1olSOAAd5Vms1aPyHTuXQqhc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-minify-font-values/-/postcss-minify-font-values-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-minify-gradients@7.0.0:
    resolution: {integrity: sha1-9thEVubUkWSlXQ5FuxsYCcbPCVk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-minify-gradients/-/postcss-minify-gradients-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-minify-params@7.0.2:
    resolution: {integrity: sha1-Jkp24l8gLYtcpSkFacDow6xZnf4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-minify-params/-/postcss-minify-params-7.0.2.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-minify-selectors@7.0.4:
    resolution: {integrity: sha1-K2nJnsSKHCI/zkhAYJ2cUzQKEfU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-minify-selectors/-/postcss-minify-selectors-7.0.4.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-charset@7.0.0:
    resolution: {integrity: sha1-kiRK5zwxv4+IhdXxb/aehXrGwAE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-charset/-/postcss-normalize-charset-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-display-values@7.0.0:
    resolution: {integrity: sha1-AftQ5el++JNTY2Kb6lptOzqsE0I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-display-values/-/postcss-normalize-display-values-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-positions@7.0.0:
    resolution: {integrity: sha1-TuvXydPd5AyXuAR8rTgST8hExGM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-positions/-/postcss-normalize-positions-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-repeat-style@7.0.0:
    resolution: {integrity: sha1-DLeEZV1XFNKb072m3uL7YoqnIns=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-string@7.0.0:
    resolution: {integrity: sha1-oRnT5jqWFFcNhBPVcvufyMamTow=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-string/-/postcss-normalize-string-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-timing-functions@7.0.0:
    resolution: {integrity: sha1-mdDujEsjt/Q1X6+5E4WDO5sHEIs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-unicode@7.0.2:
    resolution: {integrity: sha1-CV+NNuoprf30lAacHeEBESmSpxM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-unicode/-/postcss-normalize-unicode-7.0.2.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-url@7.0.0:
    resolution: {integrity: sha1-yIy3z4lS0/9jHk66kk57BgyoAvY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-url/-/postcss-normalize-url-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-normalize-whitespace@7.0.0:
    resolution: {integrity: sha1-RrAl8L6nITnd7mMBVhmwwhzr2EU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-normalize-whitespace/-/postcss-normalize-whitespace-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-ordered-values@7.0.1:
    resolution: {integrity: sha1-i0tbgHDKd1a9SfB9Xt8nS49nguA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-ordered-values/-/postcss-ordered-values-7.0.1.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-reduce-initial@7.0.2:
    resolution: {integrity: sha1-PcCFNHpZQ+GFR9SwqlvU/1qTssU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-reduce-initial/-/postcss-reduce-initial-7.0.2.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-reduce-transforms@7.0.0:
    resolution: {integrity: sha1-A4YIChTl+q2fjtozN1t5/nxPlnc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-reduce-transforms/-/postcss-reduce-transforms-7.0.0.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-resolve-nested-selector@0.1.6:
    resolution: {integrity: sha1-PYTeyAnzTeAgNyxBsDmVaWaJZoY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.6.tgz}

  postcss-safe-parser@6.0.0:
    resolution: {integrity: sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.3.3

  postcss-safe-parser@7.0.1:
    resolution: {integrity: sha1-NuT35ggRGgypQP2XEs4DRxjEDsA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-safe-parser/-/postcss-safe-parser-7.0.1.tgz}
    engines: {node: '>=18.0'}
    peerDependencies:
      postcss: ^8.4.31

  postcss-scss@4.0.9:
    resolution: {integrity: sha1-oDx3PNTJYjywTOFCpSr87HSAZoU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-scss/-/postcss-scss-4.0.9.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.4.29

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz}
    engines: {node: '>=4'}

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-selector-parser/-/postcss-selector-parser-7.1.0.tgz}
    engines: {node: '>=4'}

  postcss-sorting@8.0.2:
    resolution: {integrity: sha1-Y5M4Xs4nK690vumCD7G1gJjk7so=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-sorting/-/postcss-sorting-8.0.2.tgz}
    peerDependencies:
      postcss: ^8.4.20

  postcss-svgo@7.0.1:
    resolution: {integrity: sha1-K2NXHY6VaDhN8zS6yZF7r/TSP1g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-svgo/-/postcss-svgo-7.0.1.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >= 18}
    peerDependencies:
      postcss: ^8.4.31

  postcss-unique-selectors@7.0.3:
    resolution: {integrity: sha1-SD/BEhWyPVF9XZu+WDPZkVYZyjM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-unique-selectors/-/postcss-unique-selectors-7.0.3.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz}

  postcss@8.5.3:
    resolution: {integrity: sha1-FGO28cf7Fv4lhzbLopot41I36vs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/postcss/-/postcss-8.5.3.tgz}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/prelude-ls/-/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz}
    engines: {node: '>=6.0.0'}

  prettier@3.5.3:
    resolution: {integrity: sha1-T8LODWV+egLmAlSfBTsjnLff4bU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/prettier/-/prettier-3.5.3.tgz}
    engines: {node: '>=14'}
    hasBin: true

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/proxy-from-env/-/proxy-from-env-1.1.0.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/punycode/-/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  qs@6.14.0:
    resolution: {integrity: sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/qs/-/qs-6.14.0.tgz}
    engines: {node: '>=0.6'}

  quansync@0.2.10:
    resolution: {integrity: sha1-MgU88Wb6NlEarpX8SXlhFvLcIOE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/quansync/-/quansync-0.2.10.tgz}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/queue-microtask/-/queue-microtask-1.2.3.tgz}

  rc9@2.1.2:
    resolution: {integrity: sha1-YoL/Y4pQyqCpGjHXavSgucvRCA0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rc9/-/rc9-2.1.2.tgz}

  readdirp@4.1.2:
    resolution: {integrity: sha1-64WAFDX78qfuWPGeCSGwaPxplI0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/readdirp/-/readdirp-4.1.2.tgz}
    engines: {node: '>= 14.18.0'}

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/require-directory/-/require-directory-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/require-from-string/-/require-from-string-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/resolve-from/-/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/resolve-from/-/resolve-from-5.0.0.tgz}
    engines: {node: '>=8'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz}

  responsive-storage@2.2.0:
    resolution: {integrity: sha1-jKql2rm/lvWsGHgsTjYN3ej9OGY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/responsive-storage/-/responsive-storage-2.2.0.tgz}

  restore-cursor@5.1.0:
    resolution: {integrity: sha1-B2bZVpnvrLFBUJk/VbrwlT6h6+c=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/restore-cursor/-/restore-cursor-5.1.0.tgz}
    engines: {node: '>=18'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/reusify/-/reusify-1.1.0.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha1-d492xPtzHZNBTo+SX77PZMzn9so=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rfdc/-/rfdc-1.4.1.tgz}

  rimraf@6.0.1:
    resolution: {integrity: sha1-/7itiETdYDMqsV9SvBBLw+1x6k4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rimraf/-/rimraf-6.0.1.tgz}
    engines: {node: 20 || >=22}
    hasBin: true

  rollup-plugin-external-globals@0.10.0:
    resolution: {integrity: sha1-Zl3zHz+HFXnMOXR3VrdyHY64lLM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rollup-plugin-external-globals/-/rollup-plugin-external-globals-0.10.0.tgz}
    peerDependencies:
      rollup: ^2.25.0 || ^3.3.0 || ^4.1.4

  rollup-plugin-visualizer@5.14.0:
    resolution: {integrity: sha1-voLUP7PGROOW4tUKyKU9NUAi1Xw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rollup-plugin-visualizer/-/rollup-plugin-visualizer-5.14.0.tgz}
    engines: {node: '>=18'}
    hasBin: true
    peerDependencies:
      rolldown: 1.x
      rollup: 2.x || 3.x || 4.x
    peerDependenciesMeta:
      rolldown:
        optional: true
      rollup:
        optional: true

  rollup@4.40.1:
    resolution: {integrity: sha1-A9bFPrtqnCwGCuaGph5yokcrNm8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/rollup/-/rollup-4.40.1.tgz}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/run-parallel/-/run-parallel-1.2.0.tgz}

  sass@1.87.0:
    resolution: {integrity: sha1-jM6zb6Y/tIqNXX8vTBO0nFJLcj4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/sass/-/sass-1.87.0.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true

  scule@1.3.0:
    resolution: {integrity: sha1-bvvSL9C7gBvcxYXIkman0tqo+9M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/scule/-/scule-1.3.0.tgz}

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/semver/-/semver-6.3.1.tgz}
    hasBin: true

  semver@7.7.1:
    resolution: {integrity: sha1-q9UJjYKxjGyB9gdP8mR/0+ciDJ8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/semver/-/semver-7.7.1.tgz}
    engines: {node: '>=10'}
    hasBin: true

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/shebang-command/-/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/shebang-regex/-/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/side-channel-list/-/side-channel-list-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/side-channel-map/-/side-channel-map-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/side-channel/-/side-channel-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/signal-exit/-/signal-exit-4.1.0.tgz}
    engines: {node: '>=14'}

  slash@3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/slash/-/slash-3.0.0.tgz}
    engines: {node: '>=8'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/slice-ansi/-/slice-ansi-4.0.0.tgz}
    engines: {node: '>=10'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/slice-ansi/-/slice-ansi-5.0.0.tgz}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha1-zWtGVeKYqNG96wQlCkMwlLNHuak=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/slice-ansi/-/slice-ansi-7.1.0.tgz}
    engines: {node: '>=18'}

  sortablejs@1.15.6:
    resolution: {integrity: sha1-/5NplJP1uKuNgo+TMie0mI3x05M=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/sortablejs/-/sortablejs-1.15.6.tgz}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/source-map-js/-/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/source-map/-/source-map-0.7.4.tgz}
    engines: {node: '>= 8'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz}
    deprecated: Please use @jridgewell/sourcemap-codec instead

  speakingurl@14.0.1:
    resolution: {integrity: sha1-837I3cSrmOlgDByewySoxI13KlM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/speakingurl/-/speakingurl-14.0.1.tgz}
    engines: {node: '>=0.10.0'}

  split2@4.2.0:
    resolution: {integrity: sha1-ycWSCQTRSLqwufZxRfJFqGqtv6Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/split2/-/split2-4.2.0.tgz}
    engines: {node: '>= 10.x'}

  std-env@3.9.0:
    resolution: {integrity: sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/std-env/-/std-env-3.9.0.tgz}

  string-argv@0.3.2:
    resolution: {integrity: sha1-K20O8ktlYnTZV9VOCku/YVPcArY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/string-argv/-/string-argv-0.3.2.tgz}
    engines: {node: '>=0.6.19'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/string-width/-/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/string-width/-/string-width-5.1.2.tgz}
    engines: {node: '>=12'}

  string-width@7.2.0:
    resolution: {integrity: sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/string-width/-/string-width-7.2.0.tgz}
    engines: {node: '>=18'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/strip-ansi/-/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/strip-ansi/-/strip-ansi-7.1.0.tgz}
    engines: {node: '>=12'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/strip-final-newline/-/strip-final-newline-3.0.0.tgz}
    engines: {node: '>=12'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}

  strip-literal@3.0.0:
    resolution: {integrity: sha1-zpxFKpGgryh27Rrk5YNTmjU98/w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/strip-literal/-/strip-literal-3.0.0.tgz}

  style-value-types@5.1.2:
    resolution: {integrity: sha1-a+ZrI3vVRgSKdkiDUoBy7ZVxO2I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/style-value-types/-/style-value-types-5.1.2.tgz}

  stylehacks@7.0.4:
    resolution: {integrity: sha1-nCH3N09LzMAIJBK4WbPInXfTJ3w=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylehacks/-/stylehacks-7.0.4.tgz}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31

  stylelint-config-html@1.1.0:
    resolution: {integrity: sha1-mZ2xmupxO3/23ekq2nbkwb2BK2Y=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-html/-/stylelint-config-html-1.1.0.tgz}
    engines: {node: ^12 || >=14}
    peerDependencies:
      postcss-html: ^1.0.0
      stylelint: '>=14.0.0'

  stylelint-config-recess-order@6.0.0:
    resolution: {integrity: sha1-WVQzOw+pkqQhjGY2xmGGmDoQUdk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-recess-order/-/stylelint-config-recess-order-6.0.0.tgz}
    peerDependencies:
      stylelint: '>=16'

  stylelint-config-recommended-scss@14.1.0:
    resolution: {integrity: sha1-GlhVZVzdy193wQ84x2VnrfK7mqM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-recommended-scss/-/stylelint-config-recommended-scss-14.1.0.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      postcss: ^8.3.3
      stylelint: ^16.6.1
    peerDependenciesMeta:
      postcss:
        optional: true

  stylelint-config-recommended-vue@1.6.0:
    resolution: {integrity: sha1-5dNNnxFBeSKiTb0hQ4xsBYhD+iA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-recommended-vue/-/stylelint-config-recommended-vue-1.6.0.tgz}
    engines: {node: ^12 || >=14}
    peerDependencies:
      postcss-html: ^1.0.0
      stylelint: '>=14.0.0'

  stylelint-config-recommended@14.0.1:
    resolution: {integrity: sha1-0l6GQJqvee5sYIXCwUszx+I8kMY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-recommended/-/stylelint-config-recommended-14.0.1.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      stylelint: ^16.1.0

  stylelint-config-recommended@16.0.0:
    resolution: {integrity: sha1-AiHxmQKBb+fVPZoB6wvkzHtP6Ao=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-recommended/-/stylelint-config-recommended-16.0.0.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      stylelint: ^16.16.0

  stylelint-config-standard-scss@14.0.0:
    resolution: {integrity: sha1-0qPd4O6uNgHM3XNKY6DjvhJDCn4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-standard-scss/-/stylelint-config-standard-scss-14.0.0.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      postcss: ^8.3.3
      stylelint: ^16.11.0
    peerDependenciesMeta:
      postcss:
        optional: true

  stylelint-config-standard@36.0.1:
    resolution: {integrity: sha1-cny7Kh7z4hD1zoMpzeUxEp8VZgk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-config-standard/-/stylelint-config-standard-36.0.1.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      stylelint: ^16.1.0

  stylelint-order@6.0.4:
    resolution: {integrity: sha1-PoDYdsYamNJkDeGBQzaG8kKEdIs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-order/-/stylelint-order-6.0.4.tgz}
    peerDependencies:
      stylelint: ^14.0.0 || ^15.0.0 || ^16.0.1

  stylelint-prettier@5.0.3:
    resolution: {integrity: sha1-RwE07F7YZQERm+mly0Rwm0Z9fJE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-prettier/-/stylelint-prettier-5.0.3.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      prettier: '>=3.0.0'
      stylelint: '>=16.0.0'

  stylelint-scss@6.11.1:
    resolution: {integrity: sha1-A4YKqyUBEoJbLet3yn/x4ro6VBQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint-scss/-/stylelint-scss-6.11.1.tgz}
    engines: {node: '>=18.12.0'}
    peerDependencies:
      stylelint: ^16.0.2

  stylelint@16.19.1:
    resolution: {integrity: sha1-SGuV+nUYowd+4oArxt2iF0vAl7s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/stylelint/-/stylelint-16.19.1.tgz}
    engines: {node: '>=18.12.0'}
    hasBin: true

  superjson@2.2.2:
    resolution: {integrity: sha1-nVK/C/a1dRo8NHLxKS5xR4K6MXM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/superjson/-/superjson-2.2.2.tgz}
    engines: {node: '>=16'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/supports-color/-/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-hyperlinks@3.2.0:
    resolution: {integrity: sha1-uOSFsXloHepJah56vfiYW9MUVGE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/supports-hyperlinks/-/supports-hyperlinks-3.2.0.tgz}
    engines: {node: '>=14.18'}

  svg-tags@1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/svg-tags/-/svg-tags-1.0.0.tgz}

  svgo@3.3.2:
    resolution: {integrity: sha1-rVgAJlLf+7WYb8lxav5S2Gnsvag=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/svgo/-/svgo-3.3.2.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true

  synckit@0.11.4:
    resolution: {integrity: sha1-SJcjJrWXI/wVuNFZgDz4MCtUXVk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/synckit/-/synckit-0.11.4.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}

  table@6.9.0:
    resolution: {integrity: sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/table/-/table-6.9.0.tgz}
    engines: {node: '>=10.0.0'}

  tailwindcss@4.1.4:
    resolution: {integrity: sha1-J7PJEMbxpH9FQEUfP6983W2Xemk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tailwindcss/-/tailwindcss-4.1.4.tgz}

  tapable@2.2.1:
    resolution: {integrity: sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tapable/-/tapable-2.2.1.tgz}
    engines: {node: '>=6'}

  text-extensions@2.4.0:
    resolution: {integrity: sha1-oc/MUM802kG/0EfMdE+ATRaA6jQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/text-extensions/-/text-extensions-2.4.0.tgz}
    engines: {node: '>=8'}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/through/-/through-2.3.8.tgz}

  tinycolor2@1.6.0:
    resolution: {integrity: sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tinycolor2/-/tinycolor2-1.6.0.tgz}

  tinyexec@0.3.2:
    resolution: {integrity: sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tinyexec/-/tinyexec-0.3.2.tgz}

  tinyglobby@0.2.13:
    resolution: {integrity: sha1-oORlFc5svNZTMVN+V0hK9aey/34=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tinyglobby/-/tinyglobby-0.2.13.tgz}
    engines: {node: '>=12.0.0'}

  tinygradient@1.1.5:
    resolution: {integrity: sha1-D7hVzrGNlrIbp4C1GoASAzslMO8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tinygradient/-/tinygradient-1.1.5.tgz}

  tippy.js@6.3.7:
    resolution: {integrity: sha1-jM+2UdZCAQ7Zoy/ymw6eGcW4xhw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tippy.js/-/tippy.js-6.3.7.tgz}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/to-regex-range/-/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ts-api-utils/-/ts-api-utils-2.1.0.tgz}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  tslib@2.3.0:
    resolution: {integrity: sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tslib/-/tslib-2.3.0.tgz}

  tslib@2.4.0:
    resolution: {integrity: sha1-fOyqfwc85oCgWEeqd76UEJjzbcM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tslib/-/tslib-2.4.0.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/tslib/-/tslib-2.8.1.tgz}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/type-check/-/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}

  type-fest@4.40.1:
    resolution: {integrity: sha1-14oJ8I3RCBpDTdN3lnZQz9VlQB0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/type-fest/-/type-fest-4.40.1.tgz}
    engines: {node: '>=16'}

  typescript-eslint@8.31.1:
    resolution: {integrity: sha1-t3qx5IztLaq5Il/5S6tUORpK9ps=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/typescript-eslint/-/typescript-eslint-8.31.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@5.8.3:
    resolution: {integrity: sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/typescript/-/typescript-5.8.3.tgz}
    engines: {node: '>=14.17'}
    hasBin: true

  ufo@1.6.1:
    resolution: {integrity: sha1-rC2x1UYU0bIsHWA+Ou9EqF2PFGs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/ufo/-/ufo-1.6.1.tgz}

  unctx@2.4.1:
    resolution: {integrity: sha1-kzRqmNSjjGTMWGH2CY9M58b4Fko=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unctx/-/unctx-2.4.1.tgz}

  undici-types@6.19.8:
    resolution: {integrity: sha1-NREcnRQ3q4OnzcCrri8m2I7aCgI=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/undici-types/-/undici-types-6.19.8.tgz}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha1-G7mlHII6r51zqL/NPRoj3elLDOQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unicorn-magic/-/unicorn-magic-0.1.0.tgz}
    engines: {node: '>=18'}

  unimport@5.0.0:
    resolution: {integrity: sha1-wuWjZMA/yQ3hktiIE4Sjec+LTZk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unimport/-/unimport-5.0.0.tgz}
    engines: {node: '>=18.12.0'}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/universalify/-/universalify-2.0.1.tgz}
    engines: {node: '>= 10.0.0'}

  unplugin-icons@22.1.0:
    resolution: {integrity: sha1-Wm/j11HlDxyTfiiYV7BBjmhV2So=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unplugin-icons/-/unplugin-icons-22.1.0.tgz}
    peerDependencies:
      '@svgr/core': '>=7.0.0'
      '@svgx/core': ^1.0.1
      '@vue/compiler-sfc': ^3.0.2 || ^2.7.0
      svelte: ^3.0.0 || ^4.0.0 || ^5.0.0
      vue-template-compiler: ^2.6.12
      vue-template-es2015-compiler: ^1.9.0
    peerDependenciesMeta:
      '@svgr/core':
        optional: true
      '@svgx/core':
        optional: true
      '@vue/compiler-sfc':
        optional: true
      svelte:
        optional: true
      vue-template-compiler:
        optional: true
      vue-template-es2015-compiler:
        optional: true

  unplugin-utils@0.2.4:
    resolution: {integrity: sha1-VuQCmmkGZFoQZE+L78QEsG1dJNA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unplugin-utils/-/unplugin-utils-0.2.4.tgz}
    engines: {node: '>=18.12.0'}

  unplugin@2.3.2:
    resolution: {integrity: sha1-Nsk6FmK3DJei4vxFwOePoJ96SYQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/unplugin/-/unplugin-2.3.2.tgz}
    engines: {node: '>=18.12.0'}

  untyped@2.0.0:
    resolution: {integrity: sha1-hrwgWk7EsBNygihYZrgnhVeu7pc=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/untyped/-/untyped-2.0.0.tgz}
    hasBin: true

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/uri-js/-/uri-js-4.4.1.tgz}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/util-deprecate/-/util-deprecate-1.0.2.tgz}

  vite-code-inspector-plugin@0.20.10:
    resolution: {integrity: sha1-9kp1sAzxveoZpmGJvA8yy/4xGWM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-code-inspector-plugin/-/vite-code-inspector-plugin-0.20.10.tgz}

  vite-plugin-cdn-import@1.0.1:
    resolution: {integrity: sha1-b+1+YQnxbvzmCXMTT15l7AkBmyA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-cdn-import/-/vite-plugin-cdn-import-1.0.1.tgz}

  vite-plugin-compression@0.5.1:
    resolution: {integrity: sha1-p1sNj0g1frs3e2UBbanyCIXvObY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-compression/-/vite-plugin-compression-0.5.1.tgz}
    peerDependencies:
      vite: '>=2.0.0'

  vite-plugin-externals@0.6.2:
    resolution: {integrity: sha1-siAViP9BVmlvdCK/ZJ7GMLtusQs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-externals/-/vite-plugin-externals-0.6.2.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: '>=2.0.0'

  vite-plugin-fake-server@2.2.0:
    resolution: {integrity: sha1-WbZQ94SBhiJatnbIJIgmR+lPgk4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-fake-server/-/vite-plugin-fake-server-2.2.0.tgz}

  vite-plugin-remove-console@2.2.0:
    resolution: {integrity: sha1-pzxrobIXtWrMxRNcUItYxN9FQx4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-remove-console/-/vite-plugin-remove-console-2.2.0.tgz}

  vite-plugin-router-warn@1.0.0:
    resolution: {integrity: sha1-/BR+sWRbA2GoJp5KHQ2dxM5xW5s=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-plugin-router-warn/-/vite-plugin-router-warn-1.0.0.tgz}

  vite-svg-loader@5.1.0:
    resolution: {integrity: sha1-sLib2AJLwPcH0OjXQiRGrAFXbZQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite-svg-loader/-/vite-svg-loader-5.1.0.tgz}
    peerDependencies:
      vue: '>=3.2.13'

  vite@6.3.4:
    resolution: {integrity: sha1-1EGnLHzZqTtxm7hRJQpObBGcnP8=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vite/-/vite-6.3.4.tgz}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vscode-uri@3.1.0:
    resolution: {integrity: sha1-3QnsWmaji1w//8d0AVcTSW0U4Jw=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vscode-uri/-/vscode-uri-3.1.0.tgz}

  vue-demi@0.14.10:
    resolution: {integrity: sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-demi/-/vue-demi-0.14.10.tgz}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-eslint-parser@10.1.3:
    resolution: {integrity: sha1-lkV4I6WRWmIAF5jP2cwVqJBnv4E=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-eslint-parser/-/vue-eslint-parser-10.1.3.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0

  vue-router@4.5.1:
    resolution: {integrity: sha1-R7/+LTpUedKIapokRUeoU6oKv2k=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-router/-/vue-router-4.5.1.tgz}
    peerDependencies:
      vue: ^3.2.0

  vue-tippy@6.7.0:
    resolution: {integrity: sha1-LLwk6T+Sziz8OPGC2HUqgIQ7JMg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-tippy/-/vue-tippy-6.7.0.tgz}
    peerDependencies:
      vue: ^3.2.0

  vue-tsc@2.2.10:
    resolution: {integrity: sha1-e1GmZsuQeIiE79DK7cafwfycW3g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-tsc/-/vue-tsc-2.2.10.tgz}
    hasBin: true
    peerDependencies:
      typescript: '>=5.0.0'

  vue-types@6.0.0:
    resolution: {integrity: sha1-OpvlYkSR1ZNmUvPR8kaa4/U8apQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue-types/-/vue-types-6.0.0.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      vue: ^3.0.0
    peerDependenciesMeta:
      vue:
        optional: true

  vue@3.5.13:
    resolution: {integrity: sha1-n3YKGpgrCcDASoZ5A/wznJ8p7Ao=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/vue/-/vue-3.5.13.tgz}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  webpack-code-inspector-plugin@0.20.10:
    resolution: {integrity: sha1-GfD7rzPtb/BQP6wwzy2oWY4d7ck=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/webpack-code-inspector-plugin/-/webpack-code-inspector-plugin-0.20.10.tgz}

  webpack-virtual-modules@0.6.2:
    resolution: {integrity: sha1-BX+qkGXIrPSPJMtXrA53c5q5p+g=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz}

  which@1.3.1:
    resolution: {integrity: sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/which/-/which-1.3.1.tgz}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/which/-/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  widest-line@5.0.0:
    resolution: {integrity: sha1-t0gmoeSAeDNF8M2QYbSXU8nacNA=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/widest-line/-/widest-line-5.0.0.tgz}
    engines: {node: '>=18'}

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/word-wrap/-/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/wrap-ansi/-/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/wrap-ansi/-/wrap-ansi-8.1.0.tgz}
    engines: {node: '>=12'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha1-Gj3Itw2F7rg5jd+x5KAs0Ybliz4=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/wrap-ansi/-/wrap-ansi-9.0.0.tgz}
    engines: {node: '>=18'}

  write-file-atomic@5.0.1:
    resolution: {integrity: sha1-aN9HF8Vcb6QoGnhgtMK6Cm0rEec=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/write-file-atomic/-/write-file-atomic-5.0.1.tgz}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  xml-name-validator@4.0.0:
    resolution: {integrity: sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/xml-name-validator/-/xml-name-validator-4.0.0.tgz}
    engines: {node: '>=12'}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/y18n/-/y18n-5.0.8.tgz}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yallist/-/yallist-3.1.1.tgz}

  yaml@2.7.1:
    resolution: {integrity: sha1-RKJH0biFI4VWeax/p82m7X4TXPY=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yaml/-/yaml-2.7.1.tgz}
    engines: {node: '>= 14'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yargs-parser/-/yargs-parser-21.1.1.tgz}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yargs/-/yargs-17.7.2.tgz}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yocto-queue/-/yocto-queue-0.1.0.tgz}
    engines: {node: '>=10'}

  yocto-queue@1.2.1:
    resolution: {integrity: sha1-NtfEc593Wzy8KOYTbiGqBXrexBg=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/yocto-queue/-/yocto-queue-1.2.1.tgz}
    engines: {node: '>=12.20'}

  zrender@5.6.1:
    resolution: {integrity: sha1-4I1X7PSsrHCMT8t0gesgHffxCms=, tarball: https://artnj.zte.com.cn:443/artifactory/api/npm/public-npm-remote/zrender/-/zrender-5.6.1.tgz}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@antfu/install-pkg@1.0.0':
    dependencies:
      package-manager-detector: 0.2.11
      tinyexec: 0.3.2

  '@antfu/utils@8.1.1': {}

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.10':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helpers': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.0':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-compilation-targets@7.27.0':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.27.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helpers@7.27.0':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0

  '@babel/parser@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typescript@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-syntax-typescript': 7.25.9(@babel/core@7.26.10)
    transitivePeerDependencies:
      - supports-color

  '@babel/template@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@babel/traverse@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.0':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@commitlint/cli@19.8.0(@types/node@20.17.32)(typescript@5.8.3)':
    dependencies:
      '@commitlint/format': 19.8.0
      '@commitlint/lint': 19.8.0
      '@commitlint/load': 19.8.0(@types/node@20.17.32)(typescript@5.8.3)
      '@commitlint/read': 19.8.0
      '@commitlint/types': 19.8.0
      tinyexec: 0.3.2
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/config-conventional@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      conventional-changelog-conventionalcommits: 7.0.2

  '@commitlint/config-validator@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      ajv: 8.17.1

  '@commitlint/ensure@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  '@commitlint/execute-rule@19.8.0': {}

  '@commitlint/format@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      chalk: 5.4.1

  '@commitlint/is-ignored@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      semver: 7.7.1

  '@commitlint/lint@19.8.0':
    dependencies:
      '@commitlint/is-ignored': 19.8.0
      '@commitlint/parse': 19.8.0
      '@commitlint/rules': 19.8.0
      '@commitlint/types': 19.8.0

  '@commitlint/load@19.8.0(@types/node@20.17.32)(typescript@5.8.3)':
    dependencies:
      '@commitlint/config-validator': 19.8.0
      '@commitlint/execute-rule': 19.8.0
      '@commitlint/resolve-extends': 19.8.0
      '@commitlint/types': 19.8.0
      chalk: 5.4.1
      cosmiconfig: 9.0.0(typescript@5.8.3)
      cosmiconfig-typescript-loader: 6.1.0(@types/node@20.17.32)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/message@19.8.0': {}

  '@commitlint/parse@19.8.0':
    dependencies:
      '@commitlint/types': 19.8.0
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0

  '@commitlint/read@19.8.0':
    dependencies:
      '@commitlint/top-level': 19.8.0
      '@commitlint/types': 19.8.0
      git-raw-commits: 4.0.0
      minimist: 1.2.8
      tinyexec: 0.3.2

  '@commitlint/resolve-extends@19.8.0':
    dependencies:
      '@commitlint/config-validator': 19.8.0
      '@commitlint/types': 19.8.0
      global-directory: 4.0.1
      import-meta-resolve: 4.1.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0

  '@commitlint/rules@19.8.0':
    dependencies:
      '@commitlint/ensure': 19.8.0
      '@commitlint/message': 19.8.0
      '@commitlint/to-lines': 19.8.0
      '@commitlint/types': 19.8.0

  '@commitlint/to-lines@19.8.0': {}

  '@commitlint/top-level@19.8.0':
    dependencies:
      find-up: 7.0.0

  '@commitlint/types@19.8.0':
    dependencies:
      '@types/conventional-commits-parser': 5.0.1
      chalk: 5.4.1

  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-tokenizer@3.0.3': {}

  '@csstools/media-query-list-parser@4.0.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    dependencies:
      postcss-selector-parser: 7.1.0

  '@ctrl/tinycolor@3.6.1': {}

  '@dual-bundle/import-meta-resolve@4.1.0': {}

  '@element-plus/icons-vue@2.3.1(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      vue: 3.5.13(typescript@5.8.3)

  '@esbuild/aix-ppc64@0.24.2':
    optional: true

  '@esbuild/aix-ppc64@0.25.3':
    optional: true

  '@esbuild/android-arm64@0.24.2':
    optional: true

  '@esbuild/android-arm64@0.25.3':
    optional: true

  '@esbuild/android-arm@0.24.2':
    optional: true

  '@esbuild/android-arm@0.25.3':
    optional: true

  '@esbuild/android-x64@0.24.2':
    optional: true

  '@esbuild/android-x64@0.25.3':
    optional: true

  '@esbuild/darwin-arm64@0.24.2':
    optional: true

  '@esbuild/darwin-arm64@0.25.3':
    optional: true

  '@esbuild/darwin-x64@0.24.2':
    optional: true

  '@esbuild/darwin-x64@0.25.3':
    optional: true

  '@esbuild/freebsd-arm64@0.24.2':
    optional: true

  '@esbuild/freebsd-arm64@0.25.3':
    optional: true

  '@esbuild/freebsd-x64@0.24.2':
    optional: true

  '@esbuild/freebsd-x64@0.25.3':
    optional: true

  '@esbuild/linux-arm64@0.24.2':
    optional: true

  '@esbuild/linux-arm64@0.25.3':
    optional: true

  '@esbuild/linux-arm@0.24.2':
    optional: true

  '@esbuild/linux-arm@0.25.3':
    optional: true

  '@esbuild/linux-ia32@0.24.2':
    optional: true

  '@esbuild/linux-ia32@0.25.3':
    optional: true

  '@esbuild/linux-loong64@0.24.2':
    optional: true

  '@esbuild/linux-loong64@0.25.3':
    optional: true

  '@esbuild/linux-mips64el@0.24.2':
    optional: true

  '@esbuild/linux-mips64el@0.25.3':
    optional: true

  '@esbuild/linux-ppc64@0.24.2':
    optional: true

  '@esbuild/linux-ppc64@0.25.3':
    optional: true

  '@esbuild/linux-riscv64@0.24.2':
    optional: true

  '@esbuild/linux-riscv64@0.25.3':
    optional: true

  '@esbuild/linux-s390x@0.24.2':
    optional: true

  '@esbuild/linux-s390x@0.25.3':
    optional: true

  '@esbuild/linux-x64@0.24.2':
    optional: true

  '@esbuild/linux-x64@0.25.3':
    optional: true

  '@esbuild/netbsd-arm64@0.24.2':
    optional: true

  '@esbuild/netbsd-arm64@0.25.3':
    optional: true

  '@esbuild/netbsd-x64@0.24.2':
    optional: true

  '@esbuild/netbsd-x64@0.25.3':
    optional: true

  '@esbuild/openbsd-arm64@0.24.2':
    optional: true

  '@esbuild/openbsd-arm64@0.25.3':
    optional: true

  '@esbuild/openbsd-x64@0.24.2':
    optional: true

  '@esbuild/openbsd-x64@0.25.3':
    optional: true

  '@esbuild/sunos-x64@0.24.2':
    optional: true

  '@esbuild/sunos-x64@0.25.3':
    optional: true

  '@esbuild/win32-arm64@0.24.2':
    optional: true

  '@esbuild/win32-arm64@0.25.3':
    optional: true

  '@esbuild/win32-ia32@0.24.2':
    optional: true

  '@esbuild/win32-ia32@0.25.3':
    optional: true

  '@esbuild/win32-x64@0.24.2':
    optional: true

  '@esbuild/win32-x64@0.25.3':
    optional: true

  '@eslint-community/eslint-utils@4.6.1(eslint@9.25.1(jiti@2.4.2))':
    dependencies:
      eslint: 9.25.1(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.1': {}

  '@eslint/core@0.13.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.25.1': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.2.8':
    dependencies:
      '@eslint/core': 0.13.0
      levn: 0.4.1

  '@faker-js/faker@9.7.0': {}

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.2': {}

  '@iconify/json@2.2.333':
    dependencies:
      '@iconify/types': 2.0.0
      pathe: 1.1.2

  '@iconify/types@2.0.0': {}

  '@iconify/utils@2.3.0':
    dependencies:
      '@antfu/install-pkg': 1.0.0
      '@antfu/utils': 8.1.1
      '@iconify/types': 2.0.0
      debug: 4.4.0
      globals: 15.15.0
      kolorist: 1.8.0
      local-pkg: 1.1.1
      mlly: 1.7.4
    transitivePeerDependencies:
      - supports-color

  '@iconify/vue@4.2.0(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@iconify/types': 2.0.0
      vue: 3.5.13(typescript@5.8.3)

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@keyv/serialize@1.0.3':
    dependencies:
      buffer: 6.0.3

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@nuxt/kit@3.17.1':
    dependencies:
      c12: 3.0.3
      consola: 3.4.2
      defu: 6.1.4
      destr: 2.0.5
      errx: 0.1.0
      exsolve: 1.0.5
      ignore: 7.0.4
      jiti: 2.4.2
      klona: 2.0.6
      knitwork: 1.2.0
      mlly: 1.7.4
      ohash: 2.0.11
      pathe: 2.0.3
      pkg-types: 2.1.0
      scule: 1.3.0
      semver: 7.7.1
      std-env: 3.9.0
      tinyglobby: 0.2.13
      ufo: 1.6.1
      unctx: 2.4.1
      unimport: 5.0.0
      untyped: 2.0.0
    transitivePeerDependencies:
      - magicast
    optional: true

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@pkgr/core@0.2.4': {}

  '@popperjs/core@2.11.8': {}

  '@pureadmin/descriptions@1.2.1(echarts@5.6.0)(element-plus@2.9.9(vue@3.5.13(typescript@5.8.3)))(typescript@5.8.3)':
    dependencies:
      '@element-plus/icons-vue': 2.3.1(vue@3.5.13(typescript@5.8.3))
      '@pureadmin/utils': 2.6.0(echarts@5.6.0)(vue@3.5.13(typescript@5.8.3))
      element-plus: 2.9.9(vue@3.5.13(typescript@5.8.3))
      vue: 3.5.13(typescript@5.8.3)
    transitivePeerDependencies:
      - echarts
      - typescript

  '@pureadmin/table@3.2.1(element-plus@2.9.9(vue@3.5.13(typescript@5.8.3)))(typescript@5.8.3)':
    dependencies:
      element-plus: 2.9.9(vue@3.5.13(typescript@5.8.3))
      vue: 3.5.13(typescript@5.8.3)
    transitivePeerDependencies:
      - typescript

  '@pureadmin/utils@2.6.0(echarts@5.6.0)(vue@3.5.13(typescript@5.8.3))':
    optionalDependencies:
      echarts: 5.6.0
      vue: 3.5.13(typescript@5.8.3)

  '@rollup/pluginutils@5.1.4(rollup@4.40.1)':
    dependencies:
      '@types/estree': 1.0.7
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.40.1

  '@rollup/rollup-android-arm-eabi@4.40.1':
    optional: true

  '@rollup/rollup-android-arm64@4.40.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.40.1':
    optional: true

  '@rollup/rollup-darwin-x64@4.40.1':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.40.1':
    optional: true

  '@rollup/rollup-freebsd-x64@4.40.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.40.1':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.40.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.40.1':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.40.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.40.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.40.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.40.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.40.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.40.1':
    optional: true

  '@sxzz/popperjs-es@2.11.7': {}

  '@tailwindcss/node@4.1.4':
    dependencies:
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.29.2
      tailwindcss: 4.1.4

  '@tailwindcss/oxide-android-arm64@4.1.4':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.4':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.4':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.4':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.4':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.4':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.4':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.4':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.4':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.4':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.4':
    optional: true

  '@tailwindcss/oxide@4.1.4':
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.4
      '@tailwindcss/oxide-darwin-arm64': 4.1.4
      '@tailwindcss/oxide-darwin-x64': 4.1.4
      '@tailwindcss/oxide-freebsd-x64': 4.1.4
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.4
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.4
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.4
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.4
      '@tailwindcss/oxide-linux-x64-musl': 4.1.4
      '@tailwindcss/oxide-wasm32-wasi': 4.1.4
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.4
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.4

  '@tailwindcss/vite@4.1.4(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))':
    dependencies:
      '@tailwindcss/node': 4.1.4
      '@tailwindcss/oxide': 4.1.4
      tailwindcss: 4.1.4
      vite: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)

  '@trysound/sax@0.2.0': {}

  '@types/conventional-commits-parser@5.0.1':
    dependencies:
      '@types/node': 20.17.32

  '@types/estree@1.0.7': {}

  '@types/js-cookie@3.0.6': {}

  '@types/json-schema@7.0.15': {}

  '@types/lodash-es@4.17.12':
    dependencies:
      '@types/lodash': 4.17.16

  '@types/lodash@4.17.16': {}

  '@types/node@20.17.32':
    dependencies:
      undici-types: 6.19.8

  '@types/nprogress@0.2.3': {}

  '@types/path-browserify@1.0.3': {}

  '@types/qs@6.9.18': {}

  '@types/sortablejs@1.15.8': {}

  '@types/tinycolor2@1.4.6': {}

  '@types/web-bluetooth@0.0.16': {}

  '@types/web-bluetooth@0.0.21': {}

  '@typescript-eslint/eslint-plugin@8.31.1(@typescript-eslint/parser@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3))(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.31.1
      '@typescript-eslint/type-utils': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.31.1
      eslint: 9.25.1(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.31.1
      '@typescript-eslint/types': 8.31.1
      '@typescript-eslint/typescript-estree': 8.31.1(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.31.1
      debug: 4.4.0
      eslint: 9.25.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.31.1':
    dependencies:
      '@typescript-eslint/types': 8.31.1
      '@typescript-eslint/visitor-keys': 8.31.1

  '@typescript-eslint/type-utils@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.31.1(typescript@5.8.3)
      '@typescript-eslint/utils': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      debug: 4.4.0
      eslint: 9.25.1(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.31.1': {}

  '@typescript-eslint/typescript-estree@8.31.1(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 8.31.1
      '@typescript-eslint/visitor-keys': 8.31.1
      debug: 4.4.0
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.1
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.6.1(eslint@9.25.1(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.31.1
      '@typescript-eslint/types': 8.31.1
      '@typescript-eslint/typescript-estree': 8.31.1(typescript@5.8.3)
      eslint: 9.25.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.31.1':
    dependencies:
      '@typescript-eslint/types': 8.31.1
      eslint-visitor-keys: 4.2.0

  '@vitejs/plugin-vue-jsx@4.1.2(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/plugin-transform-typescript': 7.27.0(@babel/core@7.26.10)
      '@vue/babel-plugin-jsx': 1.4.0(@babel/core@7.26.10)
      vite: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)
      vue: 3.5.13(typescript@5.8.3)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@5.2.3(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      vite: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)
      vue: 3.5.13(typescript@5.8.3)

  '@volar/language-core@2.4.13':
    dependencies:
      '@volar/source-map': 2.4.13

  '@volar/source-map@2.4.13': {}

  '@volar/typescript@2.4.13':
    dependencies:
      '@volar/language-core': 2.4.13
      path-browserify: 1.0.1
      vscode-uri: 3.1.0

  '@vue/babel-helper-vue-transform-on@1.4.0': {}

  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.10)
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
      '@vue/babel-helper-vue-transform-on': 1.4.0
      '@vue/babel-plugin-resolve-type': 1.4.0(@babel/core@7.26.10)
      '@vue/shared': 3.5.13
    optionalDependencies:
      '@babel/core': 7.26.10
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/parser': 7.27.0
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.27.0
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-sfc@3.5.13':
    dependencies:
      '@babel/parser': 7.27.0
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.17
      postcss: 8.5.3
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.13':
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-vue2@2.7.16':
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  '@vue/devtools-api@6.6.4': {}

  '@vue/devtools-api@7.7.6':
    dependencies:
      '@vue/devtools-kit': 7.7.6

  '@vue/devtools-kit@7.7.6':
    dependencies:
      '@vue/devtools-shared': 7.7.6
      birpc: 2.3.0
      hookable: 5.5.3
      mitt: 3.0.1
      perfect-debounce: 1.0.0
      speakingurl: 14.0.1
      superjson: 2.2.2

  '@vue/devtools-shared@7.7.6':
    dependencies:
      rfdc: 1.4.1

  '@vue/language-core@2.2.10(typescript@5.8.3)':
    dependencies:
      '@volar/language-core': 2.4.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-vue2': 2.7.16
      '@vue/shared': 3.5.13
      alien-signals: 1.0.13
      minimatch: 9.0.5
      muggle-string: 0.4.1
      path-browserify: 1.0.1
    optionalDependencies:
      typescript: 5.8.3

  '@vue/reactivity@3.5.13':
    dependencies:
      '@vue/shared': 3.5.13

  '@vue/runtime-core@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/runtime-dom@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/runtime-core': 3.5.13
      '@vue/shared': 3.5.13
      csstype: 3.1.3

  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      vue: 3.5.13(typescript@5.8.3)

  '@vue/shared@3.5.13': {}

  '@vueuse/core@13.1.0(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@types/web-bluetooth': 0.0.21
      '@vueuse/metadata': 13.1.0
      '@vueuse/shared': 13.1.0(vue@3.5.13(typescript@5.8.3))
      vue: 3.5.13(typescript@5.8.3)

  '@vueuse/core@9.13.0(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@3.5.13(typescript@5.8.3))
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.8.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@13.1.0': {}

  '@vueuse/metadata@9.13.0': {}

  '@vueuse/motion@3.0.3(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      '@vueuse/core': 13.1.0(vue@3.5.13(typescript@5.8.3))
      '@vueuse/shared': 13.1.0(vue@3.5.13(typescript@5.8.3))
      defu: 6.1.4
      framesync: 6.1.2
      popmotion: 11.0.5
      style-value-types: 5.1.2
      vue: 3.5.13(typescript@5.8.3)
    optionalDependencies:
      '@nuxt/kit': 3.17.1
    transitivePeerDependencies:
      - magicast

  '@vueuse/shared@13.1.0(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      vue: 3.5.13(typescript@5.8.3)

  '@vueuse/shared@9.13.0(vue@3.5.13(typescript@5.8.3))':
    dependencies:
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.8.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  alien-signals@1.0.13: {}

  animate.css@4.1.1: {}

  ansi-align@3.0.1:
    dependencies:
      string-width: 4.2.3

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  argparse@2.0.1: {}

  array-ify@1.0.0: {}

  array-union@2.1.0: {}

  astral-regex@2.0.0: {}

  async-validator@4.2.5: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  balanced-match@2.0.0: {}

  base64-js@1.5.1: {}

  birpc@2.3.0: {}

  boolbase@1.0.0: {}

  boxen@8.0.1:
    dependencies:
      ansi-align: 3.0.1
      camelcase: 8.0.0
      chalk: 5.4.1
      cli-boxes: 3.0.0
      string-width: 7.2.0
      type-fest: 4.40.1
      widest-line: 5.0.0
      wrap-ansi: 9.0.0

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001716
      electron-to-chromium: 1.5.145
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  bundle-import@0.0.2:
    dependencies:
      get-tsconfig: 4.10.0
      import-from-string: 0.0.5

  c12@3.0.3:
    dependencies:
      chokidar: 4.0.3
      confbox: 0.2.2
      defu: 6.1.4
      dotenv: 16.5.0
      exsolve: 1.0.5
      giget: 2.0.0
      jiti: 2.4.2
      ohash: 2.0.11
      pathe: 2.0.3
      perfect-debounce: 1.0.0
      pkg-types: 2.1.0
      rc9: 2.1.2
    optional: true

  cacheable@1.8.10:
    dependencies:
      hookified: 1.8.2
      keyv: 5.3.3

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camelcase@8.0.0: {}

  caniuse-api@3.0.0:
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001716
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  caniuse-lite@1.0.30001716: {}

  chalk@4.1.1:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  citty@0.1.6:
    dependencies:
      consola: 3.4.2
    optional: true

  cli-boxes@3.0.0: {}

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  code-inspector-core@0.20.10:
    dependencies:
      '@vue/compiler-dom': 3.5.13
      chalk: 4.1.1
      dotenv: 16.5.0
      launch-ide: 1.0.7
      portfinder: 1.0.37
    transitivePeerDependencies:
      - supports-color

  code-inspector-plugin@0.20.10:
    dependencies:
      chalk: 4.1.1
      code-inspector-core: 0.20.10
      dotenv: 16.5.0
      esbuild-code-inspector-plugin: 0.20.10
      vite-code-inspector-plugin: 0.20.10
      webpack-code-inspector-plugin: 0.20.10
    transitivePeerDependencies:
      - supports-color

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  colord@2.9.3: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@13.1.0: {}

  commander@7.2.0: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  confbox@0.1.8: {}

  confbox@0.2.2: {}

  consola@3.4.2:
    optional: true

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@7.0.2:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@5.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0

  convert-source-map@2.0.0: {}

  copy-anything@3.0.5:
    dependencies:
      is-what: 4.1.16

  cosmiconfig-typescript-loader@6.1.0(@types/node@20.17.32)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    dependencies:
      '@types/node': 20.17.32
      cosmiconfig: 9.0.0(typescript@5.8.3)
      jiti: 2.4.2
      typescript: 5.8.3

  cosmiconfig@9.0.0(typescript@5.8.3):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.8.3

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-declaration-sorter@7.2.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  css-functions-list@3.2.3: {}

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.2.2
      nth-check: 2.1.1

  css-tree@2.2.1:
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.1

  css-tree@2.3.1:
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.1

  css-tree@3.1.0:
    dependencies:
      mdn-data: 2.12.2
      source-map-js: 1.2.1

  css-what@6.1.0: {}

  cssesc@3.0.0: {}

  cssnano-preset-default@7.0.6(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      css-declaration-sorter: 7.2.0(postcss@8.5.3)
      cssnano-utils: 5.0.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-calc: 10.1.1(postcss@8.5.3)
      postcss-colormin: 7.0.2(postcss@8.5.3)
      postcss-convert-values: 7.0.4(postcss@8.5.3)
      postcss-discard-comments: 7.0.3(postcss@8.5.3)
      postcss-discard-duplicates: 7.0.1(postcss@8.5.3)
      postcss-discard-empty: 7.0.0(postcss@8.5.3)
      postcss-discard-overridden: 7.0.0(postcss@8.5.3)
      postcss-merge-longhand: 7.0.4(postcss@8.5.3)
      postcss-merge-rules: 7.0.4(postcss@8.5.3)
      postcss-minify-font-values: 7.0.0(postcss@8.5.3)
      postcss-minify-gradients: 7.0.0(postcss@8.5.3)
      postcss-minify-params: 7.0.2(postcss@8.5.3)
      postcss-minify-selectors: 7.0.4(postcss@8.5.3)
      postcss-normalize-charset: 7.0.0(postcss@8.5.3)
      postcss-normalize-display-values: 7.0.0(postcss@8.5.3)
      postcss-normalize-positions: 7.0.0(postcss@8.5.3)
      postcss-normalize-repeat-style: 7.0.0(postcss@8.5.3)
      postcss-normalize-string: 7.0.0(postcss@8.5.3)
      postcss-normalize-timing-functions: 7.0.0(postcss@8.5.3)
      postcss-normalize-unicode: 7.0.2(postcss@8.5.3)
      postcss-normalize-url: 7.0.0(postcss@8.5.3)
      postcss-normalize-whitespace: 7.0.0(postcss@8.5.3)
      postcss-ordered-values: 7.0.1(postcss@8.5.3)
      postcss-reduce-initial: 7.0.2(postcss@8.5.3)
      postcss-reduce-transforms: 7.0.0(postcss@8.5.3)
      postcss-svgo: 7.0.1(postcss@8.5.3)
      postcss-unique-selectors: 7.0.3(postcss@8.5.3)

  cssnano-utils@5.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  cssnano@7.0.6(postcss@8.5.3):
    dependencies:
      cssnano-preset-default: 7.0.6(postcss@8.5.3)
      lilconfig: 3.1.3
      postcss: 8.5.3

  csso@5.0.5:
    dependencies:
      css-tree: 2.2.1

  csstype@3.1.3: {}

  dargs@8.1.0: {}

  dayjs@1.11.13: {}

  de-indent@1.0.2: {}

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  deep-is@0.1.4: {}

  define-lazy-prop@2.0.0: {}

  defu@6.1.4: {}

  delayed-stream@1.0.0: {}

  destr@2.0.5:
    optional: true

  detect-libc@1.0.3:
    optional: true

  detect-libc@2.0.4: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv@16.5.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  echarts@5.6.0:
    dependencies:
      tslib: 2.3.0
      zrender: 5.6.1

  electron-to-chromium@1.5.145: {}

  element-plus@2.9.9(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      '@ctrl/tinycolor': 3.6.1
      '@element-plus/icons-vue': 2.3.1(vue@3.5.13(typescript@5.8.3))
      '@floating-ui/dom': 1.6.13
      '@popperjs/core': '@sxzz/popperjs-es@2.11.7'
      '@types/lodash': 4.17.16
      '@types/lodash-es': 4.17.12
      '@vueuse/core': 9.13.0(vue@3.5.13(typescript@5.8.3))
      async-validator: 4.2.5
      dayjs: 1.11.13
      escape-html: 1.0.3
      lodash: 4.17.21
      lodash-es: 4.17.21
      lodash-unified: 1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21)
      memoize-one: 6.0.0
      normalize-wheel-es: 1.2.0
      vue: 3.5.13(typescript@5.8.3)
    transitivePeerDependencies:
      - '@vue/composition-api'

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@4.5.0: {}

  env-paths@2.2.1: {}

  environment@1.1.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  errx@0.1.0:
    optional: true

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-module-lexer@0.4.1: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild-code-inspector-plugin@0.20.10:
    dependencies:
      code-inspector-core: 0.20.10
    transitivePeerDependencies:
      - supports-color

  esbuild@0.24.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.24.2
      '@esbuild/android-arm': 0.24.2
      '@esbuild/android-arm64': 0.24.2
      '@esbuild/android-x64': 0.24.2
      '@esbuild/darwin-arm64': 0.24.2
      '@esbuild/darwin-x64': 0.24.2
      '@esbuild/freebsd-arm64': 0.24.2
      '@esbuild/freebsd-x64': 0.24.2
      '@esbuild/linux-arm': 0.24.2
      '@esbuild/linux-arm64': 0.24.2
      '@esbuild/linux-ia32': 0.24.2
      '@esbuild/linux-loong64': 0.24.2
      '@esbuild/linux-mips64el': 0.24.2
      '@esbuild/linux-ppc64': 0.24.2
      '@esbuild/linux-riscv64': 0.24.2
      '@esbuild/linux-s390x': 0.24.2
      '@esbuild/linux-x64': 0.24.2
      '@esbuild/netbsd-arm64': 0.24.2
      '@esbuild/netbsd-x64': 0.24.2
      '@esbuild/openbsd-arm64': 0.24.2
      '@esbuild/openbsd-x64': 0.24.2
      '@esbuild/sunos-x64': 0.24.2
      '@esbuild/win32-arm64': 0.24.2
      '@esbuild/win32-ia32': 0.24.2
      '@esbuild/win32-x64': 0.24.2

  esbuild@0.25.3:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.3
      '@esbuild/android-arm': 0.25.3
      '@esbuild/android-arm64': 0.25.3
      '@esbuild/android-x64': 0.25.3
      '@esbuild/darwin-arm64': 0.25.3
      '@esbuild/darwin-x64': 0.25.3
      '@esbuild/freebsd-arm64': 0.25.3
      '@esbuild/freebsd-x64': 0.25.3
      '@esbuild/linux-arm': 0.25.3
      '@esbuild/linux-arm64': 0.25.3
      '@esbuild/linux-ia32': 0.25.3
      '@esbuild/linux-loong64': 0.25.3
      '@esbuild/linux-mips64el': 0.25.3
      '@esbuild/linux-ppc64': 0.25.3
      '@esbuild/linux-riscv64': 0.25.3
      '@esbuild/linux-s390x': 0.25.3
      '@esbuild/linux-x64': 0.25.3
      '@esbuild/netbsd-arm64': 0.25.3
      '@esbuild/netbsd-x64': 0.25.3
      '@esbuild/openbsd-arm64': 0.25.3
      '@esbuild/openbsd-x64': 0.25.3
      '@esbuild/sunos-x64': 0.25.3
      '@esbuild/win32-arm64': 0.25.3
      '@esbuild/win32-ia32': 0.25.3
      '@esbuild/win32-x64': 0.25.3

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@4.0.0: {}

  escape-string-regexp@5.0.0:
    optional: true

  eslint-config-prettier@10.1.2(eslint@9.25.1(jiti@2.4.2)):
    dependencies:
      eslint: 9.25.1(jiti@2.4.2)

  eslint-plugin-prettier@5.2.6(eslint-config-prettier@10.1.2(eslint@9.25.1(jiti@2.4.2)))(eslint@9.25.1(jiti@2.4.2))(prettier@3.5.3):
    dependencies:
      eslint: 9.25.1(jiti@2.4.2)
      prettier: 3.5.3
      prettier-linter-helpers: 1.0.0
      synckit: 0.11.4
    optionalDependencies:
      eslint-config-prettier: 10.1.2(eslint@9.25.1(jiti@2.4.2))

  eslint-plugin-vue@10.0.1(eslint@9.25.1(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.25.1(jiti@2.4.2))):
    dependencies:
      '@eslint-community/eslint-utils': 4.6.1(eslint@9.25.1(jiti@2.4.2))
      eslint: 9.25.1(jiti@2.4.2)
      natural-compare: 1.4.0
      nth-check: 2.1.1
      postcss-selector-parser: 6.1.2
      semver: 7.7.1
      vue-eslint-parser: 10.1.3(eslint@9.25.1(jiti@2.4.2))
      xml-name-validator: 4.0.0

  eslint-scope@8.3.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@9.25.1(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.6.1(eslint@9.25.1(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.0
      '@eslint/config-helpers': 0.2.1
      '@eslint/core': 0.13.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.25.1
      '@eslint/plugin-kit': 0.2.8
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.2
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      escape-string-regexp: 4.0.0
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 4.2.0

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.7

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  exsolve@1.0.5: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastest-levenshtein@1.0.16: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.4(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  file-entry-cache@10.0.8:
    dependencies:
      flat-cache: 6.1.8

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@7.0.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0
      unicorn-magic: 0.1.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flat-cache@6.1.8:
    dependencies:
      cacheable: 1.8.10
      flatted: 3.3.3
      hookified: 1.8.2

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  framesync@6.1.2:
    dependencies:
      tslib: 2.4.0

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-east-asian-width@1.3.0: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1: {}

  get-tsconfig@4.10.0:
    dependencies:
      resolve-pkg-maps: 1.0.0

  giget@2.0.0:
    dependencies:
      citty: 0.1.6
      consola: 3.4.2
      defu: 6.1.4
      node-fetch-native: 1.6.6
      nypm: 0.6.0
      pathe: 2.0.3
    optional: true

  git-raw-commits@4.0.0:
    dependencies:
      dargs: 8.1.0
      meow: 12.1.1
      split2: 4.2.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@11.0.2:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 4.1.0
      minimatch: 10.0.1
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 2.0.0

  global-directory@4.0.1:
    dependencies:
      ini: 4.1.1

  global-modules@2.0.0:
    dependencies:
      global-prefix: 3.0.0

  global-prefix@3.0.0:
    dependencies:
      ini: 1.3.8
      kind-of: 6.0.3
      which: 1.3.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globjoin@0.1.4: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  gradient-string@3.0.0:
    dependencies:
      chalk: 5.4.1
      tinygradient: 1.1.5

  graphemer@1.4.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hey-listen@1.0.8: {}

  hookable@5.5.3: {}

  hookified@1.8.2: {}

  html-tags@3.3.1: {}

  htmlparser2@8.0.2:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 4.5.0

  human-signals@5.0.0: {}

  husky@9.1.7: {}

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  ignore@7.0.4: {}

  immediate@3.0.6: {}

  immutable@5.1.1: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-from-string@0.0.5:
    dependencies:
      esbuild: 0.24.2
      import-meta-resolve: 4.1.0

  import-meta-resolve@4.1.0: {}

  imurmurhash@0.1.4: {}

  ini@1.3.8: {}

  ini@4.1.1: {}

  is-arrayish@0.2.1: {}

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-plain-object@5.0.0: {}

  is-reference@3.0.3:
    dependencies:
      '@types/estree': 1.0.7

  is-stream@3.0.0: {}

  is-text-path@2.0.0:
    dependencies:
      text-extensions: 2.4.0

  is-what@4.1.16: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isexe@2.0.0: {}

  jackspeak@4.1.0:
    dependencies:
      '@isaacs/cliui': 8.0.2

  jiti@2.4.2: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-tokens@9.0.1: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonparse@1.3.1: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  keyv@5.3.3:
    dependencies:
      '@keyv/serialize': 1.0.3

  kind-of@6.0.3: {}

  klona@2.0.6:
    optional: true

  knitwork@1.2.0:
    optional: true

  known-css-properties@0.35.0: {}

  known-css-properties@0.36.0: {}

  kolorist@1.8.0: {}

  launch-ide@1.0.7:
    dependencies:
      chalk: 4.1.1
      dotenv: 16.5.0

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lie@3.1.1:
    dependencies:
      immediate: 3.0.6

  lightningcss-darwin-arm64@1.29.2:
    optional: true

  lightningcss-darwin-x64@1.29.2:
    optional: true

  lightningcss-freebsd-x64@1.29.2:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.29.2:
    optional: true

  lightningcss-linux-arm64-gnu@1.29.2:
    optional: true

  lightningcss-linux-arm64-musl@1.29.2:
    optional: true

  lightningcss-linux-x64-gnu@1.29.2:
    optional: true

  lightningcss-linux-x64-musl@1.29.2:
    optional: true

  lightningcss-win32-arm64-msvc@1.29.2:
    optional: true

  lightningcss-win32-x64-msvc@1.29.2:
    optional: true

  lightningcss@1.29.2:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.29.2
      lightningcss-darwin-x64: 1.29.2
      lightningcss-freebsd-x64: 1.29.2
      lightningcss-linux-arm-gnueabihf: 1.29.2
      lightningcss-linux-arm64-gnu: 1.29.2
      lightningcss-linux-arm64-musl: 1.29.2
      lightningcss-linux-x64-gnu: 1.29.2
      lightningcss-linux-x64-musl: 1.29.2
      lightningcss-win32-arm64-msvc: 1.29.2
      lightningcss-win32-x64-msvc: 1.29.2

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lint-staged@15.5.1:
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      debug: 4.4.0
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.3.2
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.7.1
    transitivePeerDependencies:
      - supports-color

  listr2@8.3.2:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  local-pkg@1.1.1:
    dependencies:
      mlly: 1.7.4
      pkg-types: 2.1.0
      quansync: 0.2.10

  localforage@1.10.0:
    dependencies:
      lie: 3.1.1

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash-es@4.17.21: {}

  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    dependencies:
      '@types/lodash-es': 4.17.12
      lodash: 4.17.21
      lodash-es: 4.17.21

  lodash.camelcase@4.3.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.snakecase@4.1.1: {}

  lodash.startcase@4.4.0: {}

  lodash.truncate@4.4.2: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  lru-cache@11.1.0: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  math-intrinsics@1.1.0: {}

  mathml-tag-names@2.1.3: {}

  mdn-data@2.0.28: {}

  mdn-data@2.0.30: {}

  mdn-data@2.12.2: {}

  mdn-data@2.21.0: {}

  memoize-one@6.0.0: {}

  meow@12.1.1: {}

  meow@13.2.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  minimatch@10.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  mitt@3.0.1: {}

  mlly@1.7.4:
    dependencies:
      acorn: 8.14.1
      pathe: 2.0.3
      pkg-types: 1.3.1
      ufo: 1.6.1

  ms@2.1.3: {}

  muggle-string@0.4.1: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  node-addon-api@7.1.1:
    optional: true

  node-fetch-native@1.6.6:
    optional: true

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  normalize-wheel-es@1.2.0: {}

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nprogress@0.2.0: {}

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nypm@0.6.0:
    dependencies:
      citty: 0.1.6
      consola: 3.4.2
      pathe: 2.0.3
      pkg-types: 2.1.0
      tinyexec: 0.3.2
    optional: true

  object-inspect@1.13.4: {}

  ohash@2.0.11:
    optional: true

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  package-json-from-dist@1.0.1: {}

  package-manager-detector@0.2.11:
    dependencies:
      quansync: 0.2.10

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-scurry@2.0.0:
    dependencies:
      lru-cache: 11.1.0
      minipass: 7.1.2

  path-to-regexp@8.2.0: {}

  path-type@4.0.0: {}

  pathe@1.1.2: {}

  pathe@2.0.3: {}

  perfect-debounce@1.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.6.0: {}

  pinia@3.0.2(typescript@5.8.3)(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      '@vue/devtools-api': 7.7.6
      vue: 3.5.13(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3

  pinyin-pro@3.26.0: {}

  pkg-types@1.3.1:
    dependencies:
      confbox: 0.1.8
      mlly: 1.7.4
      pathe: 2.0.3

  pkg-types@2.1.0:
    dependencies:
      confbox: 0.2.2
      exsolve: 1.0.5
      pathe: 2.0.3

  popmotion@11.0.5:
    dependencies:
      framesync: 6.1.2
      hey-listen: 1.0.8
      style-value-types: 5.1.2
      tslib: 2.4.0

  portfinder@1.0.37:
    dependencies:
      async: 3.2.6
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  postcss-calc@10.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  postcss-colormin@7.0.2(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-convert-values@7.0.4(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-discard-comments@7.0.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-discard-duplicates@7.0.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-discard-empty@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-discard-overridden@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-html@1.8.0:
    dependencies:
      htmlparser2: 8.0.2
      js-tokens: 9.0.1
      postcss: 8.5.3
      postcss-safe-parser: 6.0.0(postcss@8.5.3)

  postcss-load-config@6.0.1(jiti@2.4.2)(postcss@8.5.3)(yaml@2.7.1):
    dependencies:
      lilconfig: 3.1.3
    optionalDependencies:
      jiti: 2.4.2
      postcss: 8.5.3
      yaml: 2.7.1

  postcss-media-query-parser@0.2.3: {}

  postcss-merge-longhand@7.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      stylehacks: 7.0.4(postcss@8.5.3)

  postcss-merge-rules@7.0.4(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      cssnano-utils: 5.0.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-minify-font-values@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-gradients@7.0.0(postcss@8.5.3):
    dependencies:
      colord: 2.9.3
      cssnano-utils: 5.0.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-params@7.0.2(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      cssnano-utils: 5.0.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-selectors@7.0.4(postcss@8.5.3):
    dependencies:
      cssesc: 3.0.0
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-normalize-charset@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-normalize-display-values@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-positions@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-repeat-style@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-string@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-timing-functions@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-unicode@7.0.2(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-url@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-whitespace@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-ordered-values@7.0.1(postcss@8.5.3):
    dependencies:
      cssnano-utils: 5.0.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-reduce-initial@7.0.2(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      postcss: 8.5.3

  postcss-reduce-transforms@7.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-resolve-nested-selector@0.1.6: {}

  postcss-safe-parser@6.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-safe-parser@7.0.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-scss@4.0.9(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-sorting@8.0.2(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-svgo@7.0.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      svgo: 3.3.2

  postcss-unique-selectors@7.0.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-value-parser@4.2.0: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.5.3: {}

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  quansync@0.2.10: {}

  queue-microtask@1.2.3: {}

  rc9@2.1.2:
    dependencies:
      defu: 6.1.4
      destr: 2.0.5
    optional: true

  readdirp@4.1.2: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  responsive-storage@2.2.0: {}

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rimraf@6.0.1:
    dependencies:
      glob: 11.0.2
      package-json-from-dist: 1.0.1

  rollup-plugin-external-globals@0.10.0(rollup@4.40.1):
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.40.1)
      estree-walker: 3.0.3
      is-reference: 3.0.3
      magic-string: 0.30.17
      rollup: 4.40.1

  rollup-plugin-visualizer@5.14.0(rollup@4.40.1):
    dependencies:
      open: 8.4.2
      picomatch: 4.0.2
      source-map: 0.7.4
      yargs: 17.7.2
    optionalDependencies:
      rollup: 4.40.1

  rollup@4.40.1:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.40.1
      '@rollup/rollup-android-arm64': 4.40.1
      '@rollup/rollup-darwin-arm64': 4.40.1
      '@rollup/rollup-darwin-x64': 4.40.1
      '@rollup/rollup-freebsd-arm64': 4.40.1
      '@rollup/rollup-freebsd-x64': 4.40.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.40.1
      '@rollup/rollup-linux-arm-musleabihf': 4.40.1
      '@rollup/rollup-linux-arm64-gnu': 4.40.1
      '@rollup/rollup-linux-arm64-musl': 4.40.1
      '@rollup/rollup-linux-loongarch64-gnu': 4.40.1
      '@rollup/rollup-linux-powerpc64le-gnu': 4.40.1
      '@rollup/rollup-linux-riscv64-gnu': 4.40.1
      '@rollup/rollup-linux-riscv64-musl': 4.40.1
      '@rollup/rollup-linux-s390x-gnu': 4.40.1
      '@rollup/rollup-linux-x64-gnu': 4.40.1
      '@rollup/rollup-linux-x64-musl': 4.40.1
      '@rollup/rollup-win32-arm64-msvc': 4.40.1
      '@rollup/rollup-win32-ia32-msvc': 4.40.1
      '@rollup/rollup-win32-x64-msvc': 4.40.1
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  sass@1.87.0:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.1
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1

  scule@1.3.0:
    optional: true

  semver@6.3.1: {}

  semver@7.7.1: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@4.1.0: {}

  slash@3.0.0: {}

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  sortablejs@1.15.6: {}

  source-map-js@1.2.1: {}

  source-map@0.7.4: {}

  sourcemap-codec@1.4.8: {}

  speakingurl@14.0.1: {}

  split2@4.2.0: {}

  std-env@3.9.0:
    optional: true

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-final-newline@3.0.0: {}

  strip-json-comments@3.1.1: {}

  strip-literal@3.0.0:
    dependencies:
      js-tokens: 9.0.1
    optional: true

  style-value-types@5.1.2:
    dependencies:
      hey-listen: 1.0.8
      tslib: 2.4.0

  stylehacks@7.0.4(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  stylelint-config-html@1.1.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      postcss-html: 1.8.0
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint-config-recess-order@6.0.0(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      stylelint: 16.19.1(typescript@5.8.3)
      stylelint-order: 6.0.4(stylelint@16.19.1(typescript@5.8.3))

  stylelint-config-recommended-scss@14.1.0(postcss@8.5.3)(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      postcss-scss: 4.0.9(postcss@8.5.3)
      stylelint: 16.19.1(typescript@5.8.3)
      stylelint-config-recommended: 14.0.1(stylelint@16.19.1(typescript@5.8.3))
      stylelint-scss: 6.11.1(stylelint@16.19.1(typescript@5.8.3))
    optionalDependencies:
      postcss: 8.5.3

  stylelint-config-recommended-vue@1.6.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      postcss-html: 1.8.0
      semver: 7.7.1
      stylelint: 16.19.1(typescript@5.8.3)
      stylelint-config-html: 1.1.0(postcss-html@1.8.0)(stylelint@16.19.1(typescript@5.8.3))
      stylelint-config-recommended: 16.0.0(stylelint@16.19.1(typescript@5.8.3))

  stylelint-config-recommended@14.0.1(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint-config-recommended@16.0.0(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint-config-standard-scss@14.0.0(postcss@8.5.3)(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      stylelint: 16.19.1(typescript@5.8.3)
      stylelint-config-recommended-scss: 14.1.0(postcss@8.5.3)(stylelint@16.19.1(typescript@5.8.3))
      stylelint-config-standard: 36.0.1(stylelint@16.19.1(typescript@5.8.3))
    optionalDependencies:
      postcss: 8.5.3

  stylelint-config-standard@36.0.1(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      stylelint: 16.19.1(typescript@5.8.3)
      stylelint-config-recommended: 14.0.1(stylelint@16.19.1(typescript@5.8.3))

  stylelint-order@6.0.4(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      postcss: 8.5.3
      postcss-sorting: 8.0.2(postcss@8.5.3)
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint-prettier@5.0.3(prettier@3.5.3)(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      prettier: 3.5.3
      prettier-linter-helpers: 1.0.0
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint-scss@6.11.1(stylelint@16.19.1(typescript@5.8.3)):
    dependencies:
      css-tree: 3.1.0
      is-plain-object: 5.0.0
      known-css-properties: 0.35.0
      mdn-data: 2.21.0
      postcss-media-query-parser: 0.2.3
      postcss-resolve-nested-selector: 0.1.6
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0
      stylelint: 16.19.1(typescript@5.8.3)

  stylelint@16.19.1(typescript@5.8.3):
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3
      '@csstools/media-query-list-parser': 4.0.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/selector-specificity': 5.0.0(postcss-selector-parser@7.1.0)
      '@dual-bundle/import-meta-resolve': 4.1.0
      balanced-match: 2.0.0
      colord: 2.9.3
      cosmiconfig: 9.0.0(typescript@5.8.3)
      css-functions-list: 3.2.3
      css-tree: 3.1.0
      debug: 4.4.0
      fast-glob: 3.3.3
      fastest-levenshtein: 1.0.16
      file-entry-cache: 10.0.8
      global-modules: 2.0.0
      globby: 11.1.0
      globjoin: 0.1.4
      html-tags: 3.3.1
      ignore: 7.0.4
      imurmurhash: 0.1.4
      is-plain-object: 5.0.0
      known-css-properties: 0.36.0
      mathml-tag-names: 2.1.3
      meow: 13.2.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-resolve-nested-selector: 0.1.6
      postcss-safe-parser: 7.0.1(postcss@8.5.3)
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0
      resolve-from: 5.0.0
      string-width: 4.2.3
      supports-hyperlinks: 3.2.0
      svg-tags: 1.0.0
      table: 6.9.0
      write-file-atomic: 5.0.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  superjson@2.2.2:
    dependencies:
      copy-anything: 3.0.5

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@3.2.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  svg-tags@1.0.0: {}

  svgo@3.3.2:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.1.1

  synckit@0.11.4:
    dependencies:
      '@pkgr/core': 0.2.4
      tslib: 2.8.1

  table@6.9.0:
    dependencies:
      ajv: 8.17.1
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  tailwindcss@4.1.4: {}

  tapable@2.2.1: {}

  text-extensions@2.4.0: {}

  through@2.3.8: {}

  tinycolor2@1.6.0: {}

  tinyexec@0.3.2: {}

  tinyglobby@0.2.13:
    dependencies:
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2

  tinygradient@1.1.5:
    dependencies:
      '@types/tinycolor2': 1.4.6
      tinycolor2: 1.6.0

  tippy.js@6.3.7:
    dependencies:
      '@popperjs/core': 2.11.8

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  tslib@2.3.0: {}

  tslib@2.4.0: {}

  tslib@2.8.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@4.40.1: {}

  typescript-eslint@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.31.1(@typescript-eslint/parser@8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3))(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/parser': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.31.1(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.25.1(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  ufo@1.6.1: {}

  unctx@2.4.1:
    dependencies:
      acorn: 8.14.1
      estree-walker: 3.0.3
      magic-string: 0.30.17
      unplugin: 2.3.2
    optional: true

  undici-types@6.19.8: {}

  unicorn-magic@0.1.0: {}

  unimport@5.0.0:
    dependencies:
      acorn: 8.14.1
      escape-string-regexp: 5.0.0
      estree-walker: 3.0.3
      local-pkg: 1.1.1
      magic-string: 0.30.17
      mlly: 1.7.4
      pathe: 2.0.3
      picomatch: 4.0.2
      pkg-types: 2.1.0
      scule: 1.3.0
      strip-literal: 3.0.0
      tinyglobby: 0.2.13
      unplugin: 2.3.2
      unplugin-utils: 0.2.4
    optional: true

  universalify@2.0.1: {}

  unplugin-icons@22.1.0(@vue/compiler-sfc@3.5.13):
    dependencies:
      '@antfu/install-pkg': 1.0.0
      '@iconify/utils': 2.3.0
      debug: 4.4.0
      local-pkg: 1.1.1
      unplugin: 2.3.2
    optionalDependencies:
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  unplugin-utils@0.2.4:
    dependencies:
      pathe: 2.0.3
      picomatch: 4.0.2
    optional: true

  unplugin@2.3.2:
    dependencies:
      acorn: 8.14.1
      picomatch: 4.0.2
      webpack-virtual-modules: 0.6.2

  untyped@2.0.0:
    dependencies:
      citty: 0.1.6
      defu: 6.1.4
      jiti: 2.4.2
      knitwork: 1.2.0
      scule: 1.3.0
    optional: true

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  vite-code-inspector-plugin@0.20.10:
    dependencies:
      code-inspector-core: 0.20.10
    transitivePeerDependencies:
      - supports-color

  vite-plugin-cdn-import@1.0.1(rollup@4.40.1)(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)):
    dependencies:
      rollup-plugin-external-globals: 0.10.0(rollup@4.40.1)
      vite-plugin-externals: 0.6.2(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1))
    transitivePeerDependencies:
      - rollup
      - vite

  vite-plugin-compression@0.5.1(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)):
    dependencies:
      chalk: 4.1.2
      debug: 4.4.0
      fs-extra: 10.1.0
      vite: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)
    transitivePeerDependencies:
      - supports-color

  vite-plugin-externals@0.6.2(vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)):
    dependencies:
      acorn: 8.14.1
      es-module-lexer: 0.4.1
      fs-extra: 10.1.0
      magic-string: 0.25.9
      vite: 6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)

  vite-plugin-fake-server@2.2.0:
    dependencies:
      bundle-import: 0.0.2
      chokidar: 4.0.3
      path-to-regexp: 8.2.0
      picocolors: 1.1.1
      tinyglobby: 0.2.13

  vite-plugin-remove-console@2.2.0: {}

  vite-plugin-router-warn@1.0.0: {}

  vite-svg-loader@5.1.0(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      svgo: 3.3.2
      vue: 3.5.13(typescript@5.8.3)

  vite@6.3.4(@types/node@20.17.32)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1):
    dependencies:
      esbuild: 0.25.3
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.3
      rollup: 4.40.1
      tinyglobby: 0.2.13
    optionalDependencies:
      '@types/node': 20.17.32
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.29.2
      sass: 1.87.0
      yaml: 2.7.1

  vscode-uri@3.1.0: {}

  vue-demi@0.14.10(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      vue: 3.5.13(typescript@5.8.3)

  vue-eslint-parser@10.1.3(eslint@9.25.1(jiti@2.4.2)):
    dependencies:
      debug: 4.4.0
      eslint: 9.25.1(jiti@2.4.2)
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      lodash: 4.17.21
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  vue-router@4.5.1(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.8.3)

  vue-tippy@6.7.0(vue@3.5.13(typescript@5.8.3)):
    dependencies:
      tippy.js: 6.3.7
      vue: 3.5.13(typescript@5.8.3)

  vue-tsc@2.2.10(typescript@5.8.3):
    dependencies:
      '@volar/typescript': 2.4.13
      '@vue/language-core': 2.2.10(typescript@5.8.3)
      typescript: 5.8.3

  vue-types@6.0.0(vue@3.5.13(typescript@5.8.3)):
    optionalDependencies:
      vue: 3.5.13(typescript@5.8.3)

  vue@3.5.13(typescript@5.8.3):
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-sfc': 3.5.13
      '@vue/runtime-dom': 3.5.13
      '@vue/server-renderer': 3.5.13(vue@3.5.13(typescript@5.8.3))
      '@vue/shared': 3.5.13
    optionalDependencies:
      typescript: 5.8.3

  webpack-code-inspector-plugin@0.20.10:
    dependencies:
      code-inspector-core: 0.20.10
    transitivePeerDependencies:
      - supports-color

  webpack-virtual-modules@0.6.2: {}

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  widest-line@5.0.0:
    dependencies:
      string-width: 7.2.0

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  write-file-atomic@5.0.1:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 4.1.0

  xml-name-validator@4.0.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@2.7.1: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}

  zrender@5.6.1:
    dependencies:
      tslib: 2.3.0
