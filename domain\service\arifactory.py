# coding=utf-8
'''
Created on 2025年3月27日

@author: 10222665
'''
import base64
import json
import logging
import os
import re
import time
from collections import deque
import requests
from datetime import datetime


def retries_on_exception(maxTries, hook=None, hookArg=None, hookGrainSize=1, exceptions=(Exception,),
                         logExceptions=False, everyTryDelaySecs=0):
    def dec(func):
        def f2(*args, **kwargs):
            hookGrainSizeInit = hookGrainSize
            tries = maxTries
            for triesRemaining in range(tries, 0, -1):  # Python 3 range is an iterator
                hookGrainSizeInit -= 1
                try:
                    return func(*args, **kwargs)
                except exceptions as exceptionMsg:  # Python 3 syntax for catching exceptions
                    if logExceptions:
                        logging.warning(f"Exception occurred: {exceptionMsg}")
                    if triesRemaining > 1:  # Don't call hook on the last try
                        if hookGrainSizeInit == 0:
                            hookGrainSizeInit = hookGrainSize
                            if hook is not None:
                                if hookArg is not None:
                                    hook(hookArg)
                                else:
                                    hook()
                    else:
                        logging.error(f'Tried {maxTries} times, but execution of {func.__name__} still failed')
                        raise
                    time.sleep(everyTryDelaySecs)
                else:
                    break

        return f2

    return dec


class ArtifactDao(object):

    def __init__(self, baseUrl):
        self._baseUrl = baseUrl
        self._cookies = None
        self._auth = None
        self._authorization = None

    def get_request(self, **kwargs):
        # raise NotImplementedError('Need implement get_method!')
        return requests.get(cookies=self._cookies, **kwargs)

    def post_request(self, **kwargs):
        # raise NotImplementedError('Need implement post_method!')
        return requests.post(cookies=self._cookies, **kwargs)

    def delete_request(self, **kwargs):
        # raise NotImplementedError('Need implement delete_method!')
        return requests.delete(cookies=self._cookies, **kwargs)

    def login(self, userName, password):
        self._generate_authorization(userName, password)
        loginUrl = "/ui/auth/login?_spring_security_remember_me=false"
        requestParams = {"user": str(userName), "password": str(password), "type": "login"}
        response = requests.post(url=self._baseUrl + loginUrl, json=requestParams, verify=True)
        if response.status_code != 200:
            logging.error("request login url fail, {}".format(response.text))
            return False
        self._cookies = response.cookies
        if self._cookies is not None:
            return True
        else:
            logging.error("SESSION is None,get cookies fail")
            return False

    import base64

    def _generate_authorization(self, userName, password):
        # 将用户名和密码连接为一个字符串，然后将其编码为字节串，再进行 base64 编码
        base64Encode = base64.b64encode(f"{userName}:{password}".encode('utf-8'))

        # 将 base64 编码结果转回字符串以便拼接
        self._authorization = "Basic " + base64Encode.decode('utf-8')

    def copy(self, sourceRepo, targetRepo):
        copyUrl = "/api/copy/{0}?to=/{1}".format(sourceRepo.replace(' ', '%20'), targetRepo.replace(' ', '%20'))
        respones = self.post_request(url=self._baseUrl + copyUrl, verify=True)
        if respones.status_code == 200:
            return respones.text
        else:
            logging.error("copy Server return not 200, message={0}".format(respones.text))
            return None

    def delete(self, targetRepo):
        delUrl = "/{0}".format(targetRepo.replace(' ', '%20'))
        respones = self.delete_request(url=self._baseUrl + delUrl, verify=True)
        logging.info(respones.status_code)
        logging.info(respones.text)

    def folderInfo(self, targetFolder):
        folderURL = "/api/storage/%s" % (targetFolder.replace(' ', '%20'))
        response = self.get_request(url=self._baseUrl + folderURL, verify=True)
        if response.status_code == 200:
            return json.loads(response.text)
        else:
            logging.error("folderInfo Server return not 200, message={0}".format(response.text))
            return {}

    @retries_on_exception(maxTries=3)
    def download(self, artifactoryPath, localfilePath):
        downloadUrl = "/api/download/{}".format(artifactoryPath)
        response = self.get_request(url=self._baseUrl + downloadUrl, stream=True, verify=True)
        if response.headers.get('Content-Length', None) is None:
            raise Exception('open path failed: ' + artifactoryPath)
        try:
            with open(localfilePath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=512):
                    f.write(chunk)
            if int(response.headers.get('Content-Length')) != os.path.getsize(localfilePath):
                raise Exception('file dowload error: ' + artifactoryPath)
        except Exception:
            raise Exception('open path failed: ' + localfilePath)

    def download_files(self, remotePath, localPath):
        foldInfo = self.folderInfo(remotePath).get('children', [])
        downloadFiles = []
        for fileInfo in foldInfo:
            if fileInfo['folder']:
                if not os.path.exists(localPath + fileInfo['uri']):
                    os.makedirs(localPath + fileInfo['uri'])
                subDownloadFiles = self.download_files(remotePath + fileInfo['uri'], localPath + fileInfo['uri'])
                downloadFiles.extend(subDownloadFiles)
            else:
                self.download(remotePath + fileInfo['uri'], localPath + fileInfo['uri'])
                downloadFiles.append(fileInfo['uri'].lstrip('/'))
        return downloadFiles

    @retries_on_exception(maxTries=3)
    def download_folder(self, artifactoryPath, localfilePath):
        downloadUrl = "/api/archive/download/{}?archiveType=tar".format(artifactoryPath)
        response = self.get_request(url=self._baseUrl + downloadUrl, stream=True, verify=True)
        try:
            with open(localfilePath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=512):
                    f.write(chunk)
            if not os.path.getsize(localfilePath):
                raise Exception('file dowload error: ' + artifactoryPath)
        except Exception:
            raise Exception('open path failed: ' + localfilePath)

    def get_lastest_folder(self, parentUrl, timeFormat='%Y-%m-%dT%H:%M:%S.%f+08:00', regular=''):
        urlList = self.folderInfo(parentUrl).get('children')
        lastModified, folder = 0, None
        for urlInfo in urlList:
            if not self._is_folder(urlInfo) or not re.search(regular, urlInfo['uri'], re.I):
                continue
            unconcernedVersion = ["-CT", "_AT_", "RFS_TEST"]
            for item in unconcernedVersion:
                if item in urlInfo['uri']:
                    continue
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            if foldCreateDate is None:
                continue
            timeStamp = time.mktime(time.strptime(foldCreateDate, timeFormat))
            if timeStamp - lastModified > 0:
                lastModified = timeStamp
                folder = urlInfo['uri'].replace('/', '')
        return folder

    def get_peer_folder_by_last_create_time(self, currentUrl, timeFormat='%Y-%m-%dT%H:%M:%S.%f+08:00'):
        parentUrl = currentUrl[:currentUrl.rindex('/')]
        urlList = self.folderInfo(parentUrl).get('children')
        currentTime = time.mktime(time.strptime(self.folderInfo(currentUrl).get('created'), timeFormat))
        lastTime, folder = 0, None
        for urlInfo in urlList:
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            if foldCreateDate is None:
                continue
            timeStamp = time.mktime(time.strptime(foldCreateDate, timeFormat))
            if timeStamp - lastTime > 0 and timeStamp < currentTime:
                lastTime = timeStamp
                folder = urlInfo['uri'].replace('/', '')
        return folder

    def get_lastest_folder_by_folder_name(self, parentUrl):
        urlList = self.folderInfo(parentUrl).get('children')
        latestTime, folder = '', None
        for urlInfo in urlList:
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            folderName = urlInfo['uri'].replace('/', '')
            folderTime = foldCreateDate[:4] + folderName[-10:]
            if folderTime > latestTime:
                latestTime = folderTime
                folder = urlInfo['uri'].replace('/', '')
        return folder

    def get_peer_folder_by_last_folder_name(self, currentUrl):
        parentUrl = currentUrl[:currentUrl.rindex('/')]
        urlList = self.folderInfo(parentUrl).get('children')
        currentTime = self.folderInfo(currentUrl).get('created')[:4] + currentUrl[-10:]
        lastTime, folder = '', None
        for urlInfo in urlList:
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            folderName = urlInfo['uri'].replace('/', '')
            folderTime = foldCreateDate[:4] + folderName[-10:]
            # if cmp(folderTime, currentTime) < 0 and cmp(folderTime, lastTime) > 0:
            if folderTime < currentTime and folderTime > lastTime:
                lastTime = folderTime
                folder = urlInfo['uri'].replace('/', '')
        return folder

    def get_peer_folder_by_last_folder_name_without_underline(self, currentUrl):
        parentUrl = currentUrl[:currentUrl.rindex('/')]
        urlList = self.folderInfo(parentUrl).get('children')
        currentTime = currentUrl[-14:]
        lastTime, folder = '', None
        for urlInfo in urlList:
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            folderName = urlInfo['uri'].replace('/', '')
            folderTime = folderName[-14:]
            # if cmp(folderTime, currentTime) < 0 and cmp(folderTime, lastTime) > 0:
            if folderTime < currentTime and folderTime > lastTime:
                lastTime = folderTime

                folder = urlInfo['uri'].replace('/', '')
        return folder

    def get_new_last_modified_folder(self, parentUrl, timeFormat='%Y-%m-%dT%H:%M:%S.%f+08:00', regular=''):
        urlList = self.folderInfo(parentUrl).get('children')
        lastModified = 0
        record = None
        for urlInfo in urlList:
            if not self._is_folder(urlInfo) or not re.search(regular, urlInfo['uri']) or re.search('DOC',
                                                                                                   urlInfo['uri']):
                continue
            foldCreateDate = self.folderInfo(parentUrl + urlInfo['uri']).get('created')
            if foldCreateDate is None:
                continue
            timeStamp = time.mktime(time.strptime(foldCreateDate, timeFormat))
            if timeStamp - lastModified > 0:
                lastModified = timeStamp
                record = urlInfo['uri']
        return record

    def get_children_file_in_path(self, parentUrl):
        urlList = self.folderInfo(parentUrl).get('children')
        return urlList

    def get_new_children_file_in_path(self, parentUrl):
        tempUrlList = self.folderInfo(parentUrl).get('children')
        urlList = tempUrlList[1].get('uri')
        return urlList

    def _is_folder(self, urlInfo):
        return urlInfo.get('folder', False)

    def general_folder_tree(self, parentUrl, parentUri):
        folder_list = self.get_children_file_in_path(parentUrl)
        child = []
        for folder in folder_list:
            if folder.get('folder'):
                child.extend(self.general_folder_tree(parentUrl + folder.get('uri'), folder.get('uri')))
            else:
                # result[parentUri].update({folder.get('uri'): {}})
                child.append(dict(
                    title=folder.get('uri').lstrip('/'),
                    key=folder.get('uri').lstrip('/'),
                    children=list()
                ))
        result = [dict(
            title=parentUri.lstrip('/'),
            key=parentUri.lstrip('/'),
            children=child
        )]
        return result

    def get_all_branch_version_list(self, base_url, branch):
        query_url = "/01-gNB/litepass/PKG"
        result = self.folderInfo(base_url + "/" + branch)
        version_list = []
        folder_list = result.get("children")
        # if "release" in base_url:
        for folder in folder_list:
            if folder.get("folder", False) == True:
                child_folder_list = self.folderInfo(base_url + "/" + branch + folder.get("uri", ""))
                print(child_folder_list.get("uri"))
                for elem in child_folder_list.get("children"):
                    if elem.get("folder", False) == True:
                        path = child_folder_list.get("uri")+"/"+elem.get("uri", "").lstrip("/")
                        # 替换URL前缀
                        if path.startswith('https://artsz.zte.com.cn:443/artifactory/api/storage/'):
                            path = path.replace(
                                'https://artsz.zte.com.cn:443/artifactory/api/storage/',
                                'https://artsz.zte.com.cn/artifactory/webapp/#/artifacts/browse/tree/General/'
                            )
                        version_list.append({elem.get("uri", "").lstrip("/"): path})
        # else:
        #     for folder in folder_list:
        #         version_list.append(folder.get("uri", "").lstrip("/"))
        return version_list

    def sort_versions(self, version_list):
        def get_timestamp(version):
            # 获取时间戳部分
            timestamp = version.split('_')[1]
            # 转换为 datetime 对象
            return datetime.strptime(timestamp, "%d%m%y%H%M")

        # 按时间戳排序
        return sorted(version_list, key=get_timestamp)

    def get_versions_between(self, version_list, version1, version2):
        """
        获取两个版本之间的所有版本，保留版本号和完整路径的映射关系
        
        Args:
            version_list: 包含版本号和路径的字典列表，如 [{'version1': 'path1'}, {'version2': 'path2'}]
            version1: 起始版本号
            version2: 结束版本号
        
        Returns:
            List[Dict]: 两个版本之间的所有版本，包含版本号和路径
        """
        # 提取版本号列表和版本号到路径的映射
        version_to_path = {}
        versions = []
        
        for version_dict in version_list:
            for version, path in version_dict.items():
                version_to_path[version] = path
                versions.append(version)
        
        # 按时间戳排序版本
        def get_timestamp(version):
            # 获取时间戳部分
            timestamp = version.split('_')[1]
            # 转换为 datetime 对象
            return datetime.strptime(timestamp, "%y%m%d%H%M")
        
        sorted_versions = sorted(versions, key=get_timestamp)
        
        # 找到两个版本的索引
        start_index = None
        end_index = None
        
        # 如果version1为空，则从列表开始
        if not version1:
            start_index = 0
        else:
            for i, version in enumerate(sorted_versions):
                if version1 in version:
                    start_index = i
                    break
        
        # 查找version2的索引
        for i, version in enumerate(sorted_versions):
            if version2 in version:
                end_index = i
                break
        
        # 如果找到了两个版本的索引
        if start_index is not None and end_index is not None:
            # 确保 start_index 小于 end_index
            if start_index > end_index:
                start_index, end_index = end_index, start_index
            
            # 获取两个版本之间的所有版本，并保留路径信息
            result = []
            for version in sorted_versions[start_index:end_index + 1]:
                result.append({version: version_to_path[version]})
            
            return result
        
        # 如果没有找到两个版本，返回空列表
        return []

    def get_last_success_version(self,base_uri, branch):
        try:
            if "release" not in base_uri:
                last_branch = self.get_lastest_folder(base_uri)
                last_forder = self.get_lastest_folder_by_folder_name(base_uri + "/" + last_branch)
                return last_forder
            else:
                last_branch = self.get_lastest_folder(base_uri+"/"+branch)
                res = self.get_lastest_folder_by_folder_name(base_uri+"/"+branch+"/"+last_branch)
                return res
        except:
            return ""
if __name__ == "__main__":
    art = ArtifactDao('https://artsz.zte.com.cn/artifactory')
    print(art.login("g5_nr_v2-ci","f9K0De_4"))
    # print(art.login("5g-hfc-ci", "G5.zte-H.FC"))
    # base_url = "g5nrv3-snapshot-generic/aurora_test/NFMerged/release"
    # branch = "V4.20.20.20_Bugfix"
    # result = (art.get_children_file_in_path('g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Bugfix/NR-V4.20.20.20R11'))
    # [print(elem) for elem in result]
    # version_list = art.get_all_branch_version_list(base_url,branch)
    # bew_list = art.get_versions_between(version_list,"NR-V4.20.20.20R06_2503201258","NR-V4.20.20.20R08_2503232054")
    # print(bew_list)
    # base_url = "g5nrv3-alpha-generic/Daily-Version/release"
    # branch = "V4.20.20.20_Main"
    # last_version = art.get_lastest_folder("g5nrv3-alpha-generic/Daily-Version/release")
    # print(last_version)
    # res = art.get_last_success_version('g5nrv3-alpha-generic/Daily-Version/release',"V4.20.20.20_Main")
    res1 = art.get_last_success_version('g5nrv3-alpha-generic/Daily-Version/master', "NR-V4.30.10.00B209-3")
    # res = art.get_last_success_version1("https://artsz.zte.com.cn/artifactory/g5nrv3-snapshot-generic/aurora_test/NFMerged/release/V4.20.20.20_Main/NR-V4.20.20.20MainR119/NR-V4.20.20.20MainR119_2504201842")
    # print(res)
    res = art.get_all_branch_version_list("g5nrv3-snapshot-generic/aurora_test/NFMerged","master")
    res2 = art.get_all_branch_version_list("g5nrv3-snapshot-generic/aurora_test/NFMerged/release","V4.20.20.20_Main")
    print(res1)
    print(res)
    res3 = art.get_versions_between(res,"NR-V4.30.10.00B209-2_2505201524",res1)
    print(res2)
