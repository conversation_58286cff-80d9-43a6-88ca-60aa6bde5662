from domain.service.tools.ume_state_service import *
from datetime import datetime


class UmeStateCheck(object):

    @staticmethod
    def ume_alarm_check(data, me_id, target_date):
        reds = [198092296,200201009,1014]
        yellows = [200201001,198097060,200201067,198097605]
        flag = True
        task_info = {}
        try:
            # 获取taskInfo中的告警信息
            task_info = data.get("output").get("data").get("taskInfo").get(me_id)
            if not "alarmcode" in task_info:
                return {"result": flag, "details": task_info.get("codename", {})}

            # 获取当前存在的所有告警
            alarms = task_info.get("alarmcode")
            # print(alarms)
            # 检查交集是否为空,判断是否存在红色严重告警
            set_reds = set(reds)
            set_alarms = set(alarms)
            if set_reds & set_alarms:
                flag = False
            # 判断是否存在升级版本后新增的黄色主要告警
            alarmstime = data.get("output").get("data").get("taskInfo").get(me_id).get("alarmraisedtime")
            indices = [i for i, time_str in enumerate(alarmstime) if datetime.fromisoformat(time_str) > target_date]
            newalarms = [alarms[i] for i in indices]
            # print(newalarms)
            # print(task_info.get("codename", {}))
            # 检查交集是否为空,判断是否存在新增黄色主要告警
            set_yellows = set(yellows)
            set_newalarms = set(newalarms)
            if set_yellows & set_newalarms:
                flag = False
            return {"result": flag, "details": task_info.get("codename", {})}
        except Exception as e:
            return {"result": False, "details": {e}}
        
    @staticmethod
    def me_connect_check(data, me_id):
        flag = True
        task_info = {}
        try:
            # 获取taskInfo中的告警信息
            task_info = data.get("output").get("data").get("taskInfo").get(me_id)
            if not "alarmcode" in task_info:
                return {"result": flag, "details": task_info}

            # 获取当前存在的所有告警
            alarms = task_info.get("alarmcode")
            # print(alarms)

            if 1014 in alarms:
                flag = False

            return {"result": flag, "details": task_info.get("codename", {})}
        except Exception as e:
            return {"result": False, "details": task_info.get("codename", {})}

    @staticmethod
    def mts_num_check(data):
        return UmeInfoService.get_mts_num(data)