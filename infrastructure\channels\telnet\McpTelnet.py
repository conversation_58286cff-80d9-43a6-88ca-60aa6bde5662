# coding=utf-8
'''
Created on 20151028

@author: 10154402
'''
import re
import socket

from infrastructure.channels.telnet.Telnet import Telnet


class McpTelnet(Telnet):

    def disconnect(self, *args):
        if self._tn is not None:
            try:
                self._tn.write(b"_exit\n")
                self._tn.write(b"exit\n")
            except AttributeError:
                pass
            except socket.error:
                pass
            else:
                self._tn.close()
