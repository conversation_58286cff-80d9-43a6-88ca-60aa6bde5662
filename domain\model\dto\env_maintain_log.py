from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Any


class EnvMaintainLog(BaseModel):
    """
    Environment maintenance log model
    """
    log_id: str = Field(default_factory=lambda: datetime.now().isoformat(), frozen=True)
    env_id: str = Field(..., description="Environment ID")
    env_type: str = Field(..., description="Environment type (jenkins_server_env, pc_env, biz_core_env, version_env, device_env)")
    operation: str = Field(..., description="Operation type (create, update, delete)")
    operator: str = Field(..., description="User who performed the operation")
    operation_time: str = Field(default_factory=lambda: datetime.now().isoformat(), frozen=True)
    details: Optional[dict] = Field(default=None, description="Operation details")
    
    class Config:
        schema_extra = {
            "example": {
                "env_id": "RAN3-T17-10466",
                "env_type": "jenkins_server_env",
                "operation": "create",
                "operator": "user123",
                "details": {
                    "before": None,
                    "after": {
                        "env_id": "RAN3-T17-10466",
                        "test_domain": "example.com",
                        "jenkins_url": "http://jenkins.example.com",
                        "jenkins_username": "admin",
                        "jenkins_password": "password"
                    }
                }
            }
        }
